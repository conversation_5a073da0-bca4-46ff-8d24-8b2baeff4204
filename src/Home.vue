<template>
    <div id="app">
        <router-view></router-view>
    </div>
</template>

<script>
    import { AppEvents } from '@/core/index.js';
    import { logoutConfirm } from 'views/common/login-optimize.js';

    export default {
        name: 'Home',
        created() {
            this.$app.on(AppEvents.APP_EVENT_UN_AUTH, (msg) => {
                logoutConfirm(msg);
            });
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
    @import 'styles/index.scss';
</style>
