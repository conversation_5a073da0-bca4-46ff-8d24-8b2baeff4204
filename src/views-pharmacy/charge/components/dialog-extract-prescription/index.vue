<template>
    <div>
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            append-to-body
            class="extract-prescription-dialog"
            :auto-focus="false"
            size="hugely"
            title="合作诊所处方"
            @open="handleOpen"
            @close="handleClose"
        >
            <abc-layout class="dialog-content clearfix">
                <abc-layout-content style="padding: 0;">
                    <abc-section>
                        <abc-space>
                            <abc-input
                                ref="searchInput"
                                v-model.trim="searchParams.keyword"
                                type="text"
                                placeholder="扫描处方上条码 / 搜索患者姓名、手机"
                                clearable
                                :width="280"
                                @clear="() => searchParams.keyword = ''"
                            >
                                <abc-search-icon slot="prepend"></abc-search-icon>
                            </abc-input>

                            <abc-date-picker
                                v-if="!searchParams.keyword"
                                v-model="datePickerValue"
                                :picker-options="pickerOptions"
                                value-format="YYYY-MM-DD"
                                type="daterange"
                                :width="262"
                                :clearable="false"
                                @change="changeDate"
                            ></abc-date-picker>
                        </abc-space>
                    </abc-section>
                    <abc-section>
                        <abc-table
                            class="extract-prescription-table"
                            :render-config="renderConfig"
                            :data-list="dataList"
                            :pagination="tablePagination"
                            style="height: 511px;"
                            :custom-tr-class="customTrClass"
                            :loading="contentLoading"
                            @pageChange="pageChange"
                        >
                            <template v-if="hasAdminModule && !hasBindHistory" #table-content-empty>
                                <abc-button style="margin-top: 16px;" @click="goBindClinic">
                                    绑定合作诊所
                                </abc-button>
                            </template>
                            <template #status="{ trData }">
                                <abc-table-cell>
                                    <abc-tag-v2 variant="outline" size="medium" v-bind="getStatusTag(trData.status)">
                                        {{ getStatusStr(trData.status) }}
                                    </abc-tag-v2>
                                </abc-table-cell>
                            </template>
                            <template #patientInfo="{ trData }">
                                <abc-table-cell>
                                    <div class="ellipsis" :title="getPatientInfoStr(trData)">
                                        {{ getPatientInfoStr(trData) }}
                                    </div>
                                </abc-table-cell>
                            </template>
                            <template #patientMobile="{ trData }">
                                <abc-table-cell v-if="trData.sourcePatientInfo">
                                    {{ trData.sourcePatientInfo.mobile }}
                                </abc-table-cell>
                            </template>
                            <template #diagnosis="{ trData }">
                                <abc-table-cell>
                                    <div class="ellipsis" :title="formatDentistry2Text(trData.extendDiagnosisInfos)">
                                        {{ formatDentistry2Text(trData.extendDiagnosisInfos) }}
                                    </div>
                                </abc-table-cell>
                            </template>
                            <template #medicines="{ trData }">
                                <abc-table-cell :title="getPRAbstractInfo(trData)">
                                    <div class="ellipsis">
                                        {{ getPRAbstractInfo(trData) }}
                                    </div>
                                </abc-table-cell>
                            </template>
                            <template #operate="{ trData }">
                                <abc-table-cell>
                                    <abc-button
                                        v-if="trData.status === CooperationOrderStatus.UNCHARGED"
                                        class="use-btn"
                                        size="small"
                                        :loading="btnLoading"
                                        @click="handleClickUse(trData)"
                                    >
                                        提单
                                    </abc-button>
                                    <abc-button
                                        v-else-if="trData.status === CooperationOrderStatus.UNCHARGED_WITH_PAID_RECORD"
                                        class="use-btn"
                                        size="small"
                                        :loading="btnLoading"
                                        @click="handleExtractCharge(trData)"
                                    >
                                        继续收费
                                    </abc-button>
                                    <abc-button
                                        v-else-if="trData.status === CooperationOrderStatus.REFUNDED || trData.status === CooperationOrderStatus.CLOSED"
                                        class="use-btn"
                                        size="small"
                                        variant="outline"
                                        :loading="btnLoading"
                                        @click="handleReExtract(trData)"
                                    >
                                        重新提单
                                    </abc-button>
                                    <abc-button
                                        v-else
                                        class="use-btn"
                                        size="small"
                                        :variant="trData.status === CooperationOrderStatus.PART_CHARGED ? 'fill' : 'outline'"
                                        :loading="btnLoading"
                                        @click="handleOpenChargeDetail(trData.relateChargeSheetId)"
                                    >
                                        <template v-if="trData.status === CooperationOrderStatus.PART_CHARGED">
                                            继续收费
                                        </template>
                                        <template v-else>
                                            查看详情
                                        </template>
                                    </abc-button>
                                </abc-table-cell>
                            </template>
                        </abc-table>
                    </abc-section>
                </abc-layout-content>
            </abc-layout>
        </abc-dialog>
        <dialog-sales-record-detail
            v-if="showDetailDialog"
            v-model="showDetailDialog"
            :charge-sheet-id="curChargeSheetId"
            @refresh-charge-sheet="fetchList"
        >
        </dialog-sales-record-detail>
    </div>
</template>

<script>
    import ChargeAPI from 'api/charge.js';
    import {
        debounce, isEqual,
    } from 'utils/lodash.js';
    import { parseTime } from '@abc/utils-date';
    import {
        prevDate,
        formatDate,
    } from '@abc/utils-date';
    import ExtractPRTable from '@/views-pharmacy/charge/components/dialog-extract-prescription/table-config';
    import ModulePermission from 'views/permission/module-permission';
    import { PharmacySettingsRouterNameKeys } from '@/views-pharmacy/settings/core/routes';
    import { formatDentistry2Text } from 'views/outpatient/common/medical-record/utils';
    import {
        CooperationOrderStatus, CooperationOrderStatusStr,
    } from '@/views-pharmacy/charge/utils/constants';
    import { mapGetters } from 'vuex';
    import BarcodeDetectorV2 from 'utils/barcode-detector-v2';
    import DialogSalesRecordDetail from '@/views-pharmacy/charge/components/dialog-sales-record-detail.vue';

    export default {
        name: 'ExtractPrescriptionDialog',
        components: {
            DialogSalesRecordDetail,
        },
        mixins: [
            ModulePermission,
        ],
        props: {
            value: Boolean,
            postData: Object,
        },
        data() {
            const now = new Date();
            const today = formatDate(new Date());
            const last7Day = formatDate(prevDate(now, 7));
            const last30Day = formatDate(prevDate(now, 30));
            const last90Day = formatDate(prevDate(now, 90));
            const last365Day = formatDate(prevDate(now, 365));
            return {
                CooperationOrderStatus,
                contentLoading: false,
                btnLoading: false,
                renderConfig: ExtractPRTable.extendConfig({
                    created: {
                        dataFormatter: (created) => parseTime(created, 'y-m-d h:i', true) || '-',
                    },
                }),
                dataList: [],

                searchParams: {
                    keyword: '',
                    beginDate: '',
                    endDate: '',
                    offset: 0,
                    limit: 10,
                },
                datePickerValue: [today, today],
                pickerStartDate: '', // 获取开始选择时间
                pickerOptions: {
                    onPick: ({
                        minDate, maxDate,
                    }) => {
                        if (minDate) {
                            this.pickerStartDate = minDate.getTime();
                        }
                        if (maxDate) {
                            this.pickerStartDate = '';
                        }
                    },
                    disabledDate: (time) => {
                        const day31 = (365 - 1) * 24 * 3600 * 1000;
                        if (this.pickerStartDate !== '') {
                            let maxTime = this.pickerStartDate + day31;
                            const minTime = this.pickerStartDate - day31;
                            if (maxTime > new Date()) {
                                maxTime = new Date();
                            }
                            return time.getTime() > maxTime ||
                                time.getTime() < minTime ||
                                time.getTime() > Date.now();
                        }
                        return time.getTime() > Date.now();
                    },
                    shortcuts: [
                        {
                            text: '今天',
                            onClick(picker) {
                                picker([
                                    today,
                                    today,
                                    '今天',
                                ]);
                            },
                        },
                        {
                            text: '近7天',
                            onClick(picker) {
                                picker([
                                    last7Day,
                                    today,
                                    '近7天',
                                ]);
                            },
                        },
                        {
                            text: '近30天',
                            onClick(picker) {
                                picker([
                                    last30Day,
                                    today,
                                    '近30天',
                                ]);
                            },
                        },
                        {
                            text: '近90天',
                            onClick(picker) {
                                picker([
                                    last90Day,
                                    today,
                                    '近90天',
                                ]);
                            },
                        },
                        {
                            text: '近1年',
                            onClick(picker) {
                                picker([
                                    last365Day,
                                    today,
                                    '近1年',
                                ]);
                            },
                        },
                    ],
                },
                totalCount: 0,
                showDetailDialog: false,
                curChargeSheetId: '',
            };
        },
        computed: {
            ...mapGetters('coPharmacyClinic', ['hasBindHistory']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            tablePagination() {
                const {
                    limit, offset,
                } = this.searchParams;
                return {
                    showTotalPage: true,
                    pageIndex: (offset / limit),
                    pageSize: limit,
                    count: this.totalCount || 0,
                };
            },
        },
        watch: {
            'searchParams.keyword': function(val) {
                if (val) {
                    this.searchParams.beginDate = '';
                    this.searchParams.endDate = '';
                    this._debounceSearch();
                } else {
                    this.searchParams.beginDate = this.datePickerValue[ 0 ];
                    this.searchParams.endDate = this.datePickerValue[ 1 ];
                    this.clearKey();
                }
            },
        },
        created() {
            this.initDataHandler();
            this._debounceSearch = debounce(this.fetchList, 250, true);
        },
        methods: {
            handleOpenChargeDetail(id) {
                this.curChargeSheetId = id;
                this.showDetailDialog = true;
            },
            handleOpen() {
                this.startBarcodeDetect();
            },
            handleClose() {
                this.stopBarcodeDetect();
                this.$emit('close');
            },
            startBarcodeDetect() {
                if (this._barcodeDetector) return;
                this._barcodeDetector = BarcodeDetectorV2.getInstance();
                this._barcodeDetector.startDetect(this.handleBarcode);
            },
            stopBarcodeDetect() {
                if (!this._barcodeDetector) return;
                this._barcodeDetector.stopDetect(this.handleBarcode);
                this._barcodeDetector = null;
            },
            handleBarcode(event, barcode) {
                const { $el } = this.$refs.searchInput;
                /**
                 * @desc target是body || 是搜索框才触发ql栏的扫码搜索
                 * <AUTHOR> Yang
                 * @date 2024-08-15 14:58:05
                 */
                if (event.target === $el || event.target === document.body) {
                    this.searchParams.keyword = barcode;
                }
            },
            customTrClass() {
                return 'extract-prescription-table-tr';
            },
            formatDentistry2Text,
            getPRAbstractInfo(item) {
                const {
                    abstractInfo,
                } = item;
                const {
                    orderItems = [],
                } = abstractInfo || {};
                return orderItems.map((it) => {
                    return `${it.name || ''} ${it.count || 1} ${it.unit || ''}`;
                }).join('，');
            },
            getStatusTag(status) {
                if ([
                    CooperationOrderStatus.UNCHARGED,
                    CooperationOrderStatus.UNCHARGED_WITH_PAID_RECORD,
                    CooperationOrderStatus.PART_CHARGED,
                ].indexOf(status) > -1) {
                    return {
                        theme: 'primary',
                    };
                }

                if ([
                    CooperationOrderStatus.CHARGED,
                    CooperationOrderStatus.PART_REFUNDED,
                ].indexOf(status) > -1) {
                    return {
                        theme: 'success',
                    };
                }

                if ([
                    CooperationOrderStatus.REFUNDED,
                ].indexOf(status) > -1) {
                    return {
                        theme: 'danger',
                    };
                }

                return {
                    theme: 'default',
                };
            },
            getStatusStr(status) {
                return CooperationOrderStatusStr[status];
            },
            getPatientInfoStr(item) {
                const {
                    sourcePatientInfo,
                } = item;
                const {
                    name,
                    sex,
                    age,
                } = sourcePatientInfo || {};
                return `${name || ''} ${sex || ''} ${age ? `${age.year}岁` : ''}`;
            },
            goBindClinic() {
                this.$router.push({
                    name: PharmacySettingsRouterNameKeys.pharmacyLinkClinicIntroduce,
                });
            },
            clearKey() {
                this.searchParams.keyword = '';
                this.initDataHandler();
            },
            pageChange(page) {
                this.searchParams.offset = (page - 1) * this.searchParams.limit;
                this.fetchList();
            },
            changeDate() {
                this.pickerStartDate = '';
                this.searchParams.offset = 0;
                this.initDataHandler();
            },
            initDataHandler() {
                this.searchParams.beginDate = this.datePickerValue[ 0 ];
                this.searchParams.endDate = this.datePickerValue[ 1 ];
                this.searchParams.offset = 0;
                this.fetchList();
            },
            async fetchList() {
                try {
                    this.contentLoading = true;
                    const { searchParams } = this;
                    const { data } = await ChargeAPI.fetchCooperationOrder(searchParams);
                    if (!isEqual(searchParams, this.searchParams)) return;
                    this.totalCount = data.total;
                    this.dataList = data.rows;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },
            handleClickUse(data) {
                this.$emit('extract', data);
                this.showDialog = false;
            },
            // 特殊情况，有收费记录的待收单据，需要先提单，然后再打开收费详情
            async handleExtractCharge(order) {
                try {
                    this.btnLoading = true;
                    const { data } = await ChargeAPI.extractCooperationOrder(order.id);
                    this.handleOpenChargeDetail(data.id);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
            handleReExtract(data) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '该处方已全部退费，是否确定重新提单收费？',
                    onConfirm: () => {
                        this.$emit('extract', data);
                        this.showDialog = false;
                    },
                });
            },
        },
    };
</script>
<style lang="scss">
.extract-prescription-dialog {
    .dialog-content {
        min-height: 560px;
    }

    .extract-prescription-table {
        .use-btn {
            min-width: 70px;
            visibility: hidden;
        }

        .extract-prescription-table-tr {
            &:hover {
                .use-btn {
                    visibility: visible;
                }
            }
        }
    }
}
</style>

