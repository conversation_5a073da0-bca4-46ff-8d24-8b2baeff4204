<template>
    <abc-dialog
        v-model="showDialog"
        :title="title"
        append-to-body
        :auto-focus="false"
        size="medium"
        class="refund-audit-confirm-dialog"
        :content-styles="contentStyles"
        @close-dialog="handleClose"
    >
        <abc-form
            ref="form"
            item-block
            item-no-margin
            :label-width="70"
        >
            <abc-flex vertical gap="16">
                <abc-form-item label="审核人" :error="auditorError">
                    <abc-select-input
                        v-model="selectedAuditorInfoStr"
                        :visible-popper.sync="visiblePanel"
                        placeholder="请选择审核人"
                        size="large"
                        popper-class="refund-audit-confirm-dialog-abc-select-input-popper"
                        adaptive-width
                    >
                        <abc-suggestions-panel
                            custom-class="refund-audit-confirm-dialog-suggestions-panel"
                        >
                            <abc-suggestions-item
                                v-for="item in refundCheckEmployees"
                                :key="item.id"
                                :value="item.id"
                                :selected="item.id === postData.auditorId"
                                @click="handleSelectItem(item)"
                            >
                                <abc-suggestions-cell width="88" :title="item.name">
                                    {{ item.name }}
                                </abc-suggestions-cell>
                                <abc-suggestions-cell width="120" align="right">
                                    <abc-text :theme="item.id === postData.auditorId ? 'white' : 'gray'">
                                        {{ `+${item.countryCode || '86'} ${formatPhone(item.mobile)}` }}
                                    </abc-text>
                                </abc-suggestions-cell>
                            </abc-suggestions-item>
                        </abc-suggestions-panel>
                    </abc-select-input>
                </abc-form-item>
                <abc-form-item label="确认方式">
                    <abc-flex
                        style="width: 100%;"
                        gap="large"
                        justify="flex-start"
                        align="flex-start"
                    >
                        <abc-option-card
                            :value="postData.confirmType === 1"
                            :show-icon="false"
                            selectable="icon-inside"
                            theme="primary"
                            width="198"
                            height="46"
                            title="微信扫码"
                            :title-config="{
                                size: 'normal', style: { textAlign: 'center' }
                            }"
                            @input="handleOptionCardInput(1, $event)"
                        >
                        </abc-option-card>
                        <abc-tooltip
                            content="未设置密码"
                            placement="top"
                            :disabled="!!(!currentAuditor || currentAuditor?.hasPassword)"
                        >
                            <abc-option-card
                                :disabled="!currentAuditor || !currentAuditor?.hasPassword"
                                :value="postData.confirmType === 3"
                                :show-icon="false"
                                selectable="icon-inside"
                                theme="primary"
                                width="198"
                                height="46"
                                title="登录密码"
                                :title-config="{
                                    size: 'normal', style: { textAlign: 'center' }
                                }"
                                @input="handleOptionCardInput(3, $event)"
                            >
                            </abc-option-card>
                        </abc-tooltip>
                    </abc-flex>
                </abc-form-item>
                <abc-form-item v-if="postData.confirmType === 1" label="">
                    <abc-flex style="width: 100%;" justify="center">
                        <div v-abc-loading="qrCodeLoading" class="img-wrapper">
                            <abc-qr-code
                                :src="qrCode"
                                :width="160"
                                :need-refresh="needRefresh"
                                @refresh="changeAccount"
                            ></abc-qr-code>
                            <!--                            <img v-if="qrCode" :src="qrCode" alt="" />-->
                            <!--                            <div v-if="needRefresh" class="img-wrapper-tips">-->
                            <!--                                <div class="tips" @click="changeAccount">-->
                            <!--                                    <abc-icon icon="s-b-refresh-line-medium" :size="32" color="#ffffff"></abc-icon>-->
                            <!--                                    <div class="tips-text">-->
                            <!--                                        刷新二维码-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                        </div>
                    </abc-flex>
                </abc-form-item>
                <!-- <abc-form-item
                    v-show="postData.confirmType === 2"
                    hidden-red-dot
                    label=""
                    :error="codeError"
                >
                    <abc-input
                        v-model="postData.code"
                        size="large"
                        adaptive-width
                    >
                        <template slot="appendInner">
                            <abc-button
                                v-if="sendCode"
                                variant="text"
                                theme="default"
                                size="small"
                                :disabled="sendCode"
                            >
                                {{ count }}s
                            </abc-button>
                            <abc-button
                                v-else
                                variant="text"
                                size="small"
                                @click="debounceFetchCode"
                            >
                                获取验证码
                            </abc-button>
                        </template>
                    </abc-input>
                </abc-form-item> -->
                <abc-form-item
                    v-show="postData.confirmType === 3"
                    hidden-red-dot
                    label=""
                    :error="passwordError"
                >
                    <abc-input
                        ref="passwordInput"
                        v-model="postData.password"
                        type="password"
                        autocomplete="off"
                        adaptive-width
                        size="large"
                        placeholder="审核人登录密码"
                    ></abc-input>
                </abc-form-item>
            </abc-flex>
        </abc-form>
        <div v-if="postData.confirmType !== 1" slot="footer" class="dialog-footer">
            <abc-button
                type="primary"
                :loading="confirmLoading"
                :disabled="postData.confirmType === 1 || !currentAuditor || postData.confirmType === 2"
                @click="debounceConfirm"
            >
                {{ confirmText }}
            </abc-button>
            <abc-button type="blank" @click="handleClose">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import ChargeAPI from 'api/charge';
    import SettingApi from 'api/settings.js';
    import AbcSocket from 'views/common/single-socket.js';
    import localStorage from 'utils/localStorage-handler.js';
    import { mapGetters } from 'vuex';
    import { debounce } from 'utils/lodash';
    import CryptoJS from 'crypto-js';

    const STORAGE_KEY = 'REFUND_LAST_AUDITOR_ID';

    export default {
        name: 'RefundAuditConfirmDialog',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            chargeSheetId: {
                type: String,
                default: '',
            },
            refundCheckEmployees: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                confirmLoading: false,
                qrCodeLoading: false,
                qrCode: '',
                qrCodeSceneKey: '',
                needRefresh: false,
                sendCode: false,
                count: 60,
                codeTimer: null,
                timer: null,
                visiblePanel: false,
                postData: {
                    auditorId: '',
                    confirmType: 1,
                    code: '',
                    phone: '',
                    password: '',
                },
                auditorError: {
                    error: false,
                    message: '',
                },
                codeError: {
                    error: false,
                    message: '',
                },
                passwordError: {
                    error: false,
                    message: '',
                },
                selectedAuditorInfoStr: '',
            };
        },
        computed: {
            ...mapGetters([
                'clinicBasicConfig',
                'userInfo',
                'isPharmacy',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                set(value) {
                    this.$emit('input', value);
                },
            },
            title() {
                return this.isPharmacy ? '需审核人确认退货' : '需审核人确认退费';
            },
            confirmText() {
                return this.isPharmacy ? '确认退货' : '确认退费';
            },
            returnErrText() {
                return this.isPharmacy ? '不是退货审核人' : '不是退费审核人';
            },
            currentAuditor() {
                return this.refundCheckEmployees.find((item) => item.id === this.postData.auditorId) || null;
            },
            clinicId() {
                return this.clinicBasicConfig.id;
            },
            employeeId() {
                return this.userInfo.id;
            },
            contentStyles() {
                return this.postData.confirmType === 1 ? 'height: 376px;' : 'height: 320px;';
            },
        },
        created() {
            this._AesKey = CryptoJS.enc.Utf8.parse('v0zpnowyokb9k248u2mrbi0rkh22dni5');
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('clinic_business_auth.charge_refund_check', this.employeeBusAuthScan, `clinic_business_auth_${this.clinicId}_${this.employeeId}`);
            this.debounceFetchCode = debounce(this.fetchCode, 500, true);
            this.debounceConfirm = debounce(this.handleConfirm, 300, true);
            this.initLastAuditor();
            this.changeAccount();
        },
        beforeDestroy() {
            this.stopCountDown();
            this._socket?.off('clinic_business_auth.charge_refund_check', this.employeeBusAuthScan, `clinic_business_auth_${this.clinicId}_${this.employeeId}`);
            this._socket = null;
            if (this._timer) {
                clearTimeout(this._timer);
                this._timer = null;
            }
            if (this.codeTimer) {
                clearInterval(this.codeTimer);
                this.codeTimer = null;
            }
            // 组件销毁时移除autocomplete属性
            this.clearPasswordAutocomplete();
        },
        methods: {
            // ===== 初始化方法 =====
            /**
             * 初始化上次选择的审核人
             */
            initLastAuditor() {
                // 取本地存储的当前用户上次选择的费别
                const storageObj = localStorage.get(STORAGE_KEY, true, true);
                if (storageObj && this.userInfo.id === storageObj.userId && storageObj.auditorId) {
                    const item = this.refundCheckEmployees.find(($item) => $item.id === storageObj.auditorId);
                    if (item) {
                        this.handleSelectItem(item);
                        return;
                    }
                }
                if (this.refundCheckEmployees.length === 1) {
                    // 如果只有一个审核人，自动选择
                    this.handleSelectItem(this.refundCheckEmployees[0]);
                }
            },

            // ===== 主要业务方法 =====
            /**
             * 验证成功后的回调
             */
            successHandler(token) {
                if (!token) {
                    this.$Toast({
                        type: 'error',
                        message: '审核失败',
                    });
                    return;
                }
                // 存储最后选择的审核人
                const storageObj = {
                    userId: this.userInfo.id,
                    auditorId: this.postData.auditorId,
                };
                localStorage.set(STORAGE_KEY, storageObj, true);

                this.$Toast({
                    type: 'success',
                    message: '审核成功',
                });
                this.$emit('success', token);
                this.successHandleClose();
            },
            successHandleClose() {
                this.stopCountDown();
                this.showDialog = false;
                this.$emit('close');
            },
            handleClose() {
                if (this.qrCodeSceneKey) {
                    this.updateBusAuthenticationQrCodeStatus();
                }
                this.stopCountDown();
                this.showDialog = false;
                this.$emit('close');
            },

            aesEncrypt(data) {
                const secretKey = CryptoJS.enc.Utf8.parse(CryptoJS.MD5(this._AesKey).toString());
                const encrypted = CryptoJS.AES.encrypt(data, secretKey, {
                    mode: CryptoJS.mode.ECB,
                    padding: CryptoJS.pad.Pkcs7,
                }).toString();
                return encrypted;
            },

            /**
             * 获取退费配置并校验
             */
            async getRefundConfigAndCheck(employeeId) {
                try {
                    const { data } = await SettingApi.chargeSet.selectChargesConfig();
                    const { refundCheck } = data;
                    const refundCheckEmployees = data.refundCheckEmployees || [];
                    // 更新审核人列表
                    this.$emit('update:refundCheckConfig', {
                        refundCheck,
                        refundCheckEmployees,
                    });
                    if (!refundCheck) {
                        // 关闭弹窗，且刷新父级页面的配置

                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${this.isPharmacy ? '退货' : '退费'}审核已关闭`,
                            onClose: () => {
                                this.$emit('update:refundCheckConfig', {
                                    refundCheck,
                                    refundCheckEmployees,
                                });
                                this.handleClose();
                            },
                        });
                        if (employeeId) return 'close';
                        return false;
                    }
                    // 扫码传入的employeeId
                    if (employeeId && !refundCheckEmployees.some((item) => item.id === employeeId)) {
                        if (this.postData.auditorId && this.postData.auditorId === employeeId) {
                            this.auditorError.error = true;
                            this.auditorError.message = '审核人不存在';
                        } else {
                            this.$Toast({
                                type: 'error',
                                message: '请使用审核人的微信扫码',
                            });
                        }
                        return 'close';
                    }
                    // 判断当前选中的审核人是否在配置的审核人列表中
                    if (!refundCheckEmployees.some((item) => item.id === this.postData.auditorId)) {
                        this.auditorError.error = true;
                        this.auditorError.message = '审核人不存在';
                        return false;
                    }

                    return true;
                } catch (e) {
                    console.error(e);
                    return false;
                }
            },

            async handleConfirm(authCode) {
                this.clearFormErrors();

                if (!this.currentAuditor) {
                    this.auditorError.error = true;
                    this.auditorError.message = '请选择审核人';
                    return;
                }

                if (this.postData.confirmType === 2 && !this.postData.code) {
                    this.codeError.error = true;
                    this.codeError.message = '请输入验证码';
                    return;
                }

                if (this.postData.confirmType === 3 && !this.postData.password) {
                    this.passwordError.error = true;
                    this.passwordError.message = '请输入密码';
                    return;
                }

                const bool = await this.getRefundConfigAndCheck();
                if (!bool) return;
                try {
                    this.confirmLoading = true;
                    const params = {
                        'employeeId': this.currentAuditor.id,
                        'businessId': this.chargeSheetId,
                        'businessType': 'charge.refundCheck',
                        'onlyEmployeeSelf': 0,
                    };
                    if (this.postData.confirmType === 1) {
                        params.grantType = 'authorization_code';
                        params.authCode = authCode;
                    } else if (this.postData.confirmType === 2) {
                        params.grantType = 'verify_code';
                        params.verifyCode = this.postData.code;
                    } else if (this.postData.confirmType === 3) {
                        params.grantType = 'password';
                        params.password = this.aesEncrypt(this.postData.password);
                    }
                    const res = await ChargeAPI.verifyEmployeeBusAuthentication(params);
                    this.successHandler(res?.data?.accessToken);
                } catch (error) {
                    if (this.postData.confirmType === 2) {
                        this.codeError.error = true;
                        this.codeError.message = '验证码错误';
                    } else if (this.postData.confirmType === 3) {
                        this.passwordError.error = true;
                        this.passwordError.message = '密码错误';
                    }
                } finally {
                    this.confirmLoading = false;
                }
            },

            handleSelectItem(item) {
                this.clearFormErrors();
                this.stopCountDown();
                this.visiblePanel = false;

                if (!item) return;

                // const {
                //     id, name, countryCode, mobile, wechatSubscribe, hasPassword,
                // } = item;
                const {
                    id, name, countryCode, mobile,
                } = item;

                // 更新基本信息
                this.postData.auditorId = id;
                this.postData.phone = mobile;
                this.postData.code = '';
                this.postData.password = '';
                this.selectedAuditorInfoStr = `${name}    +${countryCode || '86'} ${this.formatPhone(mobile)}`;
                // 因为微信扫码背放开了 应该默认带给你
                this.postData.confirmType = 1;
                // this.changeAccount();
                // // 当前确认方式
                // const currentType = this.postData.confirmType;
                //
                // // 检查审核人是否支持当前确认方式
                // const confirmTypeMap = {
                //     1: wechatSubscribe,
                //     2: !!mobile,
                //     3: hasPassword,
                // };

                // 如果支持当前确认方式，则保持不变
                // if (confirmTypeMap[currentType] && currentType !== 2) {
                //     return;
                // }

                // 否则设置为支持的第一个确认方式 暂时屏蔽
                // if (wechatSubscribe) {
                //     this.postData.confirmType = 1;
                // } else if (mobile) {
                //     this.postData.confirmType = 2;
                // } else if (hasPassword) {
                //     this.postData.confirmType = 3;
                //     this.$nextTick(() => {
                //         this.setPasswordAutocomplete();
                //     });
                // }

                // if (wechatSubscribe) {
                // this.postData.confirmType = 1;
                // } else if (hasPassword) {
                //     this.postData.confirmType = 3;
                //     this.$nextTick(() => {
                //         this.setPasswordAutocomplete();
                //     });
                // } else if (mobile) {
                //     this.postData.confirmType = 2;
                // }
            },
            async updateBusAuthenticationQrCodeStatus() {
                try {
                    await ChargeAPI.updateBusAuthenticationQrCodeStatus({
                        status: 99,
                        qrCodeSceneKey: this.qrCodeSceneKey,
                    });
                } catch (e) {
                    console.log(e);
                }
            },

            async fetchBusAuthenticationQrCodeStatus() {
                try {
                    const { data } = await ChargeAPI.getBusAuthenticationQrCodeStatus({
                        qrCodeSceneKey: this.qrCodeSceneKey,
                    });
                    // 表示二维码过期了需要结束扫码状态
                    if (data.status !== 0) {
                        this.needRefresh = true;
                        clearInterval(this.codeTimer);
                        this.codeTimer = null;
                        this.qrCodeSceneKey = '';
                    }
                } catch (e) {
                    console.log(e);
                }
            },
            /**
             * desc [点击切换账号]
             */
            async changeAccount() {
                this.qrCode = '';
                this.qrCodeSceneKey = '';
                this.qrCodeLoading = true;
                this.needRefresh = false;
                if (this.codeTimer) {
                    clearInterval(this.codeTimer);
                }
                try {
                    const data = await this.getSceneQr();
                    if (data) {
                        this.qrCode = data.qrCodeUrl || '';
                        this.qrCodeSceneKey = data.qrCodeSceneKey || '';
                        this.codeTimer = setInterval(() => {
                            this.fetchBusAuthenticationQrCodeStatus();
                            // 10s轮询一次接口
                        }, 10000);
                    } else {
                        this.needRefresh = true;
                    }
                } catch (e) {
                    console.log(e);
                    this.needRefresh = true;
                } finally {
                    this.qrCodeLoading = false;
                }
            },
            /**
             * desc [获取场景二维码]
             */
            async getSceneQr() {
                try {
                    const { data } = await ChargeAPI.getBusAuthenticationQrCode({
                        businessId: this.chargeSheetId,
                        businessType: 'charge.refundCheck',
                    });
                    return data;
                } catch (error) {
                    console.log('getSceneQr error', error);
                }
            },
            /**
             * desc [扫码登录成功返回]
             * @param {*} data {token, scanEmployeeId}
             */
            async employeeBusAuthScan(resData) {
                const { data } = resData;
                if (!data?.scanEmployeeId) {
                    this.$Toast.error('用户不存在');
                    return;
                }
                if (this.postData.auditorId !== data.scanEmployeeId) {
                    // 审核人列表中无扫码人
                    if (!this.refundCheckEmployees?.find((item) => {
                        return item.id === data.scanEmployeeId;
                    })) {
                        this.$Toast.error(this.returnErrText);
                        return;
                    }
                    const currentItem = this.refundCheckEmployees?.find((item) => {
                        return item.id === data.scanEmployeeId;
                    });
                    // 反选中审核人
                    this.handleSelectItem(currentItem);
                }
                const bool = await this.getRefundConfigAndCheck(data.scanEmployeeId);
                if (bool === 'close') return;
                if (!bool) {
                    return;
                }
                this.postData.auditorId = data.scanEmployeeId;
                this._timer = setTimeout(() => {
                    this.successHandler(data.token);
                }, 1500);
            },
            /**
             * 进程中断
             * 二维码失效，需要刷新重连
             */
            disconnect() {
                this.needRefresh = true;
            },

            // ===== 验证码相关方法 =====
            /**
             * 获取验证码
             */
            async fetchCode() {
                if (this.sendCode) return;
                if (!this.postData.auditorId) {
                    this.auditorError.error = true;
                    this.auditorError.message = '请选择审核人';
                    return;
                }
                try {
                    await ChargeAPI.sendSmsVerifyCode({
                        employeeId: this.currentAuditor.id,
                        businessType: 'charge.refundCheck',
                        countryCode: this.currentAuditor.countryCode,
                    });
                    this.startCountDown();
                } catch (error) {
                    console.error('获取验证码失败:', error);
                }
            },
            /**
             * 开始倒计时
             */
            startCountDown() {
                this.sendCode = true;
                this.count = 60;
                this.timer = setInterval(() => {
                    if (this.count > 0) {
                        this.count--;
                    } else {
                        this.stopCountDown();
                    }
                }, 1000);
            },
            /**
             * 停止倒计时
             */
            stopCountDown() {
                this.sendCode = false;
                this.count = 60;
                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            },

            // ===== 工具方法 =====
            /**
             * 手机号脱敏
             * @param {string} phone - 手机号
             * @returns {string} - 脱敏后的手机号
             */
            formatPhone(phone) {
                if (!phone) return '';
                return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
            },
            clearFormErrors() {
                this.auditorError.error = false;
                this.auditorError.message = '';
                this.codeError.error = false;
                this.codeError.message = '';
                this.passwordError.error = false;
                this.passwordError.message = '';
            },
            // 切换验证方式
            handleOptionCardInput(type, value) {
                if (value) {
                    this.postData.confirmType = type;
                    this.stopCountDown();
                    this.clearFormErrors();
                    // 如果选择了密码验证方式，在下一个渲染周期设置autocomplete属性
                    if (type === 3) {
                        this.$nextTick(() => {
                            this.setPasswordAutocomplete();
                        });
                    }
                }
            },
            // 设置密码输入框的autocomplete属性
            setPasswordAutocomplete() {
                if (this.$refs.passwordInput) {
                    // 找到abc-input内部的原生input元素
                    const inputEl = this.$refs.passwordInput.$el.querySelector('input');
                    if (inputEl) {
                        // 设置autocomplete属性为new-password
                        inputEl.setAttribute('autocomplete', 'new-password');
                    }
                }
            },
            // 清除密码输入框的autocomplete属性
            clearPasswordAutocomplete() {
                if (this.$refs.passwordInput) {
                    const inputEl = this.$refs.passwordInput.$el.querySelector('input');
                    if (inputEl) {
                        // 将autocomplete属性恢复为默认值
                        inputEl.setAttribute('autocomplete', 'off');
                    }
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';

.refund-audit-confirm-dialog {
    padding: 20px;

    .img-wrapper {
        position: relative;
        width: 160px;
        height: 160px;
        overflow: hidden;
        border: 1px solid var(--abc-color-P8);
        border-radius: var(--abc-border-radius-small);

        &-tips {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            border-radius: var(--abc-border-radius-small);

            .tips {
                position: absolute;
                top: 50%;
                right: 0;
                left: 0;
                z-index: 2;
                width: 100%;
                color: var(--abc-color-S2);
                text-align: center;
                cursor: pointer;
                transform: translateY(-50%);

                .tips-text {
                    margin-top: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    line-height: 22px;
                    color: var(--abc-color-S2);
                }
            }
        }

        .abc-loading-wrapper {
            .cover-wrap {
                border-radius: var(--abc-border-radius-small);
            }
        }

        img {
            width: 100%;
            height: 100%;
            border-radius: var(--abc-border-radius-small);
        }
    }

    .option-cards-container {
        display: flex;
        gap: 16px;
    }
}

.refund-audit-confirm-dialog-abc-select-input-popper {
    border: unset !important;

    .abc-suggestions-panel {
        .abc-suggestions-content {
            border-radius: var(--abc-border-radius-small);
        }
    }
}
</style>
