<template>
    <abc-popover
        width="230px"
        placement="bottom"
        trigger="hover"
        theme="yellow"
        popper-class="chinese-stat-popover"
    >
        <span slot="reference" style="cursor: pointer;">
            <slot></slot>
        </span>
        <abc-text tag="div" class="content-header">
            合计明细
        </abc-text>
        <ul class="content-wrap">
            <li v-for="item in statList" :key="item.id" class="content-item">
                <abc-text class="name" :title="item.name">
                    {{ item.name }}
                </abc-text>
                <abc-text class="unitCount">
                    {{ item.unitCount }}{{ item.unit }}
                </abc-text>
                <abc-text class="doseCount" theme="gray">
                    ×{{ item.doseCount }}
                </abc-text>
                <abc-text class="unit" :title="item.countStr">
                    {{ item.countStr }}
                </abc-text>
            </li>
        </ul>
    </abc-popover>
</template>

<script>
    import Big from 'big.js';

    export default {
        name: 'ChargeStatPopover',
        props: {
            form: {
                type: Object,
                default: () => {
                    return {};
                },
            },
        },
        computed: {
            statList() {
                const {
                    chargeFormItems,
                    doseCount,
                } = this.form;
                return chargeFormItems
                    .filter((x) => x.checked)
                    .map((item) => {
                        const {
                            id,
                            name,
                            unitCount,
                            unit,
                        } = item;

                        const count = Big(Number(unitCount)).times(doseCount || 1).toNumber();
                        return {
                            id,
                            name,
                            doseCount: doseCount || 1,
                            unitCount: unitCount || '',
                            unit: unit || '',
                            countStr: `${count} ${unit}`,
                        };
                    });
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .chinese-stat-popover {
        padding: 0;

        .content-header {
            height: 38px;
            line-height: 38px;
            text-align: center;
            border-bottom: 1px solid var(--abc-color-LY1);
        }

        .content-wrap {
            max-height: 400px;
            padding: 8px 12px 4px;
            overflow-x: hidden;
            overflow-y: auto;

            .content-item {
                display: flex;
                gap: 2px;
                align-items: center;
                justify-content: space-between;
                height: 22px;
                margin-bottom: 4px;
                line-height: 22px;

                .name {
                    flex: 1;

                    @include ellipsis();
                }

                .unit {
                    width: 51px;
                    text-align: right;

                    @include ellipsis();
                }

                .unitCount {
                    width: 48px;
                    text-align: right;

                    @include ellipsis();
                }

                .doseCount {
                    width: 32px;
                    text-align: right;

                    @include ellipsis();
                }
            }
        }
    }
</style>
