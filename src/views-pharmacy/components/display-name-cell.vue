<script>
    import {
        goodsFullName, goodsSpec,
    } from '@/filters';

    export default {
        name: 'DisplayNameCell',
        props: {
            goods: {
                type: Object,
                required: true,
            },
            cellConfig: {
                type: Object,
                default: () => ({}),
            },
            contentStyle: {
                type: Object,
                default: () => ({}),
            },
            hoverConfig: {
                type: Object,
                default: () => ({
                    openDelay: 500,
                }),
            },
            showTooltip: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            displayName() {
                return this.goods?.displayName ?? goodsFullName(this.goods);
            },
            displaySpec() {
                return this.goods?.displaySpec ?? goodsSpec(this.goods);
            },
            manufacturer() {
                return this.goods?.manufacturer ?? this.goods?.manufacturerFull ?? '';
            },
            hoverProps() {
                return {
                    goods: this.goods,
                    ...this.hoverConfig,
                    handleGoods: () => {
                        this.$emit('updateGoods');
                    },
                };
            },
        },
    };
</script>

<template>
    <abc-table-cell v-abc-goods-hover-popper:remote="hoverProps" v-bind="cellConfig">
        <abc-flex vertical class="ellipsis" :style="contentStyle">
            <abc-text
                v-if="!showTooltip"
                theme="black"
                bold
                class="ellipsis"
            >
                {{ displayName }}
            </abc-text>
            <abc-flex
                v-else
                justify="start"
                :gap="4"
                align="center"
            >
                <abc-text
                    theme="black"
                    bold
                    class="ellipsis"
                >
                    {{ displayName }}
                </abc-text>
                <abc-tooltip
                    placement="top"
                    content="不在供应商经营范围"
                >
                    <abc-icon
                        style="overflow: unset;"
                        :size="16"
                        icon="s-alert-small-fill"
                        color="var(--abc-color-Y3)"
                    ></abc-icon>
                </abc-tooltip>
            </abc-flex>

            <abc-flex align="center" :gap="4">
                <abc-text theme="gray" size="mini" class="ellipsis">
                    {{ displaySpec }}
                </abc-text>
                <abc-text
                    theme="gray"
                    size="mini"
                    class="ellipsis"
                    style="flex: 1; min-width: 72px;"
                >
                    {{ manufacturer }}
                </abc-text>
            </abc-flex>
        </abc-flex>
    </abc-table-cell>
</template>
