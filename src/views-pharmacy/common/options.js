/*
 * <AUTHOR>
 * @DateTime 2023-12-29 10:31:24
 */
import * as constants from './constants';
import { dosageFormType } from 'views/common/inventory/constants';

// 首营审核状态
export const gspStatusOptions = Object.freeze([
    // 产品崔说的，gsp这边不展示待首营的
    // {
    //     value: constants.gspStatusConst.AWAIT,
    //     label: '待首营',
    // },
    {
        value: constants.gspStatusConst.DOING,
        label: '审批中',
    },
    {
        value: constants.gspStatusConst.REJECT,
        label: '已驳回',
    },
    {
        value: constants.gspStatusConst.FINISH,
        label: '已首营',
    },
]);

// 供应商类型
export const supplierTypeOptions = Object.freeze([
    {
        value: constants.supplierTypeConst.BUSINESS,
        label: '药品批发企业',
    },
    {
        value: constants.supplierTypeConst.MEDICINAL_MAKE,
        label: '药品生产企业',
    },
    {
        value: constants.supplierTypeConst.INSTRUMENT_WHOLESALE,
        label: '医疗器械批发企业',
    },
    {
        value: constants.supplierTypeConst.INSTRUMENT_MAKE,
        label: '医疗器械生产企业',
    },
    {
        value: constants.supplierTypeConst.COSMETICS_MAKE,
        label: '化妆品生产企业',
    },
    {
        value: constants.supplierTypeConst.OTHER,
        label: '其他商业企业',
    },
]);

// 经营范围
export const businessScopeOptions = Object.freeze([
    {
        value: '1',
        label: '西药',
    },
    {
        value: '2',
        label: '中成药',
    },
    {
        value: '3',
        label: '中药材',
    },
    {
        value: '4',
        label: '中药饮片',
    },
    {
        value: '5',
        label: '化学原料药',
    },
    {
        value: '6',
        label: '化学药制剂',
    },
    {
        value: '7',
        label: '抗生素原料药',
    },
    {
        value: '8',
        label: '抗生素制剂',
    },
    {
        value: '9',
        label: '生化药品',
    },
    {
        value: '10',
        label: '生物制品（疫苗除外）',
    },
    {
        value: '11',
        label: '注射制剂',
    },
    {
        value: '12',
        label: '含麻药品',
    },
    {
        value: '13',
        label: '二类精神药品',
    },
    {
        value: '14',
        label: '医疗用毒性药品',
    },
    {
        value: '15',
        label: '罂粟壳',
    },
    {
        value: '16',
        label: '食品',
    },
    {
        value: '17',
        label: '保健食品',
    },
    {
        value: '18',
        label: '含婴幼儿配方乳制品',
    },
    {
        value: '19',
        label: '其他特殊食品',
    },
    {
        value: '20',
        label: '一类医疗器械',
    },
    {
        value: '21',
        label: '二类医疗器械',
    },
    {
        value: '22',
        label: '三类医疗器械',
    },
    {
        value: '23',
        label: '普通化妆品',
    },
    {
        value: '24',
        label: '特殊化妆品',
    },
    {
        value: '25',
        label: '消毒产品',
    },
    {
        value: '26',
        label: '日用百货',
    },
    {
        value: '27',
        label: '保健用品',
    },
    {
        value: '28',
        label: '农副产品',
    },
    {
        value: '29',
        label: '物料服务',
    },
    {
        value: '99',
        label: '其他',
    },
]);

// 养护类型
export const maintenanceTypeOptions = Object.freeze([
    {
        value: constants.maintenanceTypeConst.GENERAL,
        label: '一般养护',
    },
    {
        value: constants.maintenanceTypeConst.KEYNOTE,
        label: '重点养护',
    },
]);

// GSP质量可疑上报
export const suspiciousStatusOptions = Object.freeze([
    {
        value: 10,
        label: '待审核',
    },
    {
        value: 30,
        label: '已完成',
    },
]);

// GSP不合格
export const unqualifiedStatusOptions = Object.freeze([
    {
        value: 20,
        label: '报损中',
    },
    {
        value: 21,
        label: '已报损',
    },
    {
        value: 10,
        label: '退货中',
    },
    {
        value: 11,
        label: '已退货',
    },
    {
        value: 31,
        label: '待销毁',
    },
    {
        value: 32,
        label: '已销毁',
    },
]);


// GSP销毁记录
export const destroyStatusOptions = Object.freeze([
    {
        value: 30,
        label: '待销毁',
    },
    {
        value: 40,
        label: '已销毁',
    },
]);

// GSP销毁记录
export const destroyApplyStatusOptions = Object.freeze([
    {
        value: 10,
        label: '待审核',
    },
    {
        value: 20,
        label: '已驳回',
    },
    {
        value: 30,
        label: '待销毁',
    },
    {
        value: 40,
        label: '已销毁',
    },
]);

export const suspiciousResultStatusOptions = Object.freeze([
    {
        value: 1,
        label: '确认不合格',
    },
    {
        value: 0,
        label: '解除可疑',
    },
]);

export const suspiciousGspOptions = Object.freeze([
    {
        value: 0,
        label: '解除可疑',
    },
    {
        value: 1,
        label: '确认不合格',
    },
]);

// 包装状况
export const appearancePackageConditionOptions = Object.freeze([
    {
        value: constants.appearancePackageConditionConst.GOOD,
        label: '良好',
    },
    {
        value: constants.appearancePackageConditionConst.WORN,
        label: '破损',
    },
]);

// 质量状况
export const qualityConditionOptions = Object.freeze([
    {
        value: constants.qualityConditionConst.GOOD,
        label: '良好',
    },
    {
        value: constants.qualityConditionConst.BAD,
        label: '变质',
    },
]);

// 处理结果
export const resultOptions = Object.freeze([
    {
        value: constants.resultConst.NORMAL_SALES,
        label: '正常销售',
    },
    {
        value: constants.resultConst.STOP_SALES,
        label: '停止销售',
    },
]);

// 养护措施
export const yhMeasureOptions = Object.freeze([
    {
        value: '防尘',
        label: '防尘',
    },
    {
        value: '除尘',
        label: '除尘',
    },
    {
        value: '防潮',
        label: '防潮',
    },
    {
        value: '晾晒',
        label: '晾晒',
    },
    {
        value: '避光',
        label: '避光',
    },
    {
        value: '报损',
        label: '报损',
    },
    {
        value: '温湿度调控',
        label: '温湿度调控',
    },
    {
        value: '防串味',
        label: '防串味',
    },
    {
        value: '防鼠虫',
        label: '防鼠虫',
    },
]);

// 时间段
export const timePhaseOptions = Object.freeze([
    {
        value: constants.timePhaseConst.AM,
        label: '上午',
    },
    {
        value: constants.timePhaseConst.PM,
        label: '下午',
    },
]);

// 调控措施
export const wsMeasureOptions = Object.freeze([
    {
        value: constants.wsMeasureConst.WD_DOW,
        label: '降温',
    },
    {
        value: constants.wsMeasureConst.WD_TOP,
        label: '升温',
    },
    {
        value: constants.wsMeasureConst.SD_ADD,
        label: '加湿',
    },
    {
        value: constants.wsMeasureConst.SD_RED,
        label: '除湿',
    },
]);

// 检查结果
export const checkResultOptions = Object.freeze([
    {
        value: constants.checkResultConst.PASS,
        label: '合格',
    },
    {
        value: constants.checkResultConst.PASS_NO,
        label: '不合格',
    },
]);

// 陈列检查类型
export const displayTypeOptions = Object.freeze([
    {
        value: constants.displayTypeConst.ENV_OUT,
        label: '周边环境',
    },
    {
        value: constants.displayTypeConst.ENV_IN,
        label: '店内环境',
    },
    {
        value: constants.displayTypeConst.STORAGE,
        label: '存放条件',
    },
    {
        value: constants.displayTypeConst.HEALTH,
        label: '卫生情况',
    },
]);

// 严重程度
export const severityOptions = Object.freeze([
    {
        value: constants.severityConst.SERIOUS,
        label: '严重',
    },
    {
        value: constants.severityConst.GENERAL,
        label: '一般',
    },
]);

// 解决结果
export const solveResultOptions = Object.freeze([
    {
        value: constants.solveResultConst.HEAL,
        label: '痊愈',
    },
    {
        value: constants.solveResultConst.TO_GOOD,
        label: '好转',
    },
    {
        value: constants.solveResultConst.NO_GOOD,
        label: '未好转',
    },
    {
        value: constants.solveResultConst.SEQUELA,
        label: '存在后遗症',
    },
    {
        value: constants.solveResultConst.DEATH,
        label: '死亡',
    },
]);

// 处置措施类型
export const measureTypeOptions = Object.freeze([
    {
        value: constants.measureTypeConst.BACK_CLINIC,
        label: '退回门店',
    },
    {
        value: constants.measureTypeConst.BACK_SUPPLIER,
        label: '退回供应商',
    },
    {
        value: constants.measureTypeConst.USELESS,
        label: '报损',
    },
    {
        value: constants.measureTypeConst.STORAGE,
        label: '封存',
    },
    {
        value: constants.measureTypeConst.OTHER,
        label: '其他',
    },
]);

// 对原患疾病影响程度
export const effectLevelOptions = Object.freeze([
    {
        value: constants.effectLevelConst.UNOBVIOUS,
        label: '不明显',
    },
    {
        value: constants.effectLevelConst.PROLONGED,
        label: '病程延长',
    },
    {
        value: constants.effectLevelConst.EXACERBATION,
        label: '病情加重',
    },
    {
        value: constants.effectLevelConst.SEQUELA,
        label: '导致后遗症',
    },
    {
        value: constants.effectLevelConst.DEATH,
        label: '导致死亡',
    },
]);

// 评价
export const evaluateOptions = Object.freeze([
    {
        value: constants.evaluateConst.RELATED,
        label: '相关',
    },
    {
        value: constants.evaluateConst.RELATED_POSSIBLE,
        label: '可能相关',
    },
    {
        value: constants.evaluateConst.UNABLE_JUDGE,
        label: '无法判断',
    },
]);

// 用药类型
export const useMedicinesTypeOptions = Object.freeze([
    {
        value: constants.useMedicinesTypeConst.DOUBT_USED,
        label: '怀疑用药',
    },
    {
        value: constants.useMedicinesTypeConst.TOGETHER_USED,
        label: '并用药品',
    },
]);

// 审批方式
export const approveTypeOptions = Object.freeze([
    {
        value: constants.approveTypeConst.SOME,
        label: '任意一人通过',
    },
    {
        value: constants.approveTypeConst.EVERY,
        label: '全部通过',
    },
]);

// 审批业务类型
export const businessTypeOptions = Object.freeze([
    {
        value: constants.businessTypeConst.goodsReportingLosses,
        label: '报损申请',
    },
    {
        value: constants.businessTypeConst.goodsFirstOperation,
        label: '药品首营申请',
    },
    {
        value: constants.businessTypeConst.goodsOtherGoodsFirstOperation,
        label: '其他商品首营申请',
    },
    {
        value: constants.businessTypeConst.goodsMedicalEquipmentFirstOperation,
        label: '医疗器械首营申请',
    },
    {
        value: constants.businessTypeConst.marketingPriceAdjustment,
        label: '调价申请',
    },
    {
        value: constants.businessTypeConst.goodsSupplierFirstOperation,
        label: '供应商首营申请',
    },
]);

// 是否造成质量事故
export const causeQualityAccidentOptions = Object.freeze([
    {
        value: constants.causeQualityAccidentConst.FALSE,
        label: '否',
    },
    {
        value: constants.causeQualityAccidentConst.TRUE,
        label: '是',
    },
]);

// 召回级别
export const recallLevelOptions = Object.freeze([
    {
        value: constants.recallLevelConst.L1,
        label: '一级',
    },
    {
        value: constants.recallLevelConst.L2,
        label: '二级',
    },
    {
        value: constants.recallLevelConst.L3,
        label: '三级',
    },
]);

// 召回方式
export const recallMethodOptions = Object.freeze([
    {
        value: constants.recallMethodConst.STORE,
        label: '门店召回',
    },
    {
        value: constants.recallMethodConst.FACTORY,
        label: '厂家召回',
    },
]);

// 追回级别
export const recoverLevelOptions = Object.freeze([
    {
        value: constants.recoverLevelConst.L1,
        label: '一级',
    },
    {
        value: constants.recoverLevelConst.L2,
        label: '二级',
    },
    {
        value: constants.recoverLevelConst.L3,
        label: '三级',
    },
]);

// 追回方式
export const recoverMethodOptions = Object.freeze([
    {
        value: constants.recoverMethodConst.STORE,
        label: '门店追回',
    },
    {
        value: constants.recoverMethodConst.FACTORY,
        label: '厂家追回',
    },
]);

// 有无健康证
export const healthCertificateOptions = Object.freeze([
    {
        value: constants.healthCertificateConst.NONE,
        label: '无',
    },
    {
        value: constants.healthCertificateConst.HAVE,
        label: '有',
    },
]);

// 性别
export const sexOptions = Object.freeze([
    {
        value: constants.sexConst.MALE,
        label: '男',
    },
    {
        value: constants.sexConst.FEMALE,
        label: '女',
    },
]);

// 培训方式
export const trainMethodOptions = Object.freeze([
    {
        value: constants.trainMethodConst.ON_LINE,
        label: '线上课程',
    },
    {
        value: constants.trainMethodConst.OFF_LINE,
        label: '线下课程',
    },
]);

// 考核结果
export const assessmentResultOptions = Object.freeze([
    {
        value: constants.assessmentResultConst.PASS,
        label: '合格',
    },
    {
        value: constants.assessmentResultConst.PASS_NO,
        label: '不合格',
    },
]);

// 剂型
export const dosageFormTypeOptions = dosageFormType;

// 基药
export const baseMedicineTypeOptions = Object.freeze([
    {
        value: constants.baseMedicineTypeConst.NATION,
        label: '国家基药',
    },
    {
        value: constants.baseMedicineTypeConst.LOCAL,
        label: '地标基药',
    },
]);

// 处方/OTC
export const otcTypeOptions = Object.freeze([
    {
        value: constants.otcTypeConst.NON_OTC,
        label: '处方药',
    },
    {
        value: constants.otcTypeConst.LEVAL_A_OTC,
        label: '甲类非处方',
    },
    {
        value: constants.otcTypeConst.LEVAL_B_OTC,
        label: '乙类非处方',
    },
]);

// 养护类型
export const maintainTypeOptions = Object.freeze([
    {
        value: constants.maintainTypeConst.NO,
        label: '无需养护',
    },
    {
        value: constants.maintainTypeConst.NORMAL,
        label: '一般养护',
    },
    {
        value: constants.maintainTypeConst.VIP,
        label: '重点养护',
    },
]);

// 储存条件
export const storageTypeOptions = Object.freeze([
    {
        value: constants.storageTypeConst.NORMAL,
        label: '常温',
    },
    {
        value: constants.storageTypeConst.COOL,
        label: '阴凉',
    },
    {
        value: constants.storageTypeConst.COLD,
        label: '冷藏',
    },
    {
        value: constants.storageTypeConst.FROZEN,
        label: '冷冻',
    },
    {
        value: constants.storageTypeConst.DARK,
        label: '避光',
    },
    {
        value: constants.storageTypeConst.SEAL,
        label: '密封',
    },
]);

// 剂量模式
export const specTypeOptions = Object.freeze([
    {
        value: constants.specTypeConst.Dose,
        label: '剂量(成分含量)',
        example: '如：吗丁啉多潘立酮片 10mg*42粒/盒',
    },
    {
        value: constants.specTypeConst.Capacity,
        label: '剂量(容量：成分含量)',
        example: '如：硫酸阿托品注射液 1ml:0.5mg*10支/盒',
    },
]);

// 证照类型
export const certTypeOptions = Object.freeze([
    {
        value: constants.certTypeConst.YYZZ,
        label: '营业执照',
    },
    {
        value: constants.certTypeConst.YPYY_XKZ,
        label: '药品经营许可证',
    },
    {
        value: constants.certTypeConst.YPSC_XKZ,
        label: '药品生产许可证',
    },
    {
        value: constants.certTypeConst.YLZY_XKZ,
        label: '医疗执业许可证',
    },
    {
        value: constants.certTypeConst.GSP_ZS,
        label: 'GSP证书',
    },
    {
        value: constants.certTypeConst.SX_XKZ,
        label: '卫生许可证',
    },
    {
        value: constants.certTypeConst.QXSC_XKZ,
        label: '器械生产许可证',
    },
    {
        value: constants.certTypeConst.DELYLQX_JYBA,
        label: '第二类医疗器械经营备案',
    },
    {
        value: constants.certTypeConst.ZLBZ_XY,
        label: '质量保证协议',
    },
    {
        value: constants.certTypeConst.BJPWS_XKZ,
        label: '保健品卫生许可证',
    },
    {
        value: constants.certTypeConst.SPJY_XKZ,
        label: '食品经营许可证',
    },
    {
        value: constants.certTypeConst.YBZSP_JYBA,
        label: '预包装食品经营备案',
    },
    {
        value: constants.certTypeConst.WTS,
        label: '委托书',
    },
    {
        value: constants.certTypeConst.OTHER,
        label: '其他',
    },
]);
