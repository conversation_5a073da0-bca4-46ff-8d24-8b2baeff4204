<script>
    export default {
        name: 'GoodsCountInput',
        props: {
            component: {
                type: String,
                default: 'abc-flex',
            },
            packageCount: [Number,String],
            pieceCount: [Number,String],
            packageUnit: String,
            pieceUnit: String,
            showPackageCountInput: {
                type: Boolean,
                default: true,
            },
            showPieceCountInput: {
                type: Boolean,
                default: true,
            },
            packageCountInputConfig: {
                type: Object,
                default: () => ({}),
            },
            pieceCountInputConfig: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            currentPackageCount: {
                get() {
                    return this.packageCount;
                },
                set(count) {
                    this.$emit('update:packageCount', count);
                },
            },
            currentPieceCount: {
                get() {
                    return this.pieceCount;
                },
                set(count) {
                    this.$emit('update:pieceCount', count);
                },
            },
        },
        methods: {
            handlePackageCountChange(packageCount) {
                this.$emit('packageCountChange', packageCount);
            },
            handlePieceCountChange(pieceCount) {
                this.$emit('pieceCountChange', pieceCount);
            },
            handleEnterEvent(e) {
                this.$emit('enter', e);
            },
        },
    };
</script>

<template>
    <component :is="component" v-bind="$attrs">
        <abc-input
            v-if="showPackageCountInput"
            v-model.number="currentPackageCount"
            v-abc-focus-selected
            type="number"
            v-bind="packageCountInputConfig"
            @enter="handleEnterEvent"
            @change="handlePackageCountChange"
        >
            <slot name="packageCountInput">
                <div slot="appendInner" :style="`color: ${$store.state.theme.style.T2}`">
                    <span v-if="packageUnit">{{ packageUnit }}</span>
                </div>
            </slot>
        </abc-input>
        <abc-input
            v-if="showPieceCountInput"
            v-model.number="currentPieceCount"
            v-abc-focus-selected
            type="number"
            v-bind="pieceCountInputConfig"
            @enter="handleEnterEvent"
            @change="handlePieceCountChange"
        >
            <slot name="pieceCountInput">
                <div slot="appendInner" :style="`color: ${$store.state.theme.style.T2}`">
                    <span v-if="pieceUnit">{{ pieceUnit }}</span>
                </div>
            </slot>
        </abc-input>
    </component>
</template>
