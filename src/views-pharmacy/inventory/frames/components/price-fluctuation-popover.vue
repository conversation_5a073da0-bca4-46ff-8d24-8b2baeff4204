<!-- 价格波动提示组件 -->
<template>
    <abc-popover
        ref="popoverRef"
        placement="top"
        trigger="hover"
        theme="yellow"
        :arrow-offset="arrowOffset"
        :open-delay="500"
        :disabled="disabled || !changeText"
        :resident-popover="residentPopover"
        style="height: 100%;"
    >
        <div slot="reference" style="height: 100%;">
            <slot :is-rise="isRise" :show-icon="!(disabled || !anomalyText)"></slot>
        </div>

        <abc-flex align="center" justify="space-between" :gap="4">
            <abc-text>
                {{ changeText }}
            </abc-text>
            <abc-tooltip v-if="anomalyText" :disabled="canAdjustPrice ? !disabledAdjustPrice : true" content="暂无调价权限">
                <div>
                    <abc-button type="text" :disabled="disabledAdjustPrice" @click="handleAdjustPrice">
                        {{ canAdjustPrice ? '调整售价' : '调价中' }}
                    </abc-button>
                </div>
            </abc-tooltip>
            <view-cost-price-dialog
                v-if="showHistoryCostPrice && goodsId"
                :id="goodsId"
                ref="viewCostPriceDialogRef"
                slot-style="position: relative;"
                :triangle="false"
                :open-delay="250"
                :arrow-offset="24"
                :margin-left="0"
                trigger="hover"
                :clinic-id="clinicId"
                placement="bottom"
                @show="handleShowCostPopover"
                @hide="handleHideCostPopover"
            >
                <abc-button type="text">
                    历史进价
                </abc-button>
            </view-cost-price-dialog>
        </abc-flex>
    </abc-popover>
</template>

<script>
    import BusinessGoods from '@/views-pharmacy/inventory/core/goods';
    import { mapGetters } from 'vuex';
    import Big from 'big.js';
    // import { isNull } from '@/common/utils';
    import { getSafeNumber } from '@/utils';
    import GoodsBaseAPI from 'api/goods';

    const ViewCostPriceDialog = () => import('views/inventory/common/view-cost-price-dialog.vue');
    import AbcAdjustPriceDialog from 'views/inventory/goods-in/components/adjust-price-dialog/index.js';
    import { formatMoney } from '@abc/utils';
    import useAdjustPriceInfo from 'views/inventory/goods/archives/hook/adjust-price-info';
    import {
        SubPriceFlag,
    } from 'views/common/inventory/constants';
    export default {
        name: 'PriceFluctuationPopover',
        components: {
            ViewCostPriceDialog,
        },
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
            // 当前价格
            useCostPrice: {
                type: [Number, String],
                default: 0,
            },
            // 上次价格-目前获取到的都是大单位进价
            lastPrice: {
                type: [Number, String],
                default: 0,
            },
            goods: {
                type: Object,
                default: () => ({}),
            },
            useUnit: {
                type: String,
                default: '',
            },
            clinicId: String,
            // 批次信息和商品列表处，直接用后端算好的进价变动数据
            /*
                beforePackageCostPrice: 变动前的包装成本价，
                packageCostPrice: 当前的包装成本价，
                costChangeMode: 成本变动模式，1上浮、-1下跌
                costChangeScale: 成本变动比例，百分比不带%符号
                costPriceWarnFlag: 是否有成本变动预警
            */
            costPriceWarnData: {
                type: Object,
                default: null,
            },
            /**
             * 修改价格的来源
             * @type {import('@/views/common/inventory/constants').PriceModifySourceType}
             */
            updatePriceSourceType: {
                type: Number,
            },
            arrowOffset: Number,
            useGoodsConfig: {
                type: Boolean,
                default: false,
            },
            showHistoryCostPrice: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                adjustPriceInfo,
                canAdjustPrice,
                initAdjustPriceInfo,
            } = useAdjustPriceInfo();

            return {
                adjustPriceInfo,
                canAdjustPrice,
                initAdjustPriceInfo,
            };
        },
        data() {
            return {
                goodsInfo: this.goods,
                residentPopover: false,
            };
        },
        computed: {
            ...mapGetters([
                'showSubSetPrice',
                'goodsConfig',
                'isChain',
                'isChainAdmin',
                'isChainSubStore',
                'isSingleStore',
                'isAdmin',
                'isCanOperateGoodsAdjustPriceInInventory',
            ]),
            ...mapGetters('viewDistribute', [
                'featureFeeCompose',
                'featureSupportFeeCategory',
                'viewDistributeConfig',
            ]),
            costPriceWarnPercent() {
                return this.goodsConfig.stockGoodsConfig?.costPriceWarnPercent ?? 0;
            },
            costPriceWarnType() {
                return this.goodsConfig.stockGoodsConfig?.costPriceWarnType || 3;
            },
            anomalyText() {
                if (!this.isWarning) return '';
                if (this.useGoodsConfig && ((this.costPriceWarnType === 1 && !this.isRise) || (this.costPriceWarnType === 2 && this.isRise))) {
                    return '';
                }
                return `进价异动：${this.isRise ? '上涨' : '下跌'}${Math.abs(this.percentage)}% (上次进价：${formatMoney(this.beforePackageCostPrice, false)}/${this.goods.packageUnit})`;
            },
            changeText() {
                if (this.anomalyText) {
                    return this.anomalyText;
                }

                if (this.showHistoryCostPrice) {
                    return this.lastPrice ? `最近进价：${formatMoney(this.lastPrice, false)}/${this.goodsInfo.packageUnit || this.goodsInfo.pieceUnit}` : '最近进价: 无';
                }
                return '';
            },
            currentPrice() {
                return new BusinessGoods(this.goods).getPackageCostPrice(this.useCostPrice, this.useUnit);
            },
            // 是否预警
            isWarning() {
                if (this.costPriceWarnData?.costPriceWarnFlag) return true;

                return this.lastPrice && this.currentPrice && this.lastPrice !== this.currentPrice && Math.abs(this.percentage) > this.costPriceWarnPercent;
            },
            // 是否上涨
            isRise() {
                if (this.costPriceWarnData) return this.costPriceWarnData.costChangeMode === 1;

                return this.isWarning && this.currentPrice > this.lastPrice;
            },
            percentage() {
                if (this.costPriceWarnData) return this.costPriceWarnData.costChangeScale;

                const current = Big(getSafeNumber(this.currentPrice));
                const last = Big(getSafeNumber(this.lastPrice));

                if (current === last) return 0;

                // 下跌情况：最近进价 > 当前价
                if (last.gt(current)) {
                    return last.minus(current).div(last).times(100).toFixed(2);
                }

                // 上涨情况：当前价 > 最近进价
                return current.minus(last).div(last).times(100).toFixed(2);
            },
            beforePackageCostPrice() {
                if (this.costPriceWarnData?.costPriceWarnFlag) {
                    return this.costPriceWarnData.beforePackageCostPrice;
                }
                return this.lastPrice;
            },
            useIndividualPricingModel() {
                return this.viewDistributeConfig.Inventory.useIndividualPricingModel;
            },
            operateGoodsAdjustPrice() {
                return this.viewDistributeConfig.Inventory.operateGoodsAdjustPrice;
            },
            currentSubPriceFlag() {
                return this.goodsInfo?.subClinicPriceFlag ?? SubPriceFlag.UNIFY_FIXED_PRICE;
            },
            disabledAdjustPrice() {
                // 不允许调价，禁用编辑
                if (!this.canAdjustPrice) return true;
                // 药店定价判断
                if (this.useIndividualPricingModel) {
                    // 药店定价权限
                    if (this.operateGoodsAdjustPrice && !this.isCanOperateGoodsAdjustPriceInInventory) {
                        return true;
                    }
                    // 连锁统一定价，子店不允许编辑
                    if (this.currentSubPriceFlag === SubPriceFlag.UNIFY_FIXED_PRICE) {
                        return !this.isAdmin;
                    }
                    // 门店自主定价-总部定价，只有总部能编辑
                    if (this.currentSubPriceFlag === SubPriceFlag.SEPARATE_FIXED_PRICE_AND_ADMIN_CLINIC) {
                        return !this.isChainAdmin;
                    }
                    // 门店自主定价-子店定价，只有子店能编辑
                    if (this.currentSubPriceFlag === SubPriceFlag.SEPARATE_FIXED_PRICE_AND_SUB_CLINIC) {
                        return !this.isChainSubStore;
                    }
                    // 如果不在上面3种情况的都禁用
                    return true;
                }
                if (this.isAdmin) return false; // 总部可编辑

                return !this.showSubSetPrice;
            },
            isShowAdjustPriceButton() {
                return !this.disabledAdjustPrice;
            },
            goodsId() {
                return this.goods?.goodsId || this.goods?.id;
            },
        },
        watch: {
            changeText(text) {
                // 当鼠标在组件上并且有价格变动时，更新一次popper，保证位置正确
                if (text) {
                    if (!this.isUpdatePopper) {
                        this.fetchGoodsInfo();
                    }
                    this.$nextTick(() => {
                        if (this.$refs.popoverRef) {
                            console.log('update popper');
                            this.$refs.popoverRef.updatePopper();
                            this.isUpdatePopper = true;
                        }
                    });
                }
            },
        },
        methods: {
            async fetchGoodsInfo() {
                const res = await GoodsBaseAPI.goods(this.goodsId, this.clinicId, {
                    forPurchase: 1,
                    withStock: 1,
                });
                if (res?.data) {
                    this.goodsInfo = res.data;
                    // 拉取调价信息
                    if (res.data.waitingEffectUpdatePriceItemId) {
                        await this.initAdjustPriceInfo(res.data.waitingEffectUpdatePriceItemId);
                    }
                } else {
                    this.$Toast({
                        type: 'error',
                        message: '查询药品信息失败',
                    });
                }
            },
            handleShowCostPopover() {
                this.residentPopover = true;
            },
            handleHideCostPopover() {
                this.residentPopover = false;
            },
            handleAdjustPrice() {
                new AbcAdjustPriceDialog({
                    goodsData: this.goodsInfo,
                    changePrice: this.changePrice,
                    sourceType: this.updatePriceSourceType,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            changePrice(priceInfo) {
                console.log('priceInfo', priceInfo);
                this.$emit('changePrice', priceInfo);
            },
        },
    };
</script>
