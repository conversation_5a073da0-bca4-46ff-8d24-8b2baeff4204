@import "src/styles/theme.scss";
@import 'src/styles/abc-common.scss';

.therapy-appointment-base-card-wrapper {
    .display-status-wrapper {
        width: 400px;

        .abc-loading-wrapper {
            .cover-wrap {
                border-radius: var(--abc-dialog-border-radius);
            }
        }

        .patient-info-wrapper {
            display: flex;
            align-items: center;
            height: 64px;
            padding-left: 16px;
            line-height: 20px;
            background-image: linear-gradient(117deg, #ffd99a 1%, #fff9f0 100%);
            border-radius: var(--abc-dialog-border-radius) var(--abc-dialog-border-radius) 0 0;

            &.other-color {
                background-image: linear-gradient(117deg, #9bd2fa 1%, #ceebff 100%);
            }

            .title {
                width: 40px;
                font-size: 14px;
                font-weight: 400;
                color: #bc7500;
                text-align: left;

                &.title2 {
                    color: $theme1;
                }
            }

            .patient-info {
                display: inline-flex;
                flex: 1;
                align-items: center;
                font-size: 14px;
                font-weight: bold;
                color: $S1;

                .name {
                    max-width: 70px;
                }

                span {
                    font-size: 14px;

                    & + span {
                        margin-left: 8px;
                    }
                }
            }
        }

        .registration-content {
            padding: 16px;

            .row-line {
                display: flex;
                align-content: flex-start;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;

                & + .row-line {
                    margin-top: 6px;
                }

                &.created-info {
                    margin-top: 6px;
                    margin-bottom: 0;
                    font-size: 14px;
                    font-weight: 400;

                    .row-content {
                        color: $T2;
                    }
                }

                label {
                    display: inline-block;
                    width: 40px;
                    color: $T2;
                    text-align: left;
                }

                .row-content {
                    flex: 1;
                    color: $S1;

                    &.time-status {
                        display: flex;
                        justify-content: space-between;
                        width: 100%;

                        .status {
                            .waiting-sign-in {
                                color: $Y2;
                            }

                            .waiting-diagnose {
                                color: $theme1;
                            }

                            .diagnosed {
                                color: #556d8b;
                            }
                        }
                    }
                }
            }
        }

        .footer-btn-wrapper {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            height: 44px;
            padding-right: 16px;
        }
    }

    .edit-status-wrapper {
        width: 504px;
        background-image: linear-gradient(#fddba1 0, #ffffff 20%, #ffffff 100%);
        border-radius: var(--abc-dialog-border-radius);

        .content-wrapper {
            padding: var(--abc-paddingTB-xxl) var(--abc-paddingLR-xxl);

            .therapy-edit-title {
                height: 42px;
            }
        }

        .therapy-appointment-base-card-form {
            margin-top: var(--abc-space-xxxl);

            .patient-section-wrapper {
                .patient-section__form-wrapper {
                    .patient-mobile-autocomplete {
                        .abc-input__inner {
                            padding-left: 7px !important;
                        }
                    }

                    .patient-display-mobile {
                        width: 172px !important;

                        &.no-card-btn-wrapper {
                            width: 208px !important;
                        }
                    }
                }

                .add-patient-wrapper_flex {
                    &--left {
                        border-right: 1px solid var(--abc-color-P7) !important;
                        border-radius: var(--abc-border-radius-small) !important;

                        &:hover {
                            border-right: 1px solid rgba(2, 10, 26, 0.04) !important;
                        }

                        &:active {
                            border-right: 1px solid rgba(2, 10, 26, 0.08) !important;
                        }
                    }
                }
            }

            .form-content {
                margin-top: var(--abc-space-xxxl);

                .item {
                    display: flex;
                    align-items: center;

                    & + .item {
                        margin-top: 16px;
                    }

                    .abc-form-item {
                        &:not(.space-form-item) {
                            margin: 0;
                        }

                        .abc-form-item-content {
                            .not-exist-products {
                                .abc-input__inner {
                                    border-color: $Y2;
                                }
                            }
                        }
                    }

                    .date-wrapper,
                    .flexible-date-wrapper,
                    .order-no-select-section-wrapper {
                        .abc-input__inner {
                            padding-left: var(--abc-paddingLR-xl) !important;
                            font-weight: 600;

                            &::placeholder {
                                font-weight: normal;
                            }
                        }
                    }
                }

                .additional-info {
                    margin-top: var(--abc-space-xxxl);
                }
            }

            .therapy-doctor-select-section-wrapper,
            .products-list-select,
            .registration-remark,
            .time-range-select-section-wrapper {
                .abc-input__inner {
                    padding-left: var(--abc-paddingLR-xl) !important;
                }
            }
        }

        .footer-btn {
            position: relative;
            height: 64px;
            padding: 0 var(--abc-space-xxl);
        }
    }

    .dropdown-list-wrapper {
        z-index: 1992;
    }
}

.patient-sex-options {
    .option-item-wrapper {
        .abc-option-item {
            padding: 6px;
        }
    }
}

.therapy-appointment-order-no-select-popper,
.therapy-time-range-select {
    left: -249px !important;
}
