<template>
    <abc-dialog
        v-if="showDialog"
        ref="abcDialog"
        v-model="showDialog"
        append-to-body
        size="huge"
        content-styles="padding-top: 0; max-height: 76vh"
        header-style="padding: 20px 0"
        title=" "
    >
        <edit-form
            :sheet-id="sheetId"
            :has-side-bar="false"
            :patient-info="patientInfo"
            :out-disabled="disabled"
            @onLoad="updateDialogHeight"
        ></edit-form>
    </abc-dialog>
</template>

<script>
    import EditForm from 'src/views-dentistry/outpatient/edit-form-crm.vue';

    export default {
        name: 'EditFormDialog',
        components: {
            EditForm,
        },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            sheetId: {
                type: String,
                required: true,
            },
            patientInfo: Object,
        },
        data() {
            return {
                isLoad: false,
                disabled: true,
            };
        },

        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
        },
        methods: {
            updateDialogHeight() {
                if (this.isLoad) return;
                this.$nextTick(() => {
                    const $abcDialog = this.$refs.abcDialog;
                    $abcDialog && $abcDialog.updateDialogHeight();
                    this.isLoad = true;
                    this.disabled = false;
                });
            },
        },
    };
</script>

