<template>
    <abc-container-center class="content-container abc-dentistry__content-container">
        <abc-container-center-top-head
            :class="{ 'has-right': hasBottomExtendRight }"
            :has-bottom-extend-right="hasBottomExtendRight"
        >
            <div class="crm-header-tabs">
                <abc-tabs-v2
                    v-model="curTab"
                    :option="moduleOptions"
                    class="crm_header--tabs"
                    :disable-indicator="moduleOptions.length < 2"
                    :border="false"
                    size="huge"
                    @change="onChangeTab"
                ></abc-tabs-v2>
                <abc-button
                    v-if="allowCrmSystemSetting"
                    style="margin-left: auto;"
                    variant="ghost"
                    icon="set"
                    @click="isShowCrmPatientMemberPowerSettingDialog = true"
                ></abc-button>
            </div>
            <template #bottom-extend>
                <crm-basic-box
                    ref="crmBasicBox"
                    :is-out-patient="isOutpatient || isConsult"
                    :consultants="consultantList"
                    :patient-info="patientInfo"
                    @registration-success="refreshRegistration"
                    @visit-success="refreshVisit"
                    @modifyTags="modifyTags"
                ></crm-basic-box>
            </template>
        </abc-container-center-top-head>
        <!-- 咨询 -->
        <template v-if="isConsult">
            <consult-form @update-crm-consultant-flag="updateCrmConsultant"></consult-form>
        </template>

        <!-- 门诊 -->
        <template v-if="isOutpatient">
            <dentistry-add-form v-if="$route.query.draftId"></dentistry-add-form>
            <edit-form
                v-else-if="$route.query.outpatientId"
                :out-loading="preLoading"
                :sheet-id="$route.query.outpatientId"
                :patient-info="patientInfo"
                sticky-bg-color="#f5f5f5"
            ></edit-form>
            <template v-else>
                <abc-content-empty value="暂无门诊单" style="top: calc(50% - 24px);">
                </abc-content-empty>
                <abc-button v-if="!isChainAdmin" class="dentistry-quick-reception" @click="addOutpatientNew">
                    新接诊
                </abc-button>
            </template>
        </template>

        <abc-layout v-else class="crm-module-main" :style="{ height: isBaseInfoTab || isChronicCareTab || isFamilyDoctor ? 'auto' : '100%' }">
            <div v-if="isBaseInfoTab" class="overview_info--box">
                <overview-info
                    ref="overview-info"
                    :loading="loadingPatientInfo"
                    :patient-id="patientId"
                    :patient-info="patientInfo"
                    :patient-base-info="patientBaseInfo"
                    :cumulative-amount="cumulativeAmount"
                    :patient-family-member="patientFamilyMember"
                    :hidden-operator="true"
                    @fetch-track="fetchTrack"
                    @fetch-patient-info="fetchPatientInfo"
                    @fetch-family-member="fetchPatientFamilyMember"
                    @update-info="handlePatientInfo"
                    @refresh="refreshCrm"
                ></overview-info>
            </div>

            <!-- 慢病康复 -->
            <div v-else-if="isChronicCareTab" class="chronic-care_record--box">
                <chronic-care-record
                    :patient="patientInfo"
                    @change="fetchPatientInfo"
                ></chronic-care-record>
            </div>

            <!-- 家庭医生 -->
            <div v-else-if="isFamilyDoctor" class="family_doctor--box">
                <div class="family_doctor--box-left">
                    <family-doctor
                        :patient-id="patientId"
                        :patient-info="patientInfo"
                    ></family-doctor>
                </div>
                <div class="family_doctor--box-right">
                    <patient-track
                        ref="sidebar-right"
                        :patient-id="patientId"
                    ></patient-track>
                </div>
            </div>
            <div v-else class="crm_record--box" style="height: 100%;">
                <!--影像报告-->
                <crm-package-img
                    v-if="isExamImg"
                    :patient-info="patientInfo"
                    :patient-id="patientId"
                ></crm-package-img>
                <!--影像报告-->
                <!--收费记录-->
                <card-record-list
                    v-else-if="isRecord"
                    ref="card-record-list"
                    :key="patientId"
                    v-model="recordCurrent"
                    :show-registration-btn="!isChainAdmin"
                    :patient-id="patientId"
                ></card-record-list>
                <!--收费记录-->
                <!--加工记录-->
                <crm-package-oral-process
                    v-else-if="isOralProcess"
                    :patient-info="patientInfo"
                    :patient-id="patientId"
                    :show-process-btn="!isChainAdmin"
                ></crm-package-oral-process>
                <!--加工记录-->
                <!--随访记录-->
                <crm-visit-table
                    v-else-if="isCrmVisit"
                    ref="crm-visit-table"
                    :patient-info="patientInfo"
                    :patient-id="patientId"
                    :show-visit-btn="!isChainAdmin"
                >
                </crm-visit-table>
                <!--随访记录-->
            </div>
        </abc-layout>
        <!--随访记录-->
        <crm-patient-member-power-setting-dialog
            v-if="isShowCrmPatientMemberPowerSettingDialog"
            v-model="isShowCrmPatientMemberPowerSettingDialog"
        ></crm-patient-member-power-setting-dialog>
    </abc-container-center>
</template>

<script>
    // lib
    import {
        mapGetters, mapActions,
    } from 'vuex';
    // api
    import CrmAPI from 'api/crm';
    import PatientsAPI from 'src/api/patients.js';
    // utils
    import AbcAccess from '@/access/utils.js';
    import MixinModulePermission from 'views/permission/module-permission';
    import { CrmMainModuleEnum } from 'src/views-dentistry/crm/constants.js';
    import { CrmQlTabsEnum } from 'src/views-dentistry/crm/constants.js';
    import {
        ROLE_DOCTOR_ASSIST_ID,
        ROLE_DOCTOR_ID,
    } from 'utils/constants';
    // components
    import OverviewInfo from 'views/crm/patient-files/overview-info.vue';
    import PatientTrack from 'views/crm/common/package-track/index.vue';
    import ChronicCareRecord from 'src/views/chronic-care/record/index';
    import FamilyDoctor from 'views/crm/patient-files/family-doctor/index.vue';
    const CrmPatientMemberPowerSettingDialog = () => import('views/crm/patient-files/crm-patient-member-power-setting-dialog');
    import CrmPackageImg from 'views/crm/common/package-img/index.vue';
    import CrmPackageOralProcess from 'views/crm/common/package-oral-process/index.vue';
    import CardRecordList from 'views/crm/patient-files/card-record-list.vue';
    import CrmVisitTable from 'views/crm/common/package-visit/crm-visit-table';
    import ConsultForm from '@/views-dentistry/consult/index.vue';
    import CrmBasicBox from '@/views-dentistry/crm/common/crm-basic-card';
    import EditForm from 'src/views-dentistry/outpatient/edit-form-crm.vue';
    const dentistryAddForm = () => import('src/views-dentistry/outpatient/add-form.vue');
    import { isEqual } from 'utils/lodash';
    export default {
        name: 'CrmMain',
        components: {
            ConsultForm,
            OverviewInfo,
            PatientTrack,
            ChronicCareRecord,
            FamilyDoctor,
            CrmPatientMemberPowerSettingDialog,
            CrmPackageImg,
            CardRecordList,
            CrmPackageOralProcess,
            CrmVisitTable,
            CrmBasicBox,
            EditForm,
            dentistryAddForm,
        },
        mixins: [
            MixinModulePermission,
        ],
        inject: ['dentistryQuickList'],
        provide() {
            return {
                crmMain: this,
            };
        },
        data() {
            return {
                qlTab: '',
                curTab: CrmMainModuleEnum.BASE_INFO,
                isShowCrmPatientMemberPowerSettingDialog: false,
                patientId: '',
                patientInfo: {},
                businessStat: {},
                patientFamilyMember: null,
                patientBaseInfo: {
                    outpatientCount: '',
                    retailCount: '',
                    cumulativeAmount: '',
                },
                loadingPatientInfo: false,
                recordCurrent: 'chargeRecord',
                recordList: {
                    [CrmMainModuleEnum.OUPATIENT_HISTORY]: 'outpatientRecord',
                    [CrmMainModuleEnum.CHARGE_HISTORY]: 'chargeRecord',
                    [CrmMainModuleEnum.TREATMENT_HISTORY]: 'treatmentRecord',
                    [CrmMainModuleEnum.INSPECTION_HISTORY]: 'onlyInspectRecord',
                    [CrmMainModuleEnum.EXAMINATION_HISTORY]: 'onlyExaminationRecord',
                    [CrmMainModuleEnum.REGISTRATION_HISTORY]: 'registrationRecord',
                },
                selectedPatientList: null,
                cumulativeAmount: 0,
                preLoading: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'isSingleStore',
                'isChainAdmin',
                'outpatient',
                'userInfo',
                'isCanSeePatientPayAmountInCrm',
            ]),
            ...mapGetters('viewDistribute', [
                'featureFamilyDoctor',
                'featureChronicRecovery',
                'viewDistributeConfig',
            ]),
            ...mapGetters('chronicCare', ['clinicChronicCareProjects']),
            ...mapGetters('consult', ['consultantList']),
            hasBottomExtendRight() {
                return this.isConsult || this.isOutpatient;
            },
            memberShareInfos() {
                return this.patientFamilyMember?.familyPatients || [];
            },
            // 是否基础档案tab
            isBaseInfoTab() {
                return this.curTab === CrmMainModuleEnum.BASE_INFO && this.patientId;
            },
            // 咨询
            isConsult() {
                return this.curTab === CrmMainModuleEnum.CONSULT;
            },
            // 门诊
            isOutpatient() {
                return this.curTab === CrmMainModuleEnum.OUTPATIENT;
            },
            // 是否影像检查
            isExamImg() {
                return this.curTab === CrmMainModuleEnum.MEDICAL_IMAGE;
            },
            // 各种记录
            isRecord() {
                return [
                    CrmMainModuleEnum.CHARGE_HISTORY,
                    CrmMainModuleEnum.TREATMENT_HISTORY,
                    CrmMainModuleEnum.INSPECTION_HISTORY,
                    CrmMainModuleEnum.EXAMINATION_HISTORY,
                    CrmMainModuleEnum.REGISTRATION_HISTORY,
                ].includes(this.curTab);
            },
            // 加工
            isOralProcess() {
                return this.curTab === CrmMainModuleEnum.PROCESS;
            },
            // 随访
            isCrmVisit() {
                return this.curTab === CrmMainModuleEnum.VISIT;
            },
            // 是否慢病康复tab
            isChronicCareTab() {
                return this.curTab === CrmMainModuleEnum.CHRONIC;
            },
            // 是否家庭医生tab
            isFamilyDoctor() {
                return this.curTab === CrmMainModuleEnum.FAMILY_DOCTOR;
            },

            // 慢病康复信息
            chronicArchivesInfo() {
                const { chronicArchivesInfo } = this.patientInfo || {};
                return chronicArchivesInfo || {
                    archivesCount: 0,
                    archivesList: [],
                };
            },
            // 可选项
            moduleOptions() {
                let tabOptions = [
                    {
                        value: CrmMainModuleEnum.BASE_INFO,
                        label: '档案',
                    },
                    {
                        value: CrmMainModuleEnum.REGISTRATION_HISTORY,
                        label: '预约',
                        statisticsNumber: this.businessStat.registrationCount,
                    },
                    {
                        value: CrmMainModuleEnum.CONSULT,
                        label: '咨询',
                        statisticsNumber: this.businessStat.medicalPlanCount,
                    },
                    {
                        value: CrmMainModuleEnum.OUTPATIENT,
                        label: '门诊',
                        statisticsNumber: this.businessStat.outpatientCount,
                    },
                    {
                        value: CrmMainModuleEnum.MEDICAL_IMAGE,
                        label: '影像',
                        statisticsNumber: this.businessStat.imgAttachmentCount,
                    },
                    {
                        value: CrmMainModuleEnum.CHARGE_HISTORY,
                        label: '收费',
                        statisticsNumber: this.businessStat.chargeCount,
                    },
                    {
                        value: CrmMainModuleEnum.INSPECTION_HISTORY,
                        label: '检查',
                        statisticsNumber: this.businessStat.inspectCount,
                    },
                    {
                        value: CrmMainModuleEnum.EXAMINATION_HISTORY,
                        label: '检验',
                        statisticsNumber: this.businessStat.examinationCount,
                    },
                    {
                        value: CrmMainModuleEnum.TREATMENT_HISTORY,
                        label: '治疗',
                        statisticsNumber: this.businessStat.treatmentCount,
                    },
                    {
                        value: CrmMainModuleEnum.PROCESS,
                        label: '加工',
                        statisticsNumber: this.businessStat.processCount,
                    },
                    {
                        value: CrmMainModuleEnum.VISIT,
                        label: '随访',
                        statisticsNumber: this.businessStat.revisitCount,
                    },
                ];

                if (!this.isCanSeePatientPayAmountInCrm) {
                    tabOptions = tabOptions.filter((item) => item.value !== CrmMainModuleEnum.CHARGE_HISTORY);
                }

                if (!this.viewDistributeConfig.CRM.showOralProcessRecord) {
                    tabOptions = tabOptions.filter((item) => item.value !== CrmMainModuleEnum.PROCESS);
                }
                if (this.featureFamilyDoctor && AbcAccess.getPurchasedByKey(AbcAccess.accessMap.FAMILY_DOCTOR) && !this.isChainAdmin) {
                    // 开通了家庭医生，不是连锁总部
                    tabOptions.push({
                        value: CrmMainModuleEnum.FAMILY_DOCTOR,
                        label: '家医',
                    });
                }

                if (this.featureChronicRecovery && this.$store.state.property.chainBasic.isEnableChronicRecovery) {
                    // 处理慢病康复tab是否显示
                    const { archivesCount } = this.chronicArchivesInfo;
                    if (this.clinicChronicCareProjects.length !== 0 || archivesCount > 0) {
                        tabOptions.push({
                            value: CrmMainModuleEnum.CHRONIC,
                            label: '慢病',
                            statisticsNumber: archivesCount,
                        });
                    }
                }
                return tabOptions;
            },
            isClinicAdmin() {
                return this.userInfo.roleId === 1;
            },
            isDoctor() {
                return this.userInfo.roleIds.includes(ROLE_DOCTOR_ID);
            },
            isDoctorAssist() {
                return this.userInfo.roleIds.includes(ROLE_DOCTOR_ASSIST_ID);
            },
            allowCrmSystemSetting() {
                const { allowCrmSystemSetting } = this.viewDistributeConfig.CRM;
                return (this.isChainAdmin || this.isSingleStore) && allowCrmSystemSetting;
            },
        },
        watch: {
            patientFamilyMember: {
                handler (oldValue, newValue) {
                    if (!isEqual(oldValue, newValue)) {
                        this.fetchFamilyBaseInfo();
                    }
                },
                immediate: false,
            },
            '$route.fullPath': {
                async handler() {
                    const {
                        params,
                        query,
                    } = this.$route;
                    const { patientId } = params;
                    if (!patientId) return;
                    const {
                        tab,
                        module,
                    } = query;
                    this.qlTab = tab;
                    this.curTab = module || CrmMainModuleEnum.BASE_INFO;
                },
                immediate: true,
            },
            '$route.params.patientId': {
                async handler(val) {
                    try {
                        if (!val) return;
                        const { query } = this.$route;
                        const { module } = query;
                        this.patientId = val;
                        this.preLoading = true;
                        await Promise.all([
                            this.fetchPatientInfo(val),
                            this.fetchPatientBusinessStat(),
                            this.fetchPatientFamilyMember(),
                        ]);
                        await this.initOutpatient(module);
                    } catch (e) {
                        console.error(e);
                    } finally {
                        this.preLoading = false;
                    }
                },
                immediate: true,
            },
        },
        created() {
            if (this.$store.state.property.chainBasic.isEnableChronicRecovery) {
                this.$store.dispatch('chronicCare/initProjects');
            }
            if (!this.isChainAdmin) {
                this.$store.dispatch('fetchAllDoctorsRegsFee', {
                    allEmployee: 1,
                });
                this.fetchRegistrationFee();
            }
            this.$store.dispatch('consult/updateConsultantList');
            this.$store.dispatch('fetchEmployeeListByPractice');
            this.$store.dispatch('fetchAntimicrobialDrugManagementData');
        },

        methods: {
            ...mapActions(['fetchRegistrationFee']),
            updateCrmConsultant() {
                this.$refs.crmBasicBox?.updateCrmConsultant();
            },
            // 统计的接口 操作谨慎
            async fetchFamilyBaseInfo() {
                if (!this.isBaseInfoTab) {
                    return;
                }
                const patientIds = this.memberShareInfos?.map((item) => {
                    return item?.patientId;
                });
                let clinicId = '';
                if (this.isChainSubStore || this.isSingleStore) {
                    clinicId = this.currentClinic.clinicId;
                }
                const params = {
                    patientIds,
                    clinicId,
                    chainId: this.isSingleStore ? null : this.currentClinic.chainId,
                };
                try {
                    const { data } = await CrmAPI.fetchFamilyBaseInfo(params);
                    this.cumulativeAmount = data?.rows?.map((item) => {
                        return item?.cumulativeAmount || 0;
                    })?.reduce((pre, cur) => {
                        return Number(pre) + Number(cur);
                    }, 0) || 0;
                    const {
                        outpatientCount,
                        cumulativeAmount,
                        retailCount,
                    } = data?.rows?.find((item) => {return item.patientId === this.patientId;}) || {};
                    this.patientBaseInfo.outpatientCount = outpatientCount;
                    this.patientBaseInfo.retailCount = retailCount;
                    this.patientBaseInfo.cumulativeAmount = cumulativeAmount;
                } catch (e) {
                    console.log(e);
                }
            },
            /**
             * 当切换tab时
             * <AUTHOR>
             * @date 2021-01-19
             * @param {Number} index 当前tab索引
             */
            async onChangeTab(index) {
                try {
                    this.curTab = index;
                    if (!this.patientId) return;
                    this.fetchPatientFamilyMember();
                    this.initOutpatient(index);
                    this.fetchPatientBusinessStat();
                    if (this.isRecord) {
                        this.recordCurrent = this.recordList[this.curTab];
                    }
                    const query = {
                        ...this.$route.query,
                        module: this.curTab,
                    };
                    this.$router.replace({
                        query,
                    });
                } catch (e) {
                    console.error(e);
                }
            },
            async initOutpatient(module) {
                if (module !== CrmMainModuleEnum.OUTPATIENT) return;
                try {
                    const query = {
                        ...this.$route.query,
                        module: this.curTab,
                        outpatientId: undefined,
                    };
                    // 今日患者
                    if (
                        this.qlTab === CrmQlTabsEnum.OUTPATIENT && (
                            this.isClinicAdmin ||
                            this.isDoctor ||
                            this.isDoctorAssist
                        )
                    ) {
                        // 管理员、医生、助理可以看待诊单
                        const { selectedItem } = this.outpatient;
                        const { id } = selectedItem || {};
                        query.outpatientId = id;
                    } else {
                        // 其他角色在今日也只能看就诊历史
                        const { data } = await PatientsAPI.fetchHistoryAbs(this.patientId);
                        const {
                            totalCount,
                            result,
                        } = data || {};
                        const newOutpatientSheetId = result[0]?.outpatientSheetId || '';
                        if (totalCount > 0 && query.outpatientId !== newOutpatientSheetId) {
                            query.outpatientId = newOutpatientSheetId;
                        }
                    }
                    this.$router.replace({
                        query,
                    });
                } catch (e) {
                    console.error(e);
                }
            },

            async fetchPatientFamilyMember() {
                // 非首模块不拉取
                if (!this.isBaseInfoTab) {
                    return;
                }
                const { patientId } = this;
                try {
                    const { data } = await CrmAPI.fetchFamilyMember(patientId, { needWxBindStatus: true }) || {};
                    this.patientFamilyMember = data;
                } catch (e) {
                    console.log(e);
                }
            },

            /**
             * 拉取患者详情
             * <AUTHOR>
             * @date 2021-01-19
             */
            async fetchPatientInfo() {
                this.loadingPatientInfo = true;
                try {
                    const params = {
                        wx: 1,
                        chronicArchives: 1,
                        promotionCardList: 1,
                        withBusinessStat: 1, // 业务统计数据
                    };
                    const { data } = await CrmAPI.fetchPatientOverview(this.patientId, params);
                    if (data.id === this.patientId) {
                        this.patientInfo = data;
                        this.updateSelectedPatient(this.patientInfo);
                    }
                } catch (error) {
                    console.log('fetchPatientInfo error', error);
                }
                this.loadingPatientInfo = false;
            },
            async fetchPatientBusinessStat() {
                try {
                    const { data } = await CrmAPI.fetchPatientBusinessStat(this.patientId);
                    this.businessStat = data;
                } catch (e) {
                    console.error(e);
                }
            },
            refreshCrm() {
                this.$refs?.crmBasicBox?.init();
            },
            modifyTags(tags, patientId) {
                const quickList = this.dentistryQuickList.$refs['quick-list']?.$refs['ql-content'];
                quickList && quickList.modifyTags && quickList.modifyTags(tags, patientId);
            },
            handlePatientInfo(newPatientInfo) {
                this.$set(this.patientInfo, 'wxBindStatus', newPatientInfo.wxBindStatus);
                this.$set(this.patientInfo, 'wxStatus', newPatientInfo.wxBindStatus);
                this.fetchPatientInfo();
                this.updateSelectedPatient(this.patientInfo);
            },
            /**
             * 刷新患者轨迹信息
             * <AUTHOR>
             * @date 2021-01-19
             */
            fetchTrack() {
                const refNode = this.$refs['sidebar-right'];
                refNode && refNode.fetch && refNode.fetch();
            },
            /**
             * 更新患者列表，被选中患者的信息
             * <AUTHOR>
             * @date 2021-01-19
             * @param {Object} patientInfo 患者信息
             */
            updateSelectedPatient(patientInfo) {
                const quickList = this.dentistryQuickList.$refs['quick-list']?.$refs['ql-content'];
                quickList && quickList.onUpdateSelectedPatient && quickList.onUpdateSelectedPatient(patientInfo);
            },
            refreshRegistration() {
                this.$refs['card-record-list']?.initFetchParam();
            },
            refreshVisit() {
                this.$refs['crm-visit-table']?.initFetchParam();
            },
            addOutpatientNew() {
                const qlRefVm = this.dentistryQuickList.$refs['quick-list'];
                qlRefVm.addDentistryOutpatientNew(this.patientInfo);
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    #abc-container #abc-container-center.abc-dentistry__content-container {
        #abc-container-center__top-head {
            padding: 0 10px 0 0;

            >div {
                max-width: calc(1020px + var(--rightContainerWidth));
                margin: 0 auto;
            }

            &.has-right {
                padding-right: calc(var(--rightContainerWidth) + 10px);

                >div {
                    max-width: 1020px;
                    margin: 0 auto;
                }

                .cut-line {
                    width: calc(100% + var(--rightContainerWidth)) !important;
                    max-width: calc(100% + var(--rightContainerWidth)) !important;
                }
            }

            .header-content {
                flex-wrap: wrap;
                height: auto;
                padding: 0;

                .crm-header-tabs {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 100%;
                    padding: 0 12px;

                    .abc-tabs.abc-tabs-huge {
                        height: 55px;
                    }
                }
            }

            .crm-basic-card_box {
                height: 47px;
                padding-right: 20px;
            }
        }

        .abc-layout-wrapper.crm-module-main {
            max-width: calc(1020px + var(--rightContainerWidth));
            margin: 0 auto;

            .crm-table_common--box {
                &-title,
                &-table {
                    padding-right: 20px !important;
                }
            }
        }

        .main-content {
            max-width: 1020px;
            padding-right: 20px;
        }

        .dentistry-quick-reception {
            position: relative;
            top: calc(50% - 24px);
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-left: 50%;
            transform: translate3d(-50%, -50%, 0);
        }
    }

    .overview_info--box {
        width: 100%;
        height: 100%;
        padding: 16px 24px;
    }

    .chronic-care_record--box {
        width: 100%;
        padding: 24px;

        .chronic-care-record-wrapper {
            max-width: 1150px;
        }
    }

    .family_doctor--box {
        display: flex;
        height: 100%;

        &-left {
            position: relative;
            flex: 1;
            overflow-x: hidden;
        }

        &-right {
            flex-shrink: 0;
            width: 320px;
            height: 100%;
            border-left: 1px solid $P6;
        }
    }

    @media screen and (max-width: 1024px) {
        .overview_info--box {
            padding: 16px 4px 16px 14px !important;
        }
    }
</style>
