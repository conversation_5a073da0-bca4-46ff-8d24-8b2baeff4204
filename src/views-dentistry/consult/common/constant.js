export const NUM_TO_UPPERCASE = {
    1: '一',
    2: '二',
    3: '三',
    4: '四',
    5: '五',
    6: '六',
    7: '七',
    8: '八',
    9: '九',
    10: '十',
    11: '十一',
    12: '十二',
    13: '十三',
    14: '十四',
    15: '十五',
    16: '十六',
    17: '十七',
    18: '十八',
    19: '十九',
    20: '二十',
    21: '二十一',
    22: '二十二',
    23: '二十三',
    24: '二十四',
    25: '二十五',
    26: '二十六',
    27: '二十七',
    28: '二十八',
    29: '二十九',
    30: '三十',
};

export const PROJECT_TYPE_LIST = [
    {
        name: '正畸',
        value: '0',
    },
    {
        name: '美白',
        value: '1',
    },
    {
        name: '种植',
        value: '2',
    },
    {
        name: '拔牙',
        value: '3',
    },
    {
        name: '根管',
        value: '4',
    },
    {
        name: '全科',
        value: '5',
    },
];

export const CONSULT_TYPE_ENUM = {
    '0': '正畸',
    '1': '美白',
    '2': '种植',
    '3': '拔牙',
    '4': '根管',
    '5': '全科',
};

export const CONSULT_STATUS = {
    FOLLOWING_UP: 0,
    BE_CHARGED: 10,
    LOSING: 20,
    CHARGED: 30,
    REFUND: 40,
};

export const CONSULT_STATUS_ENUM = {
    0: '跟进中',
    10: '待收费',
    20: '已丢单',
    30: '已收费',
    40: '已退费',
};

export const CONSULT_PROJECT_STATUS = {
    NORMAL: 0,
    CLOSED: 1,
};

export const CONSULT_PROJECT_STATUS_ENUM = {
    0: '正常',
    1: '已关闭',
};

export const patientSuggestion = [
    '成交意愿较低',
    '成交意愿较高',
    '比较在意项目金额，认为价格偏贵',
    '治疗方案需要再对比',
];

export const CONSULT_QL_EVENT_STATUS = {
    0: '记录方案',
    1: '记录方案',
    2: '跟进过',
};

export const CONSULT_QL_STATUS_ENUM = Object.freeze({
    FOLLOWUPING: 1, // 跟进中
    WAITING_FOLLOWUP: 10, // 待跟进
});

export const FOLLOWUP_RECORD_TYPE = {
    0: '初诊咨询',
    1: '成交跟进',
    2: '沉睡激活',
    3: '预约失败',
    4: '预约未到店',
    5: '到店未成交',
    6: '已成单',
    7: '已丢单',
};

export const FOLLOWUP_RECORD_TYPE_ENUM = {
    '初诊咨询': 0,
    '成交跟进': 1,
    '沉睡激活': 2,
    '预约失败': 3,
    '预约未到店': 4,
    '到店未成交': 5,
    '已成单': 6,
    '已丢单': 7,
};

export const FOLLOWUP_RECORD_STATUS = {
    FOLLOWUPED: 0, // 已跟进
    WAITING_FOLLOWUP: 1, // 待跟进
};

export const FOLLOWUP_BUSINESS_TYPE = {
    PLAN: 0, // 计划
    RECORD: 1, // 记录
};
