import { moneyDigit } from '@/utils';
import { FEE_TYPE_SCOPE } from '@/views-hospital/settings-common/frames/fee/fee-type/constants.js';
import {
    GoodsSubTypeEnum, GoodsTypeEnum,
} from '@abc/constants';
import { formatTreatmentUnit } from 'src/print/common/utils';
import { isChainAdminClinic } from 'src/views/common/clinic.js';
import { isEmptyObject } from 'utils/lodash';
import { DISABLED } from 'views/inventory/components/social-code-autocomplete/constant';
import { GOODS_TYPE_NAME } from 'views/inventory/constant.js';
import { PriceType } from 'views/common/inventory/constants';

export function goodsFullName(goods) {
    if (!goods) return '';
    let name;
    switch (goods.type) {
        case 1:
            name = goods.medicineCadn;
            if (goods.name) {
                if (name) {
                    name += `（${goods.name}）`;
                } else {
                    name = `${goods.name}`;
                }
            }
            return name;
        default:
            return goods.name;
    }
}

/**
 * @desc needPackageUnit 是否拼接packageUnit
 * <AUTHOR>
 * @date 2020-12-25 10:59:43
 */
export function goodsSpec(goods, useDisplaySpec = true) {
    if (!goods) return '';
    const {
        displaySpec,
        medicineDosageNum,
        medicineDosageUnit,
        pieceNum,
        pieceUnit,
        packageUnit,
        type,
        subType,
        materialSpec,
        extendSpec,
        cMSpec,
    } = goods;
    if ([
        GoodsTypeEnum.EXAMINATION,
        GoodsTypeEnum.TREATMENT,
        GoodsTypeEnum.COMPOSE,
        GoodsTypeEnum.OTHER,
    ].indexOf(type) > -1) {
        return '';
    }

    if (useDisplaySpec && displaySpec) {
        return displaySpec;
    }

    const medicineDosageNumNew = Number(medicineDosageNum) || 0;

    let goodsSep = `${pieceNum || 1}`;

    if (pieceUnit) {
        goodsSep += pieceUnit;
    }

    if (packageUnit) {
        goodsSep += `/${packageUnit}`;
    }

    // 中药
    if (type === 1 && subType === 2) {
        if (cMSpec === '中药颗粒') return `颗粒 ${extendSpec || ''}`;
        if (cMSpec === '中药饮片') return `饮片 ${extendSpec || ''}`;
        if (cMSpec === '非配方饮片') return `非配方饮片 ${extendSpec || ''}`;
        return cMSpec || '';
    }

    // 物资
    if (type === 2 || type === 7) {
        if (materialSpec) {
            goodsSep = `${materialSpec}*${goodsSep}`;
        }
        return goodsSep;
    }

    const spec = medicineDosageNumNew && medicineDosageUnit ? `${medicineDosageNumNew}${medicineDosageUnit}` : '';
    if (spec && goodsSep) {
        return `${spec}*${goodsSep}`;
    }
    return `${spec || ''}${goodsSep || ''}`;
}

/**
 * @desc 商品获取一级分类名称
 * <AUTHOR>
 * @date 2022/5/17 13:43
 */
export function goodsTypeName(goods) {
    if (!goods) return '';
    const {
        type,subType,cMSpec,
    } = goods;
    // 提前return其他类型的商品
    if (GOODS_TYPE_NAME[type]) {
        return GOODS_TYPE_NAME[type];
    }
    const typeName = GOODS_TYPE_NAME[`${type}-${subType}`];
    if (typeName === '中药') {
        return cMSpec || '';
    }
    return typeName;
}
export function goodsDisplaySpec(goods) {
    return goodsSpec(goods);
}

function isFloat(n) {
    return Number(n) !== parseInt(n);
}

export function complexCount(stock, toFix = false) {
    // eslint-disable-next-line no-use-before-define
    return _complexCount(stock, toFix) || 0;
}
// 盘点保留三位小数
export function _complexCount(stock, toFix = false) {
    if (!stock) return '';
    let {
        // eslint-disable-next-line prefer-const
        type, subType, pieceUnit = '', pieceCount, packageCount, packageUnit = '', goods,
    } = stock || {};
    if (packageCount < 0 || pieceCount < 0) {
        return (
            `-${
                complexCount(
                    {
                        type,
                        subType,
                        pieceUnit,
                        pieceCount: Math.abs(pieceCount),
                        packageCount: Math.abs(packageCount),
                        packageUnit,
                        goods,
                    },
                    toFix,
                )}`
        );
    }

    if (goods) {
        packageUnit = packageUnit || goods.packageUnit;
        pieceUnit = pieceUnit || goods.pieceUnit;
        type = type || goods.type;
        subType = subType || goods.subType;
    }

    if (type === 1 && subType === 2) {
        pieceUnit = pieceUnit || 'g';
    }

    pieceCount = Number(pieceCount) || 0;
    packageCount = Number(packageCount) || 0;
    if (toFix) {
        // 盘点如果有小数 存在位数很多的小数 则保留三位小数  整数不需要保留后面的 0
        pieceCount = isFloat(pieceCount) ? pieceCount.toFixed(4) : pieceCount;
        packageCount = isFloat(packageCount) ? packageCount.toFixed(4) : packageCount;
    }
    let out = '';
    if (packageCount > 0) {
        out += packageCount + packageUnit;
    }
    if (pieceCount > 0) {
        out += pieceCount + pieceUnit;
    }
    if (packageCount === 0 && pieceCount === 0) {
        if (type === 1 && subType === 2) {
            out += pieceCount + (pieceUnit || '');
        } else {
            out += packageCount + (packageUnit || '');
        }
    }
    return out || 0;
}

export function null_(input) {
    if (input === null) {
        return '-';
    }
    return input || 0;
}

export function clinicName(organ, showSimpleChainAdminName = true) {
    if (!organ) return '';
    if (showSimpleChainAdminName && isChainAdminClinic(organ)) {
        return '总部';
    }
    if (organ.shortName) {
        return organ.shortName;
    }
    return organ.name;
}

export function goodsTotalCostPrice(item) {
    if (!item || !item.goods) return 0;
    let packageCostPrice;
    const pieceCount = +item.pieceCount || 0,
        packageCount = +item.packageCount || 0,
        pieceNum = +item.pieceNum || +item.goods.pieceNum || 1;
    if (item.stock) {
        packageCostPrice = item.stock.packageCostPrice || item.packageCostPrice || item.goods.packageCostPrice || 0;
    } else {
        packageCostPrice = item.packageCostPrice || item.goods.packageCostPrice || 0;
    }
    return (pieceCount / pieceNum + packageCount) * packageCostPrice;
}

export function goodsTotalPrice(item) {
    if (!item || !item.goods) return 0;
    if (item.originTotalPrice) {
        return Number(item.originTotalPrice);
    }

    const pieceCount = +item.pieceCount || 0;
    const packageCount = +item.packageCount || 0;
    const pieceNum = +item.pieceNum || item.goods.pieceNum || 1;
    const packagePrice = item.packagePrice || item.goods.packagePrice;
    let piecePrice = item.piecePrice || item.goods.piecePrice;
    const dismounting = item.dismounting || item.goods.dismounting;
    if (dismounting === 0 || !piecePrice) {
        piecePrice = packagePrice / pieceNum;
    }
    if (packageCount) {
        const tmpCount = pieceCount / pieceNum;
        return (packageCount + tmpCount) * packagePrice;
    }

    if (pieceCount) {
        return pieceCount * piecePrice;
    }
}

export function isCostPriceAdditionMedicine(goods) {
    return goods.priceType === PriceType.PKG_PRICE_MAKEUP;
}
export function isChineseMedicine(goods) {
    if (!goods) return false;
    return goods.type === 1 && goods.subType === 2;
}

/**
 * @des 是否为中成药
 */
export function isChinesePatentMedicine(goods) {
    return goods.type === 1 && goods.subType === 3;
}

/**
 * @des 是否为西药
 */
export function isWesternMedicine(goods) {
    return goods.type === 1 && goods.subType === 1;
}

/**
 * @desc 是 商品 大类
 * <AUTHOR> Yang
 * @date 2024-11-21 15:39:40
*/
export function isGoods(goods) {
    return goods.type === GoodsTypeEnum.GOODS;
}

/**
 * 售价支持小数点位数
 * 西药、中成药、商品等支持四位小数
 * 商品包括自制成品（type: 7, subType: 1）、保健药品（type: 7, subType: 2）、保健食品（type: 7, subType: 3）、其他商品（type: 7, subType: 4）
 */
export function isSupportDecimalsFourMedicine(goods) {
    if (goods === '' || goods === null || goods === undefined || isEmptyObject(goods)) return false;
    return (goods.type === 1 && goods.subType === 1) || //西药
        (goods.type === 1 && goods.subType === 3) || //中成药
        (goods.type === 7 && goods.subType === 1) || //制成品
        (goods.type === 2 && goods.subType === 1) || //医疗器械
        (goods.type === 7 && goods.subType === 2) || //保健药品
        (goods.type === 7 && goods.subType === 3) || //保健食品
        (goods.type === 7 && goods.subType === 4); //其他商品
}
/**
 * 商品价格规格
 * @priceDigit 价格位数
 */
export function getGoodsPriceStr(goods, price, unit, priceDigit = 5) {
    if (goods) {
        let str = moneyDigit(price, priceDigit);
        str += unit ? `/${unit}` : '';
        return str;
    }
    return '';
}

export function isShortage(item) {
    const packageCount = +item.packageCount || 0;
    const pieceCount = +item.pieceCount || 0;
    const pieceNum = +item.pieceNum || 1;
    let stock = 0;

    const needCount = packageCount * pieceNum + pieceCount;

    if (item.stock) {
        let good =
            item.stocks &&
            item.stocks.find((it) => {
                return it.stockId === item.stockId;
            });
        good = good || {};
        const stockPackageCount = +good.packageCount || 0;
        const stockPieceCount = +good.pieceCount || 0;
        const stockPieceNum = good.pieceNum || 1;
        stock = stockPackageCount * stockPieceNum + stockPieceCount;
    } else {
        item.stocks &&
            item.stocks.forEach((s) => {
                stock += s.packageCount * s.pieceNum + s.pieceCount;
            });
    }
    return needCount > stock;
}
export function count1(num, fractionDigits = 1) {
    num = +num || 0;
    num = `${num}`;
    if (num.indexOf('.') > -1) {
        num = Number(num).toFixed(fractionDigits);
        if (+num === 0) {
            num = 0;
        }
    } else {
        num = Number(num);
    }
    return num;
}

export function goodsTypeStr(goods) {
    if (goods.type === 1) {
        if (goods.subType === 1) {
            return '西药';
        } if (goods.subType === 2) {
            return '中药';
        } if (goods.subType === 3) {
            return '中成药';
        }
        return '';

    } if (goods.type === 2) {
        if (goods.subType === 1) {
            return '医疗器械';
        } if (goods.subType === 2) {
            return '后勤材料';
        } if (goods.subType === 3) {
            return '固定资产';
        }
        return '';

    } if (goods.type === 7) {
        if (goods.subType === 1) {
            return '自制成品';
        } if (goods.subType === 2) {
            return '保健药品';
        } if (goods.subType === 3) {
            return '保健食品';
        } if (goods.subType === 4) {
            return '其他商品';
        }
        return '';

    }
    return '';

}

export function formatDiagnosisTreatmentUnit(item, formatter = '') {
    if (!item) return '';
    if (
        item.type !== GoodsTypeEnum.EXAMINATION &&
        item.type !== GoodsTypeEnum.TREATMENT &&
        item.type !== GoodsTypeEnum.OTHER
    ) {
        return item.unit;
    }
    return formatTreatmentUnit(item.unit, formatter);
}

export function goodsHoverTitle(goods) {
    const {
        type,
        subType,
        medicineCadn,
        name,
        shebaoNationalCode,
        shebaoCityCode,
    } = goods;

    let str = '';

    if (type === GoodsTypeEnum.MEDICINE && subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine) {
        str = medicineCadn || name;
    } else if (medicineCadn) {
        str = medicineCadn + (name ? `(${name})` : '');
    } else {
        str = name;
    }

    if (shebaoNationalCode) {
        str += `\n(国家医保代码:${shebaoNationalCode === DISABLED ? '不刷医保/暂无编码' : shebaoNationalCode})`;
    }
    if (shebaoCityCode) {
        str += `\n(省医保代码:${shebaoCityCode})`;
    }
    return str;
}


/**
 * 获取费用类型可用范围名称
 * @date 2023/5/11 - 15:07:17
 * <AUTHOR>
 *
 * @export
 * @param {*} feeTypeScopeId
 */
export function getFeeTypeScopeName(feeTypeScopeId) {
    const feeTypeScopeIdMap = FEE_TYPE_SCOPE;

    let binaryString = '';

    let decimal = +feeTypeScopeId;

    while (decimal > 0) {
        binaryString = binaryString + (decimal % 2);
        decimal = Math.floor(decimal / 2);
    }

    const name = binaryString.split('').map((item, index) => {
        const feeType = item * (2 ** index);
        return feeTypeScopeIdMap[feeType];
    }).filter((item) => !!item).join('，');

    return name;
}


/**
 * 检查费用类型的使用范围选项是否在当前的使用范围内
 * @date 2023/6/7 - 10:23:48
 * <AUTHOR>
 * @param {Number} scopeId 费用类型使用范围选项
 * @param {Number} scope 费用类型使用范围
 * @returns {Boolean}
 * @export
 */
export function checkScopeOptionIsInScope(scopeId, scope) {
    let decimal = +scope;
    const scopeIds = [];
    let idx = 0;

    while (decimal > 0) {
        const scopeItem = (decimal % 2) * (2 ** idx);
        if (scopeItem) {
            scopeIds.push(scopeItem);
        }
        idx++;
        decimal = Math.floor(decimal / 2);
    }

    return scopeIds.includes(+scopeId);
}
