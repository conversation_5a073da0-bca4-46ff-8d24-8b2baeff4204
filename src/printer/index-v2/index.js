import PrintManager from '@/printer/manager/print-manager';
import { PrintMode } from '@/printer/constants';
import { getPrinterPageSizeReduce } from '@/printer/utils';
const PrinterTaskModule = () => import('@/printer/task');
import PrinterService from '@/printer/service';
import Logger from 'utils/logger';
const PrintPreviewDialogModule = () => import('@/printer/components/preview-dialog');
import ABCPrinterConfig from '@/printer/config';
const printerConstantModule = () => import('@/printer/common/constant');

export default class AbcPrinterV2 {
    static async abcPrint(printOption, uuid) {
        try {
            const { printConfig } = printOption;
            const {
                preview,
                deviceName,
                pageSize,
            } = printConfig;

            if (
                pageSize &&
                !PrintManager.getInstance().mergedPagesByDeviceName(deviceName, printOption.templateKey?.pages).some((it) => it.paper.name === pageSize)
            ) {
                console.warn('当前选择纸张不在模板支持的列表中');
                printConfig.deviceIndex = -1;
            }
            if (printConfig.deviceIndex >= 0 && !PrintManager.getInstance().checkDeviceIndexByDeviceName(printConfig.deviceIndex, deviceName)) {
                console.warn('打印机发生变更');
                printConfig.deviceIndex = -1;
                await AbcPrinterV2.preview(printOption);
            } else {
                if (printOption.isAutoPrint) {
                    // 自动打印默认不预览，但是如果未选中打印机，则需要预览以此选择打印机
                    if (printConfig.deviceIndex === -1) {
                        await AbcPrinterV2.preview(printOption, uuid);
                    } else {
                        await AbcPrinterV2.print(printOption, uuid);
                    }
                } else {
                    if (printOption.isDevTools || preview || printConfig.deviceIndex === -1) {
                        await AbcPrinterV2.preview(printOption, uuid);
                    } else {
                        await AbcPrinterV2.print(printOption, uuid);
                    }
                }
            }
        } catch (e) {
            console.error(e);
        }
    }

    static print(printOption, uuid) {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve) => {
            /**
             * 客户端打印需要对打印机物理边距做兼容处理
             */
            const isElectronPrint = PrintManager.getInstance().isEnableElectronPrint() || printOption.mode === PrintMode.Electron;
            if (isElectronPrint) {
                const { printConfig } = printOption;
                const { pageSizeReduce } = getPrinterPageSizeReduce({
                    deviceName: printConfig.deviceName, pageSizeReduce: printConfig.pageSizeReduce,
                });
                printConfig.pageSizeReduce = pageSizeReduce;
            }

            const { default: PrinterTask } = await PrinterTaskModule();
            const printTask = new PrinterTask(printOption);
            await printTask.initAbcPrintInstance();
            const printService = PrinterService.getPrintService();

            printTask.onPrintLog = () => {
                // 自动打印日志上报
                if (printOption.isAutoPrint) {
                    Logger.report({
                        scene: 'auto_print_pharmacy',
                        data: {
                            scene: 'print_template',
                            uuid,
                            info: '打印完成回调',
                            data: {
                                props: printOption,
                                template: printTask.template,
                            },
                        },
                    });
                }
                // 需要上报的打印日志
                if (printOption.needReportLog && printOption.reportLogData) {
                    Logger.report({
                        scene: printOption.reportLogData.scene,
                        data: {
                            info: '实际打印模板',
                            data: {
                                printData: printOption.reportLogData.data,
                                template: printTask.template,
                                keyId: printOption.reportLogData.keyId,
                            },
                        },
                    });
                }
            };

            const { PRINT_SUCCESS } = await printerConstantModule();
            printService.pushQueue(printTask, () => {
                resolve({
                    status: PRINT_SUCCESS,
                });
            });
        });
    }

    static preview(printOption, uuid) {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve) => {
            if (printOption.needReportLog && printOption.reportLogData) {
                Logger.report({
                    scene: printOption.reportLogData.scene,
                    data: {
                        info: 'static preview 预览的打印机配置',
                        data: {
                            printConfig: JSON.stringify(printOption.printConfig),
                            keyId: printOption.reportLogData.keyId,
                        },
                    },
                });
            }

            const { default: PrinterTask } = await PrinterTaskModule();
            const printTask = new PrinterTask(printOption);
            await printTask.initAbcPrintInstance();

            printTask.onPrintLog = () => {
                // 自动打印日志上报
                if (printOption.isAutoPrint) {
                    Logger.report({
                        scene: 'auto_print_pharmacy',
                        data: {
                            scene: 'auto_print_failed_template',
                            uuid,
                            info: '自动打印失败,手动打印完成回调',
                            data: {
                                props: printOption,
                                template: printTask.template,
                            },
                        },
                    });
                }
                // 需要上报的打印日志
                if (printOption.needReportLog && printOption.reportLogData) {
                    Logger.report({
                        scene: printOption.reportLogData.scene,
                        data: {
                            info: '预览打印模板',
                            data: {
                                printData: printOption.reportLogData.data,
                                template: printTask.template,
                                keyId: printOption.reportLogData.keyId,
                                printConfig: JSON.stringify(printOption.printConfig),
                            },
                        },
                    });
                }
            };

            const {
                PRINT_SUCCESS, PRINT_CANCEL,
            } = await printerConstantModule();

            const { default: PrintPreviewDialog } = await PrintPreviewDialogModule();
            new PrintPreviewDialog({
                printTask,
                needReportLog: printOption.needReportLog,
                reportLogData: printOption.reportLogData,
                printHandler: (printConfig) => {
                    if (printOption.needReportLog && printOption.reportLogData) {
                        Logger.report({
                            scene: printOption.reportLogData.scene,
                            data: {
                                info: '预览弹窗点击打印，触发打印配置更新',
                                data: {
                                    printConfig: JSON.stringify(printConfig),
                                    keyId: printOption.reportLogData.keyId,
                                },
                            },
                        });
                    }
                    Object.assign(printTask.printConfig, printConfig);
                    printTask.getPageWidthAndHeight();

                    if (printOption.needReportLog && printOption.reportLogData) {
                        Logger.report({
                            scene: printOption.reportLogData.scene,
                            data: {
                                info: 'getPageWidthAndHeight 重新计算宽高后的配置',
                                data: {
                                    printConfig: JSON.stringify(printTask.printConfig),
                                    keyId: printOption.reportLogData.keyId,
                                },
                            },
                        });
                    }
                    // 打印后保存打印机设置
                    ABCPrinterConfig.setPrintConfig(printTask.printConfig, printOption.printConfigKey);
                    const printService = PrinterService.getPrintService();
                    printService.pushQueue(printTask, () => {
                        resolve({
                            status: PRINT_SUCCESS,
                        });
                    });
                },
                printCancelHandler: () => {
                    resolve({
                        status: PRINT_CANCEL,
                    });
                },
                printConfigKey: printOption.printConfigKey,
                isDevTools: printOption.isDevTools,
                matchRecommendPagesizeCallback: printOption.matchRecommendPagesizeCallback,
            }).generateDialog();
        });
    }
}
