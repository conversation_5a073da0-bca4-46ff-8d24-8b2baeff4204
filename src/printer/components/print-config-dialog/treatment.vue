<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        content-styles="height: auto; max-height: 588px; padding: 24px;"
        title="打印规则"
        custom-class="print-config-treatment-dialog"
        append-to-body
        :before-close="beforeClose"
    >
        <div class="dialog-content clearfix">
            <div v-if="noConnectionPrint" class="no-install-print">
                <div class="no-install-tip">
                    <i class="icon iconfont cis-icon-jinggao1"></i>
                    请连接打印机！
                </div>
            </div>

            <div class="print-setting-table">
                <abc-excel-table>
                    <div class="table-header">
                        <div class="th" style="width: 92px;">
                            单据类型
                        </div>
                        <div class="th" style="width: 126px;">
                            打印机
                        </div>
                        <div class="th" style="width: 156px;">
                            纸张尺寸
                        </div>
                        <div class="th" style="width: 78px;">
                            打印方向
                        </div>
                        <div class="th" style="width: 78px;">
                            预览模式
                        </div>
                        <div class="th" style="width: 135px;">
                            打印份数
                        </div>
                        <div class="th" style="width: 163px;">
                            有新执行单时自动打印
                        </div>
                    </div>

                    <div class="table-body">
                        <template v-for="(item, index) in printConfigWithKey">
                            <div :key="item.key" class="tr" :class="{ 'print-count-row': item.key === 'medicine-tag' }">
                                <!-- 单据类型 -->
                                <div class="td" :class="{ 'print-count-column': item.key === 'medicine-tag' }" style="width: 92px; padding: 0 10px;">
                                    <div class="label-container">
                                        {{ getLabel(item) }}
                                    </div>
                                </div>
                                <!-- 打印机 -->
                                <div class="td select-input-padding-right-20" :class="{ 'print-count-column': item.key === 'medicine-tag' }" style="width: 126px;">
                                    <abc-select
                                        :key="`printer-select-key-${printerListIsUpdate}`"
                                        v-model="item.deviceIndex"
                                        :width="126"
                                        :inner-width="360"
                                        :index="index"
                                        custom-class="print-preview-printer-select-wrapper"
                                        placeholder="选择打印机"
                                        :setting="isNewPrescriptionVersion && isABCClient()"
                                        setting-text="添加打印机"
                                        setting-icon="a-plus13px"
                                        @change="handleChangeDevice"
                                        @set="handleOpenAddPrinterDriverDialog"
                                    >
                                        <abc-option
                                            v-for="(it) in printerList"
                                            :key="it.deviceIndex"
                                            :value="it.deviceIndex"
                                            :label="it.name"
                                        >
                                            <img
                                                v-if="isTinyPrinter(it.deviceIndex)"
                                                class="print-preview-printer-img-icon"
                                                src="~assets/images/print/printer-tiny-icon.png"
                                                alt=""
                                            />
                                            <img
                                                v-else
                                                class="print-preview-printer-img-icon"
                                                src="~assets/images/print/printer-icon.png"
                                                alt=""
                                            />
                                            <span class="print-preview-printer-select-option-title">{{ it.name + (isTinyPrinter(it.deviceIndex) ? '（小票）' : '') }}</span>
                                            <span v-if="isNewPrescriptionVersion && it.offline" class="print-preview-printer-select-option-offline">脱机</span>
                                        </abc-option>
                                    </abc-select>
                                </div>
                                <!-- 纸张尺寸 -->
                                <div class="td select-input-padding-right-20" :class="{ 'print-count-column': item.key === 'medicine-tag' }" style="width: 156px;">
                                    <abc-select
                                        :key="`printer-select-key-${item.deviceIndex}-${printerListIsUpdate}`"
                                        v-model="item.pageSize"
                                        custom-class="print-page-list"
                                        placeholder="尺寸"
                                        :width="156"
                                        :inner-width="248"
                                        @change="handlePageSizeChange(item)"
                                    >
                                        <div v-for="(it, pageIndex) in getBusinessPageList(item)" :key="pageIndex">
                                            <li v-if="it.isElectronPage && !getBusinessPageList(item)[pageIndex - 1]?.isElectronPage" key="print-dialog-option-border" class="print-dialog-option-border"></li>
                                            <abc-option
                                                :value="it.paper.name"
                                                :label="it.paper.name"
                                            >
                                                {{ it.paper.name }}
                                                <span v-if="it.isRecommend" class="print-recommend-text">推荐</span>
                                            </abc-option>
                                        </div>
                                    </abc-select>
                                </div>
                                <!-- 打印方向 -->
                                <div
                                    class="td"
                                    :class="{ 'print-count-column': item.key === 'medicine-tag' }"
                                    style="width: 78px;"
                                >
                                    <abc-select
                                        v-if="heightLevelList(item)"
                                        v-model="item.pageHeightLevel"
                                        :width="77"
                                        placeholder="布局"
                                        class="spacing-right"
                                        :disabled="noConnectionPrint || disableHeightLevelOrOrient(item)"
                                    >
                                        <abc-option
                                            v-for="level in heightLevelList(item)"
                                            :key="level.key"
                                            :value="level.name"
                                            :label="level.name"
                                        ></abc-option>
                                    </abc-select>
                                    <abc-select
                                        v-else
                                        v-model="item.orient"
                                        placeholder="布局"
                                        :width="77"
                                        class="spacing-right"
                                        :disabled="noConnectionPrint || disableHeightLevelOrOrient(item)"
                                    >
                                        <abc-option
                                            v-for="orient in currentOrientations(item.pageSize)"
                                            :key="orient.value"
                                            :label="orient.label"
                                            :value="orient.value"
                                        ></abc-option>
                                    </abc-select>
                                </div>
                                <!-- 预览模式 -->
                                <div class="td" :class="{ 'print-count-column': item.key === 'medicine-tag' }" style="width: 78px;">
                                    <abc-select
                                        v-model="item.preview"
                                        :width="78"
                                        placeholder="预览"
                                    >
                                        <abc-option label="有预览" :value="1"></abc-option>
                                        <abc-option label="无预览" :value="0"></abc-option>
                                    </abc-select>
                                </div>
                                <!-- 打印份数 -->
                                <div class="td" style="width: 135px;">
                                    <template v-if="item.key === 'medicine-tag'">
                                        <div class="medicine-tag-container">
                                            <div class="medicine-tag-setting">
                                                <abc-button type="text" @click="openMedicineTagDialog(item)">
                                                    设置
                                                </abc-button>
                                            </div>
                                            <!-- 口服 -->
                                            <div>
                                                口服：{{ item.printCountData?.oral?.printCopies || 1 }}份
                                            </div>
                                            <!-- 输液 -->
                                            <div>
                                                <template v-if="item.printCountData?.infusion?.printCopies !== -1">
                                                    输液：{{ item.printCountData?.infusion?.printCopies || 1 }}份
                                                </template>
                                                <template v-else>
                                                    输液：按输液次数
                                                </template>
                                            </div>
                                            <!-- 注射 -->
                                            <div>
                                                注射：{{ item.printCountData?.injections?.printCopies || 1 }}份
                                            </div>
                                            <!-- 雾化 -->
                                            <div>
                                                雾化：{{ item.printCountData?.atomization?.printCopies || 1 }}份
                                            </div>
                                            <!-- 外用 -->
                                            <div>
                                                外用：{{ item.printCountData?.external?.printCopies || 1 }}份
                                            </div>
                                            <!-- 煎服 -->
                                            <div>
                                                <template v-if="item.printCountData?.decoction?.printCopies !== -1">
                                                    煎服：{{ item.printCountData?.decoction?.printCopies || 1 }}份
                                                </template>
                                                <template v-else>
                                                    煎服：按包装规格
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <print-count-input
                                            v-model="item.printCopies"
                                            :width="135"
                                        ></print-count-input>
                                    </template>
                                </div>
                                <!-- 有发药单时自动打印 -->
                                <div class="td" style=" display: flex; align-items: center; justify-content: center; width: 163px;">
                                    <template v-if="!isPurchased">
                                        <abc-popover
                                            trigger="hover"
                                            theme="yellow"
                                            :visible-arrow="false"
                                        >
                                            <template v-if="item.key === 'medicine-tag'">
                                                <abc-switch slot="reference" v-model="newOrderIn.isAutoPrintMedicineInfusionTag" disabled></abc-switch>
                                            </template>
                                            <div>
                                                专业版以上可以使用自动打印，<br />请先升级产品版本。
                                            </div>
                                        </abc-popover>
                                    </template>
                                    <template v-else>
                                        <template v-if="item.key === 'medicine-tag'">
                                            <abc-switch
                                                v-model="newOrderIn.isAutoPrintMedicineInfusionTag"
                                            ></abc-switch>
                                        </template>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </abc-excel-table>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <div class="print-tips">
                注：开启自动打印需在本电脑上保持ABC登陆状态
            </div>
            <abc-button :loading="saveLoading" :disabled="isSaveBtnDisabled" @click="ok">
                保存
            </abc-button>
            <abc-button :loading="saveLoading" type="blank" @click="closeDialog">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import Clone from 'utils/clone';
    import ABCPrinterConfig from '../../config.js';
    import {
        FEE_BILL, FEE_LIST, FEE_SOCIAL,
    } from '@/printer/constants';
    import store from 'src/store';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import {
        isDev, isLocal, isTest,
    } from '@/assets/configure/build-env.js';
    import LocalStore from 'utils/localStorage-handler';
    import AbcAccess from '@/access/utils';
    import PrintManager from '@/printer/manager/print-manager.js';
    import printConfigMixin from '@/printer/components/print-config-dialog/print-config-mixin';

    const _CONFIG_KEY = '_treatment_config_';

    export default {
        name: 'PrintConfigTreatmentDialog',
        mixins: [printConfigMixin],
        props: {
            scene: {
                type: String,
                default: 'treatment',
            },
            isEnableExecuteCertificate: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                newOrderIn: {
                    autoPrint: 0, // 开启自动打印

                    isAutoPrintInfusionExecute: false, // 输液注射单
                    isAutoPrintTreatment: false, // 治疗理疗单
                    isAutoPrintExamination: false, // 检查检验单
                    isAutoPrintMedicineInfusionTag: false, // 用药标签
                    isAutoPrintPatientTag: false, // 患者标签
                },
            };
        },

        computed: {
            // 是否购买自动打印
            isPurchased() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.AUTO_PRINT);
            },
            printConfigWithKey() {
                const viewDistributeConfig = getViewDistributeConfig();
                const { containInspect = true } = viewDistributeConfig.Examination;
                const { allowExamReportPrint } = viewDistributeConfig.Print;

                const _examinationArr = ['examination-report'];
                if (containInspect) {
                    _examinationArr.push('inspect-report');
                }
                _examinationArr.push('examination-tag');
                let _outpatient = ['medical-record', 'prescription', 'treatment-execute', 'infusion-execute', 'medical-certificate'];
                let _childhealth = ['medical-record', 'prescription', 'treatment-execute', 'infusion-execute', 'child-healthy-report'];
                let _cashier = ['cashier', 'dispense', 'prescription', 'infusion-execute', 'treatment-execute', 'examination-tag', 'medicine-tag', 'patient-tag', 'fee-bill', 'fee-list'];
                let _pharmacy = ['dispense', 'prescription', 'infusion-execute', 'medicine-tag', 'patient-tag'];
                const _treatment = ['infusion-execute', 'treatment-execute', 'medicine-tag', 'patient-tag', 'execute-certificate'];
                /**
                 * 针对检查检验做处理
                 * 诊所管家是检查单/检验单
                 * 医院管家是检查申请单/检验申请单
                 */
                if (allowExamReportPrint) {
                    _outpatient.splice(4, 0, 'examine-apply-sheet', 'examination-inspect-apply-sheet');
                    _childhealth.splice(4, 0, 'examine-apply-sheet', 'examination-inspect-apply-sheet');
                    _cashier.splice(6, 0, 'examine-apply-sheet', 'examination-inspect-apply-sheet');
                    _treatment.splice(2, 0, 'examine-apply-sheet', 'examination-inspect-apply-sheet');
                } else {
                    _outpatient.splice(4, 0, 'examine', 'examinationInspect');
                    _childhealth.splice(4, 0, 'examine', 'examinationInspect');
                    _cashier.splice(6, 0, 'examine', 'examinationInspect');
                    _treatment.splice(2, 0, 'examine', 'examinationInspect');
                }

                if (store.getters?.printMedicalDocumentsConfig?.prescription?.header?.templateType &&
                    (window.$abcSocialSecurity.config.isTianjin || isDev || isTest || isLocal)) {
                    _outpatient = _outpatient.filter((item) => {
                        return item !== 'prescription';
                    });
                    _outpatient.push('tianjin-western-prescription');
                    _outpatient.push('tianjin-chinese-prescription');

                    _childhealth = _childhealth.filter((item) => {
                        return item !== 'prescription';
                    });
                    _childhealth.push('tianjin-western-prescription');
                    _childhealth.push('tianjin-chinese-prescription');

                    _cashier = _cashier.filter((item) => {
                        return item !== 'prescription';
                    });
                    _cashier.push('tianjin-western-prescription');
                    _cashier.push('tianjin-chinese-prescription');

                    _pharmacy = _pharmacy.filter((item) => {
                        return item !== 'prescription';
                    });
                    _pharmacy.push('tianjin-western-prescription');
                    _pharmacy.push('tianjin-chinese-prescription');
                }

                const printV2Config = PrintManager.getInstance().getFeature('printConfig');
                if (printV2Config.prescriptionVersion && !store.getters?.printMedicalDocumentsConfig?.prescription?.header?.templateType) {
                    const outpatientIndex = _outpatient.indexOf('prescription');
                    if (outpatientIndex > -1) {
                        _outpatient.splice(outpatientIndex, 1, 'prescriptionV2');
                    }

                    const childhealthIndex = _childhealth.indexOf('prescription');
                    if (childhealthIndex > -1) {
                        _childhealth.splice(childhealthIndex, 1, 'prescriptionV2');
                    }

                    const cashierIndex = _cashier.indexOf('prescription');
                    if (cashierIndex > -1) {
                        _cashier.splice(cashierIndex, 1, 'prescriptionV2');
                    }

                    const pharmacyIndex = _pharmacy.indexOf('prescription');
                    if (pharmacyIndex > -1) {
                        _pharmacy.splice(pharmacyIndex, 1, 'prescriptionV2');
                    }
                }

                const sceneMap = {
                    treatment: _treatment,
                    registration: ['registration'],
                    registrationShiXian: ['registration','registration-tag'],
                    outpatient: _outpatient,
                    childhealth: _childhealth,
                    'outpatient-pr': ['medical-record', 'prescription', 'treatment-execute', 'infusion-execute'],
                    cashier: _cashier,
                    pharmacy: _pharmacy,
                    'pharmacy-dialog': ['dispense', 'prescription', 'infusion-execute', 'medicine-tag', 'patient-tag'],
                    examination: _examinationArr,
                    'goods-in': ['goods-in'],
                    'goods-out': ['goods-out'],
                    'goods-trans': ['goods-trans'],
                    'goods-check': ['goods-check'],
                    'goods-purchase': ['goods-purchase'],
                    'goods-apply': ['goods-apply'],
                };
                const currentScene = sceneMap[this.scene];
                if (!Array.isArray(currentScene)) {
                    console.error('当前场景值未定义');
                    return [];
                }

                const res = currentScene.map((key) => {
                    return this.allPrintConfig.find((it) => it.key === key);
                });
                // 对旧版本数据做兼容
                res.forEach((item) => {
                    if (item.key === 'medicine-tag') {
                        item.printCopies = 1;
                        if (!item.printCountData) {
                            item.printCountData = {
                                // 口服
                                oral: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 注射
                                injections: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 外用
                                external: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 输液
                                infusion: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 雾化
                                atomization: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 煎服
                                decoction: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                            };
                        }
                    }
                });
                return res;
            },
        },
        created() {
            this.getDispensingConfig();
        },
        methods: {
            /**
             * @desc 获取纸张列表
             * @return
             */
            getBusinessPageList(item) {
                if (item.deviceIndex === -1) {
                    return [];
                }
                const { AbcTemplates } = window.AbcPackages;
                let key = this.formatCame(item.key);
                if (item.key === FEE_BILL) {
                    const { format } = store.getters.printGlobalConfig && store.getters.printGlobalConfig.billConfig || {};
                    if (format) {
                        key = this.formatCame(`medical-bill-${format}`);
                    } else {
                        return [];
                    }
                }
                if (item.key === FEE_LIST) {
                    const { format } = store.getters.printGlobalConfig && store.getters.printGlobalConfig.medicalListConfig || {};
                    if (format) {
                        key = this.formatCame(`medical-fee-list-${format}`);
                    } else {
                        return [];
                    }
                }
                if (item.key === FEE_SOCIAL) {
                    key = this.formatCame(`medical-${FEE_SOCIAL}`);
                }
                // 增加纸张缓存机制
                if (!this._cache) {
                    this._cache = {};
                }
                let cacheKey = '';
                // 对检验单和检查单做单独处理
                if (key === 'examine' || key === 'examinationInspect') {
                    cacheKey = `${item.deviceName} ${JSON.stringify(AbcTemplates.examination?.pages ?? [])}`;
                } else if (key === 'examineApplySheet' || key === 'examinationInspectApplySheet') {
                    cacheKey = `${item.deviceName} ${JSON.stringify(AbcTemplates.examinationApplySheet?.pages ?? [])}`;
                } else {
                    cacheKey = `${item.deviceName} ${JSON.stringify(AbcTemplates[key]?.pages ?? [])}`;
                }
                if (this._cache[cacheKey] && !this.printerListIsUpdate) {
                    return this._cache[cacheKey];
                }
                let result = [];
                // 对检验单和检查单做单独处理
                if (key === 'examine' || key === 'examinationInspect') {
                    result = PrintManager.getInstance().mergedPagesByDeviceName(item.deviceName, AbcTemplates.examination?.pages ?? []);
                } else if (key === 'examineApplySheet' || key === 'examinationInspectApplySheet') {
                    result = PrintManager.getInstance().mergedPagesByDeviceName(item.deviceName, AbcTemplates.examinationApplySheet?.pages ?? []);
                } else {
                    result = PrintManager.getInstance().mergedPagesByDeviceName(item.deviceName, AbcTemplates[key]?.pages ?? []);
                }
                this._cache[cacheKey] = result;
                return result;
            },
            async ok() {
                // 对自动打印配置做处理
                const cacheNewOrderIn = Clone(this.newOrderIn);
                // 该参数已舍弃,兼容旧数据
                cacheNewOrderIn.autoPrint = +(cacheNewOrderIn.isAutoPrintInfusionExecute ||
                    cacheNewOrderIn.isAutoPrintTreatment ||
                    cacheNewOrderIn.isAutoPrintExamination ||
                    cacheNewOrderIn.isAutoPrintMedicineInfusionTag ||
                    cacheNewOrderIn.isAutoPrintPatientTag);
                cacheNewOrderIn.isAutoPrintInfusionExecute = +cacheNewOrderIn.isAutoPrintInfusionExecute;
                cacheNewOrderIn.isAutoPrintTreatment = +cacheNewOrderIn.isAutoPrintTreatment;
                cacheNewOrderIn.isAutoPrintExamination = +cacheNewOrderIn.isAutoPrintExamination;
                cacheNewOrderIn.isAutoPrintMedicineInfusionTag = +cacheNewOrderIn.isAutoPrintMedicineInfusionTag;
                cacheNewOrderIn.isAutoPrintPatientTag = +cacheNewOrderIn.isAutoPrintPatientTag;

                const postData = {
                    newOrderIn: cacheNewOrderIn,
                };
                // 保存自动打印配置
                LocalStore.set(_CONFIG_KEY, postData);
                // 保存打印配置
                ABCPrinterConfig.setPrintConfig({
                    prescription: this.prescriptionConfig,
                    ticket: this.ticketConfig,
                    bill: this.billConfig,
                    inventory: this.inventoryConfig,
                    statistics: this.statisticsConfig,
                });

                this.visible = false;
            },

            beforeClose(fn) {
                if (this.isChangePrintCountData) {
                    fn(false);
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '用药标签打印份数还未保存，确定离开？',
                        closeAfterConfirm: true,
                        onConfirm: this.no,
                    });
                } else {
                    fn(true);
                }
            },

            closeDialog() {
                if (this.isChangePrintCountData) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '用药标签打印份数还未保存，确定离开？',
                        closeAfterConfirm: true,
                        onConfirm: this.no,
                    });
                } else {
                    this.no();
                }
            },
            // 从缓存中获取药房配置
            getDispensingConfig() {
                const data = Clone(LocalStore.get(_CONFIG_KEY, true));
                if (data) {
                    if (!data.newOrderIn) data.newOrderIn = {};
                    // 输注单
                    if (data.newOrderIn.isAutoPrintInfusionExecute === undefined) {
                        data.newOrderIn.isAutoPrintInfusionExecute = 0;
                    }
                    data.newOrderIn.isAutoPrintInfusionExecute = !!data.newOrderIn.isAutoPrintInfusionExecute;
                    // 治疗理疗单
                    if (data.newOrderIn.isAutoPrintTreatment === undefined) {
                        data.newOrderIn.isAutoPrintTreatment = 0;
                    }
                    data.newOrderIn.isAutoPrintTreatment = !!data.newOrderIn.isAutoPrintTreatment;
                    // 检查检验单
                    if (data.newOrderIn.isAutoPrintExamination === undefined) {
                        data.newOrderIn.isAutoPrintExamination = 0;
                    }
                    data.newOrderIn.isAutoPrintExamination = !!data.newOrderIn.isAutoPrintExamination;
                    // 用药标签
                    if (data.newOrderIn.isAutoPrintMedicineInfusionTag === undefined) {
                        data.newOrderIn.isAutoPrintMedicineInfusionTag = 0;
                    }
                    data.newOrderIn.isAutoPrintMedicineInfusionTag = !!data.newOrderIn.isAutoPrintMedicineInfusionTag;
                    // 患者标签
                    if (data.newOrderIn.isAutoPrintPatientTag === undefined) {
                        data.newOrderIn.isAutoPrintPatientTag = 0;
                    }
                    data.newOrderIn.isAutoPrintPatientTag = !!data.newOrderIn.isAutoPrintPatientTag;
                    this.newOrderIn = data.newOrderIn;
                }
            },
            canHandleAutoPrint(item) {
                return item.deviceIndex > -1 && item.pageSize && (item.orient || item.pageHeightLevel);
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';
@import "src/styles/mixin";

.print-config-treatment-dialog {
    .abc-dialog-body {
        &::-webkit-scrollbar {
            display: none;
        }
    }

    .print-setting-table {
        .table-header {
            .th {
                &:not(:last-child) {
                    border-right: 1px solid $P6 !important;
                }
            }
        }

        .table-body {
            .tr {
                & + .tr {
                    border-top: 1px solid $P6 !important;
                }

                .td {
                    &:not(:last-child) {
                        border-right: 1px solid $P6 !important;
                    }

                    .medicine-tag-container {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0 10px;

                        &:hover {
                            .medicine-tag-setting {
                                display: flex;
                            }
                        }

                        .medicine-tag-setting {
                            position: absolute;
                            display: none;
                            align-items: center;
                            justify-content: center;
                            width: calc(100% - 20px);
                            height: 100%;
                            background-color: $S2;
                            opacity: 0.89;
                        }
                    }

                    .label-container {
                        display: flex;
                        align-items: center;
                        height: 40px;
                    }
                }

                .select-input-padding-right-20 {
                    .abc-input__inner {
                        padding-right: 20px !important;
                    }
                }

                .print-count-column {
                    align-items: flex-start !important;
                }
            }

            .print-count-row {
                height: 130px !important;
            }
        }
    }

    .dialog-content {
        .auto-print-config {
            margin-top: 24px;

            .select-container {
                display: flex;

                .title {
                    width: 100px;
                    margin-right: 24px;
                }
            }

            .select-pharmacy-container {
                margin-top: 16px;

                .title {
                    float: left;
                    width: 100px;
                    margin-right: 24px;
                }

                .pharmacy-checkbox {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 12px 18px;
                    float: right;
                    width: calc(100% - 124px);

                    .pharmacy-check-box {
                        width: 109px !important;
                        padding: 0 !important;
                        margin: 0 !important;
                        overflow: hidden !important;

                        .abc-checkbox__label {
                            @include ellipsis;
                        }
                    }
                }
            }
        }
    }

    .dialog-footer {
        position: relative;

        .print-tips {
            position: absolute;
            left: 0;
            font-size: 12px;
            line-height: 16px;
            color: $T3;
        }
    }

    .no-install-print {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        margin: 0 auto 16px auto;
        font-size: 16px;
        color: $Y2;
        background-color: $Y4;
        border: 1px solid $Y5;
        border-radius: var(--abc-border-radius-small);

        i {
            color: $Y2;
        }

        span {
            color: $theme2;
            cursor: pointer;
        }

        ul {
            margin: 10px;
        }

        li {
            line-height: 24px;
            color: $T2;
        }
    }

    .installed-print {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        margin: 12px auto;
        font-size: 16px;
        color: $G2;
        background-color: $G4;
        border: 1px solid $G5;
        border-radius: var(--abc-border-radius-small);
    }

    .print-setting-item {
        display: flex;
        align-items: center;
        margin: 12px 0;

        .label {
            width: 100px;
        }

        .abc-select-wrapper,
        .abc-input-wrapper {
            margin-left: 12px;
        }

        .print-copies-unit {
            margin-left: 4px;
        }
    }

    .print-count-select {
        .count-icon {
            position: absolute;
            top: 50%;
            left: 18px;
            z-index: 2;
            margin-top: -7px;
        }
    }
}
</style>
