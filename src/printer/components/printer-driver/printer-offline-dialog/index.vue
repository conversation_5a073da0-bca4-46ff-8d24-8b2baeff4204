<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="打印机脱机指引"
        append-to-body
    >
        <abc-flex vertical style="width: 640px;">
            <abc-tips-card-v2 theme="warning">
                打印机已脱机
            </abc-tips-card-v2>

            <abc-card :border="false">
                <abc-layout>
                    <abc-layout-content>
                        <abc-section>
                            <abc-p>
                                打印机已脱机，请尝试按以下步骤恢复打印机连接：
                            </abc-p>
                        </abc-section>
                        <abc-section>
                            <abc-title level="1">
                                1、检查打印机和电脑连接线是否正常
                            </abc-title>
                            <abc-p gray>
                                若使用USB连接，请检查连接线是否已插入，是否存在松动；若使用无线网络连接，请确保打印机与无线网络连接稳定。
                            </abc-p>
                            <abc-title level="1">
                                2、重启打印机
                            </abc-title>
                            <abc-p gray>
                                关闭打印机并拔下插头，等待30秒，重新插上打印机插头，然后再打开打印机。
                            </abc-p>
                            <abc-title level="1">
                                3、重新安装打印机驱动
                                <abc-link size="large" @click="handleDownloadDriver">
                                    下载驱动
                                </abc-link>
                            </abc-title>
                            <abc-p gray></abc-p>
                            <abc-title level="1">
                                4、若打印机仍无法使用，请重启电脑
                            </abc-title>
                            <abc-p gray></abc-p>
                        </abc-section>
                    </abc-layout-content>
                </abc-layout>
            </abc-card>
        </abc-flex>
    </abc-dialog>
</template>

<script>
    const AddPrinterDriverModalModule = () => import('@/printer/components/printer-driver/add-printer-driver-modal');

    export default {
        name: 'PrinterOfflineDialog',
        data() {
            return {
                visible: false,
            };
        },
        methods: {
            async handleDownloadDriver() {
                const { default: AddPrinterDriverModal } = await AddPrinterDriverModalModule();
                new AddPrinterDriverModal({ fromScene: 'offline-manager' }).generateDialogAsync();
                this.visible = false;
            },
        },
    };
</script>
