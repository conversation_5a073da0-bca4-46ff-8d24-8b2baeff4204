import { getAbcPrintOptions } from '@/printer/print-handler';
import AbcPrinter from '@/printer';
import { PrintMode } from '@/printer/constants';
import PEChargeAPI from 'api/physical-examination/pe-charge';
import { formatDate } from '@abc/utils-date';
import { PEChargeFormTypeEnum } from 'views/physical-examination/constants';
import { formatMoney } from '@/utils';


function maskPhoneNumber(phoneNumber) {
    if (!phoneNumber) {
        return '';
    }

    if (phoneNumber.length !== 11) {
        // 确保手机号为11位
        return '';
    }

    return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
}

export class PhysicalExaminationApi {
    static async printPEPersonalFeeList(payload) {
        const {
            id, clinicName,
        } = payload;
        const { data } = await PEChargeAPI.fetchChargeDetail(id);
        const {
            patientInfo, peSheet, lastChargedEmployeeName, lastChargedTime, firstChargedTime, chargeForms,
        } = data;

        const headers = [
            {
                label: '姓名',
                value: `${patientInfo?.name || ''} ${patientInfo?.sex || ''} ${patientInfo?.age?.year ? `${patientInfo?.age?.year}岁` : ''}`,
            },
            {
                label: '手机号码',
                value: patientInfo?.mobile ? `${maskPhoneNumber(patientInfo?.mobile)}` : '',
            },
            {
                label: '体检单号',
                value: peSheet?.no || '',
            },
        ];


        const list = (chargeForms || []).reduce((res, cur) => {
            if (cur.type === PEChargeFormTypeEnum.PERSONAL_ADDITION) {
                const arr = (cur.items || []).map((o) => {
                    return {
                        name: o.goodsName,
                        type: '增项',
                        price: formatMoney(o.unitPrice),
                    };
                });

                res = [...res, ...arr];
            }

            if (cur.type === PEChargeFormTypeEnum.PE_COMPOSE) {
                res = [...res, {
                    name: cur.name,
                    type: '个人普通套餐',
                    price: formatMoney(cur.receivedFee),
                }];
            }

            return res;
        }, []);

        const result = {
            clinicName,
            headers,
            chargeEmployeeName: lastChargedEmployeeName || '',
            chargeTime: formatDate((lastChargedTime || firstChargedTime), 'YYYY-MM-DD HH:mm'),
            list,
            type: 'personal',
        };
        const printOption = getAbcPrintOptions('体检费用清单', result);

        AbcPrinter.abcPrint({
            ...printOption,
            mode: PrintMode.Electron,
        });
    }

    static async printPEGroupFeeList(payload) {
        const {
            id, clinicName,
        } = payload;

        const { data } = await PEChargeAPI.fetchGroupSettleDetail(id);
        const {
            groupOrderInfo, lastChargedEmployeeName, lastChargedTime, firstChargedTime, settleItems,
        } = data;
        const headers = [
            {
                label: '体检机构',
                value: groupOrderInfo?.peOrganInfoVO?.name || '',
            },
            {
                label: '订单编号',
                value: groupOrderInfo?.orderNo || '',
            },
        ];

        const list = (settleItems || []).map((o) => {
            return {
                name: o.name,
                price: formatMoney(o.totalFee),
                count: o.unitCount,
                type: '团体普通套餐',
            };
        });

        const result = {
            headers,
            clinicName,
            list,
            type: 'group',
            chargeEmployeeName: lastChargedEmployeeName || '',
            chargeTime: formatDate((lastChargedTime || firstChargedTime), 'YYYY-MM-DD HH:mm'),
        };


        const printOption = getAbcPrintOptions('体检费用清单', result);

        AbcPrinter.abcPrint({
            ...printOption,
            mode: PrintMode.Electron,
        });
    }
}
