const OralProcess = () => import('views/oral-process');
const OralProcessTable = () => import('views/oral-process/table.vue');
const OralProcessFactory = () => import('views/oral-process/factory.vue');
import { MODULE_ID_MAP } from 'utils/constants';
import AbcAccess from '@/access/utils';

const OralProcessRoute = {
    path: 'oral-process',
    component: OralProcess,
    name: '@oralProcess',
    meta: {
        name: '加工',
        needAuth: true,
        moduleId: MODULE_ID_MAP.oralProcess,
        visible: (store) => {
            return (
                store.state.viewDistribute.viewFeature.oralProcess ||
                AbcAccess.getAccessByKey(AbcAccess.accessMap.ORAL_PROCESS)
            );
        },
        icon: 'nav_machining_outline',
        selectedIcon: 'nav_machining_fill',
    },
    redirect: {
        name: '@processTable',
    },
    children: [
        {
            path: 'table',
            component: OralProcessTable,
            name: '@processTable',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.oralProcess,
            },
        },

        {
            path: 'factory',
            component: OralProcessFactory,
            name: '@processFactory',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.oralProcess,
            },
        },
    ],
};
export default OralProcessRoute;
