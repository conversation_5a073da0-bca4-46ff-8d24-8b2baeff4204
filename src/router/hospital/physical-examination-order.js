import Layout from 'views/layout.vue';
import { MODULE_ID_MAP } from 'utils/constants.js';
import { routes as OrderTeamRoute } from '@/views/physical-examination/order/team/index';
import { routes as OrderIndividualRoute } from '@/views/physical-examination/order/individual';
export default {
    path: '/physical-examination/order',
    component: Layout,
    name: 'layout',
    meta: {
        needAuth: true,
        menuRoot: true,
        moduleId: MODULE_ID_MAP.physicalExaminationOrder,
    },
    children: [
        OrderIndividualRoute,
        OrderTeamRoute,
    ],
};
