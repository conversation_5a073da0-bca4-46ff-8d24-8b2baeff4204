import Layout from 'views/layout.vue';
import { routesV2 as InspectDiagnosisRoute } from '@/views-hospital/inspect-diagnosis/index.js';
import { routesV2 as InspectRegistrationRoute } from '@/views-hospital/inspect-regist/index.js';
import { routes as InspectScheduleRoute } from '@/views-hospital/inspect-schedule/index.js';
import { MODULE_ID_MAP } from 'utils/constants.js';

export default {
    path: '/physical-examination/inspect',
    component: Layout,
    name: 'layout',
    props: () => ({
        // 控制header展示相关信息
        isCenterNavMenu: () => true,
        isEnableMallMenuItem: () => false,
        isEnableSocialMenuItem: () => false,
        isEnableClinicSelector: () => false,

        // 是否是轻量级layout; 如果为true, 则不会处理socket等信息
        isLightLayout: true,
    }),
    meta: {
        needAuth: true,
        menuRoot: true,
        moduleId: MODULE_ID_MAP.inspect,
    },
    children: [
        InspectRegistrationRoute,
        InspectDiagnosisRoute,
        InspectScheduleRoute,
    ],
};
