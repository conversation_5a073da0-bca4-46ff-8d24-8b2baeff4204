.patients-module {
    .buttons-wrapper {
        margin-top: 24px;
    }

    .timeline {
        position: relative;
        height: 48px;
        margin-top: 16px;
        line-height: 48px;
        border-bottom: 1px solid $P4;

        .timeline-wrapper {
            position: relative;
            height: 48px;
            margin: 0 20px;
            overflow: hidden;
            line-height: 48px;

            &.is-showprenextbtn {
                margin: 0;
            }
        }

        ul {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 48px;
            font-size: 0;
            white-space: nowrap;
            list-style: none;
            transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

            @include clearfix;
        }

        li {
            float: left;
            margin: 0 20px;
            //   padding: 0 16px;
            font-size: 14px;
            color: $T2;
            cursor: pointer;

            &.active {
                height: 48px;
                color: $T1;
                border-bottom: 2px solid $theme2;
            }

            &.showprebtn {
                margin-left: 0;
            }
        }

        .next-btn,
        .pre-btn {
            float: left;
            width: 20px;
            height: 48px;
            padding: 0;
            //   text-align: center;
            font-size: 16px;
            color: $theme2;
            cursor: pointer;

            &:hover,
            &:active {
                background-color: $P1;
            }
        }

        .next-btn {
            position: absolute;
            top: -1px;
            right: 0;
            text-align: right;
        }

        .pre-btn {
            text-align: left;
        }

        .next-btn.is-disabled,
        .pre-btn.is-disabled {
            color: $P1;
            cursor: default;

            &:hover,
            &:active {
                background-color: transparent;
            }
        }

        .pre-btn.is-moving {
            position: relative;

            &::after {
                position: absolute;
                top: 0;
                right: -30px;
                z-index: 1;
                display: block;
                // width: 30px;
                height: 47px;
                content: '';
                background-image: linear-gradient(270deg, #ffffff 0, rgba(255, 255, 255, 0) 100%);
            }
        }

        .next-btn.is-moving {
            &::before {
                position: absolute;
                top: 0;
                left: -30px;
                display: block;
                // width: 30px;
                height: 47px;
                content: '';
                background-image: linear-gradient(90deg, #ffffff 0, rgba(255, 255, 255, 0) 100%);
            }
        }
    }

    .doctor-info {
        position: relative;
        height: 60px;
        line-height: 60px;
        color: $T2;

        .print-btn {
            position: absolute;
            top: 0;
            right: 0;
            float: right;
            width: 55px;
            height: 48px;
            line-height: 48px;
            color: $theme2;
            text-align: right;
            cursor: pointer;
        }

        span {
            margin-left: 20px;
        }
    }

    .charge-info {
        display: flex;
        margin-top: 24px;

        .charge-title {
            width: 120px;
            font-weight: 500;
        }

        .view-more-btn {
            display: inline-block;
            height: 48px;
            line-height: 48px;
            color: $theme2;
            cursor: pointer;
        }

        .refund-icon {
            display: inline-block;
            float: left;
            width: 16px;
            height: 16px;
            margin-top: 2px;
            margin-right: 5px;
            font-size: 12px;
            line-height: 16px;
            color: $T2;
            text-align: center;
            background-color: $P3;
            border-radius: var(--abc-border-radius-small);
        }

        .medical-charge-list {
            flex: 1;
            margin-bottom: 14px;

            .charge-table-title {
                height: 20px;
                margin-bottom: 12px;
                line-height: 20px;
                border-bottom: 0;

                ul {
                    display: flex;

                    li:first-child {
                        flex: 1;
                        color: $T1;
                    }

                    li {
                        color: $T1;
                    }
                }
            }

            .charge-table-content {
                li {
                    display: flex;
                    align-items: center;
                    margin-bottom: 14px;

                    &.refund {
                        color: $T3;

                        > div {
                            &:first-child {
                                .chineseMedicineTips {
                                    color: $T3;
                                }
                            }
                        }
                    }

                    &::after {
                        display: block;
                        height: 0;
                        clear: both;
                        font-size: 0;
                        visibility: hidden;
                        content: " ";
                    }

                    > div {
                        &:first-child {
                            flex: 1;
                        }
                    }
                }

                .chineseMedicine-name {
                    display: inline-flex;
                    width: 33%;
                    height: 20px;
                    margin-top: 0;
                    line-height: 20px;

                    .cadn,
                    .name {
                        flex: 1;
                        font-size: 14px;
                    }

                    .count {
                        display: inline-block;
                        width: 70px;
                        font-size: 14px;
                        text-align: left;

                        span {
                            font-size: 12px;
                            color: $T2;
                        }
                    }

                    &.refund {
                        color: $T3;
                    }
                }

                .chineseMedicineTips {
                    display: block;
                    font-size: 14px;
                    line-height: 20px;
                    color: $T2;
                }
            }

            .charge-total-wrapper {
                position: relative;
                margin-top: 18px;
                text-align: right;

                .des {
                    margin-right: 6px;
                    color: $T2;
                }

                .price {
                    display: inline-block;
                    min-width: 100px;
                }

                .prepend {
                    position: absolute;
                    top: 7px;
                    z-index: 1;
                    width: 32px;
                    font-size: 16px;
                    color: #8a9da5;
                    text-align: center;
                }
            }
        }
    }

    .patient-form {
        .patient-change {
            height: 32px;
            margin-left: 24px;
            font-size: 14px;
            line-height: 32px;
            color: #005ed9;

            i {
                margin-right: 4px;
                font-size: 12px;
            }

            &:hover {
                cursor: pointer;
            }
        }
    }
}
