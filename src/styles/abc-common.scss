@use "sass:math";

@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";


/* 两端对齐 */
@mixin justify($width) {
    width: $width;
    text-align: justify;
    text-align-last: justify;
    text-justify: distribute-all-lines;
}

/* flex简易 */
@mixin flex($flexDirection: row, $justContent: flex-start, $alignItems: flex-start) {
    display: flex;
    flex-direction: $flexDirection;
    align-items: $alignItems;
    justify-content: $justContent;
}

/* 超出省略 */
@mixin ellipsis($lineNum: 1) {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    word-wrap: break-word;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lineNum;
}

/* 超出省略 */
@mixin breakAll() {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    word-wrap: break-word;
    -webkit-box-orient: vertical;
}

//字体缩小到12以下
@mixin minFontSize($size) {
    white-space: nowrap;
    transform: scale(math.div($size, 12));
    transform-origin: left;
}

.base-font-style {
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;
}

@mixin font-alternate {
    font-family: "webfont-alternate" !important;

    @extend .base-font-style;
}

@mixin fontSet($size,$weight) {
    font-size: $size;
    font-weight: $weight;
}

.abc-font-color {
    &-primary {
        color: $B1;
    }

    &-success {
        color: $G1;
    }

    &-warn {
        color: $Y2;
    }

    &-danger {
        color: $R2;
    }

    &-tips {
        color: $T2;
    }
}
