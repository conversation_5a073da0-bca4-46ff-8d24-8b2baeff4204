@font-face {
    font-family: 'robot';
    src: url('//static-common-cdn.abcyun.cn/font/Roboto-Regular.woff') format('woff');
    font-display: swap;
}

@font-face {
    font-family: 'InterMediumNum';
    src: url('//static-common-cdn.abcyun.cn/font/InterMediumNum.woff') format('woff');
    font-display: swap;
}

@font-face {
    font-family: 'webfont-alternate';
    src: url('//cis-static-common.oss-cn-shanghai.aliyuncs.com/font/Mittelschrift-LT-Alternate.woff') format('woff');
    font-display: swap;

    /* chrome、firefox、opera、Safari, Android, iOS 4.2+ */
}

@font-face {
    font-family: 'MyKarlaRegular';
    src: url('//cis-static-common.oss-cn-shanghai.aliyuncs.com/font/abc-tooth-num.ttf') format('truetype');
    font-display: swap;

    /* chrome、firefox、opera、Safari, Android, iOS 4.2+ */
}

@font-face {
    font-family: MyHeiTi;
    font-style: normal;
    font-weight: normal;
    src: local('Microsoft YaHei');
}

@font-face {
    font-family: MyHeiTi;
    font-weight: bold;
    src: url('//static-common-cdn.abcyun.cn/font/MySemiboldHeiTi.ttf') format('truetype');
    font-display: swap;
}

@font-face {
    font-family: MySTZhongsong;
    src: url('//cis-static-common.oss-cn-shanghai.aliyuncs.com/font/MySTZhongsong.ttf') format('truetype');
    font-display: swap;

    /* chrome、firefox、opera、Safari, Android, iOS 4.2+ */
}

@font-face {
    font-family: 'MyReferralTitle';
    src: url('//static-common-cdn.abcyun.cn/font/MyZhenYanHeiTi.ttf') format('truetype');
    font-display: swap;
}
