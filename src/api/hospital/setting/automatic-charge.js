import fetch from 'utils/fetch';

export default {
    async fetchAutomaticChargeConfig() {
        const { data } = await fetch({
            url: '/api/his-charges/auto-charged-rule',
            method: 'get',
        });
        return data;
    },
    async saveAutomaticChargeConfig(data) {
        const res = await fetch({
            url: '/api/his-charges/auto-charged-rule',
            method: 'post',
            data,
        });
        return res?.data;
    },
    async validateAutoChargeDelete(autoChargedItemId) {
        const { data } = await fetch({
            url: `/api/his-charges/auto-charged-rule/${autoChargedItemId}/delete-pre-check`,
            method: 'post',
        });
        return data;
    },
};
