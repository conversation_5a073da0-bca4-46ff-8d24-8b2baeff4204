import { EXAMINATION_TYPE } from '@/utils/constants';
import Qs from 'qs';
import fetch from 'utils/fetch.js';

export default {
    /**
     * @desc 使用chargeSheetId 打印执行单
     * <AUTHOR>
     * @date 2019/07/09 22:15:11
     * 治疗单（ 治疗 + 输液）type = 1
     * 理疗单（ 理疗 ）type = 3
     * 输注单（ 输液 ）type = 4
     * 治疗单（ 治疗 + 理疗 ）type = 5
     */
    async chargeExecutedPrint(id, type) {
        const res = await fetch({
            url: `/api/v2/charges/${id}/print/execute?type=${type}`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc 使用patientOrderId 打印单
     * <AUTHOR>
     * @date 2019/07/09 22:51:14
     * @params patientOrderId, type 1 输液西药执行单 3 治疗理疗执行单
     * @return
     */
    async patientExecutedPrint (patientOrderId, type) {
        const res = await fetch({
            url: `/api/v2/charges/patientorder/${patientOrderId}/print/execute?type=${type}`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc 使用outpatientSheetId获取执行单打印数据
     * <AUTHOR>
     * @date 2019/07/22 13:04:28
     * @params
     * 治疗执行单（包含治疗项和输液项）type = 1
     * 理疗执行单（只包含理疗执行项）type = 3
     * 输注执行单（只包含输液项）type = 4
     * 治疗执行单（包含治疗项和理疗项）type = 5
     */
    async outpatientExecutedPrint(id, type) {
        const res = await fetch({
            url: `/api/v2/outpatients/${id}/print/execute?type=${type}`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },

    /**
     * @desc 门诊处检验申请单打印
     */
    async outpatientExaminationPrint(id, type) {
        const res = await fetch({
            url: `/api/v2/outpatients/${id}/print/examination?type=${type}`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },

    /**
     * @desc 门诊处检验申请单打印
     */
    async chargeExaminationPrint(chargeSheetId, type) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/print/examination?type=${type}`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },

    /**
     * @desc  根据门诊ID获取打印数据
     * <AUTHOR>
     * @date 2019/08/07 11:13:51
     * @params
     * @return
     */
    async prescriptionPrintByOutpatientId(id) {
        const res = await fetch({
            url: `/api/v2/outpatients/${id}/print`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc  根据patientOrderId打印处方
     * <AUTHOR>
     * @date 2019/08/07 11:17:38
     * @params
     * @return
     */
    async prescriptionPrintByPatientOrderId(patientOrderId, sourceSheetId = '') {
        const res = await fetch({
            url: `/api/v2/outpatients/patientorders/${patientOrderId}/print`,
            params: {
                chargeSheetId: sourceSheetId,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
            method: 'get',
            disabledCancel: true,
        });

        return res.data;
    },

    /**
     * @Description: 处方打印支持续方
     * <AUTHOR> Cai
     * @date 2022/07/05 10:48:05
    */
    async prescriptionPrintByPatientOrderIdForXuFang(patientOrderId, sourceSheetId = '', type = 2) {
        const res = await fetch({
            url: `/api/print/outpatients/patientorders/${patientOrderId}/prescription`,
            params: {
                chargeSheetId: sourceSheetId,
                chargeSheetType: type,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
            method: 'get',
            disabledCancel: true,
        });

        return res.data;
    },
    /**
     * @desc 挂号单打印全部有挂号服务提供打印接口
     * <AUTHOR>
     * @date 2019/10/23 17:56:12
     */
    async treatmentPrint(id) {
        const res = await fetch({
            url: `/api/v2/nurses/therapy-registrations/${id}/print-info`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data || {};
    },
    /**
     * @desc 理疗预约单打印
     * <AUTHOR>
     * @date 2019/10/23 17:56:12
     */
    async registrationPrint(patientorderId) {
        const res = await fetch({
            url: `/api/v2/registrations/patientorder/${patientorderId}/print`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data || {};
    },

    /**
     * @desc 浙江挂号发票
     * <AUTHOR>
     * @date 2021-06-21 19:26:28
     * @params
     * @return
     */
    async registrationInvoice(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/print/registration-invoice`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc 患者标签打印
     * <AUTHOR>
     * @date 2020/2/18
     */
    async patientTagPrint(patientOrderId) {
        const res = await fetch({
            url: `/api/v2/charges/patientorder/${patientOrderId}/print/patienttag`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data || {};
    },
    /**
     * @desc 获取打印配置 scope 配置级别，打印为clinic, key 配置关键字
     * <AUTHOR>
     * @date 2019/12/29
     * @params
     * @return
     */
    async fetchPrintConfig(scope, key) {
        const res = await fetch.get(`/api/v2/property/${scope}`,
            {
                params: {
                    key,
                },
                disabledCancel: true,
            });
        return res.data;
    },
    async updatePrintConfig(scope, key, data) {
        const res = await fetch({
            url: `/api/v2/property/${scope}`,
            method: 'post',
            params: {
                key,
            },
            data,
        });
        return res.data;
    },
    async updateMedicalDocumentsTitle(scope, data) {
        const res = await fetch({
            url: `/api/v3/property/batch-update/${scope}`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 打印儿保测试的评测结果
     * <AUTHOR>
     * @date 2020-06-15 10:07:37
     * @params
     * @return
     */
    async printTestResult(recordId) {
        const res = await fetch({
            url: `/api/v2/outpatients/child-care/evaluation/record/${recordId}/result/print`,
            disabledCancel: true,
        });
        return res.data;
    },

    /**
     * @desc 儿保的健康报告打印
     * <AUTHOR>
     * @date 2020-06-15 10:07:37
     * @params
     * @return
     */
    async printHealthReport(outpatientSheetId) {
        const res = await fetch({
            url: `/api/v2/outpatients/child-care/${outpatientSheetId}/health-report/view/print`,
            disabledCancel: true,
        });
        return res.data;
    },

    /**
     * @desc 收费处的发药单打印
     * <AUTHOR>
     * @date 2021-06-07 14:34:07
     * @params
     * @return
     */
    async printDispensingInCharge(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/dispensings/chargesheet/${chargeSheetId}/print-list`,
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc 收费处的退药单打印
     */
    async printDispensingUndispense(chargeSheetId) {
        const res = await fetch.post(`/api/v2/dispensings/chargesheet/${chargeSheetId}/print-undispense-list`,{},{
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc 发药的退药单打印
     */
    async printDispensingsPrintUndispense(id,data) {
        const res = await fetch.post(`/api/v2/dispensings/${id}/print-undispense`,data, {
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc 退费单打印
     * <AUTHOR>
     * @date 2021-07-08 17:54:49
     */
    async printCashierRefund(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/print/refund`,
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc 用药标签打印
     */
    async printMedicineTag(dispensingId) {
        const res = await fetch({
            url: `/api/v2/dispensings/${dispensingId}/print/label`,
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc 发票预览
     * type 发票类型  0 正常开票 1 冲红  2 重新开票
     * registrationInvoiceType: 0 正常发票  1 杭州挂号发票
     */
    async printInvoicePreview(chargeSheetId, type, registrationInvoiceType = 0) {
        registrationInvoiceType = isNaN(registrationInvoiceType) ? 0 : registrationInvoiceType;
        const res = await fetch({
            url: `/api/print/charges/invoice/${chargeSheetId}/print/preview?type=${type}&registrationInvoiceType=${registrationInvoiceType}`,
        });
        return res.data;
    },
    /**
     * @Description: 住院发票预览
     * <AUTHOR> Cai
     * @date 2022/07/20 18:08:40
    */
    async printInvoicePreviewHospital(hospitalSheetId, type) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/invoice/${hospitalSheetId}/print/preview?type=${type}`,
        });
        return res.data;
    },
    /**
     * @desc 发票预览
     * id 社保信息id
     * type 发票类型  0 正常开票 1 冲红  2 重新开票
     */
    async printShebaoInvoicePreview(id, type) {
        const res = await fetch({
            url: `/api/v2/shebao/external-outpatient/invoice/${id}/print/preview?type=${type}`,
        });
        return res.data;
    },

    // 打印服务提供的接口
    /**
     * @desc 输注单接口
     * <AUTHOR>
     * @date 2022-05-26 17:17:01
     * formIdList 收费formItemIdList
     */
    async printChargeInfusion(id, type, formIdList, disabledCancel = false) {
        const res = await fetch({
            url: `/api/print/charges/${id}`,
            params: {
                type,
                formIdList,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { arrayFormat: 'indices' });
            },
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },
    /**
     * @desc 处方打印接口
     * <AUTHOR>
     * @date 2022-05-26 17:17:01
     * formIdList 门诊formItemIdList
     */
    async printOutpatientPrescription(patientOrderId, formIdList, sourceSheetId, pharmacyNoList, disabledCancel = false) {
        const res = await fetch({
            url: `/api/print/outpatients/patientorders/${patientOrderId}/prescription`,
            params: {
                formIdList,
                chargeSheetId: sourceSheetId,
                pharmacyNoList,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { arrayFormat: 'indices' });
            },
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },
    /**
     * @desc 用药标签打印接口
     * <AUTHOR>
     * @date 2022-05-26 17:17:01
     * formIdList 发药formItemIdList
     */
    async printDispensingMedicineTag(dispensingId, formIdList, disabledCancel = false) {
        const res = await fetch({
            url: `/api/print/dispensings/${dispensingId}/label`,
            params: {
                formIdList,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { arrayFormat: 'indices' });
            },
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },
    /**
     * @desc 发药小票打印接口
     * <AUTHOR>
     * @date 2022-05-26 17:17:01
     * formIdList 发药formItemIdList
     */
    async printDispensingTicket(dispensingId, formIdList, disabledCancel = false) {
        const res = await fetch({
            url: `/api/print/dispensings/${dispensingId}`,
            params: {
                formIdList,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { arrayFormat: 'indices' });
            },
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },

    /**
     * @desc 收费用药标签打印接口
     * <AUTHOR>
     */
    async printChargeMedicineTag(chargeSheetId, disabledCancel = false) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/label/print`,
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },


    /**
     * 检查检验申请单打印
     * @date 2023/4/20 - 19:02:52
     * <AUTHOR>
     *
     * @async
     * @param {*} patientOrderId
     * @returns {*}
     */
    async printExamApplySheet(patientOrderId, params) {
        const res = await fetch({
            url: `/api/v2/examinations/apply-sheet/print/${patientOrderId}`,
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * @description 样本条码打印预览数据
     * @param {String} chargeSheetId 收费单id
     */
    async printExaminationBarCode(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/examinations/print-views/by-charge-sheet-id/${chargeSheetId}`,
            method: 'get',
            params: {
                type: EXAMINATION_TYPE.EXAMINATION,
            },
        });
        return res.data;
    },


    /**
     * 检查检验申请单打印 - 收费处
     * @date 2023/10/12 - 18:44:14
     * <AUTHOR>
     *
     * @async
     * @param {*} chargeSheetId
     * @param {*} params
     * @returns {unknown}
     */
    async printExamApplySheetByChargeId(chargeSheetId, params) {
        const res = await fetch({
            url: `/api/v2/examinations/apply-sheet/print-charge/${chargeSheetId}`,
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * 获取收费单的皮试结果
     * @param {string} chargeSheetId 收费单id
     * @param {boolean} disabledCancel 路由改变是否取消改请求
     */
    async getAstResultByCharge(chargeSheetId, disabledCancel = false) {
        if (!chargeSheetId) {
            console.warn('没有收费单id,获取皮试结果失败');
            return;
        }
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/ast-result`,
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },
    /**
     * 获取发药单的皮试结果
     * @param {string} dispensingSheetId 发药单id
     * @param {boolean} disabledCancel 路由改变是否取消改请求
     */
    async getAstResultByDispensing(dispensingSheetId, disabledCancel = false) {
        if (!dispensingSheetId) {
            console.warn('没有发药单id,获取皮试结果失败');
            return;
        }
        const res = await fetch({
            url: `/api/v2/dispensings/${dispensingSheetId}/ast-result`,
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },


    /**
     * 获取检查检验单打印数据
     * @date 2024/1/7 - 20:54:15
     * <AUTHOR>
     *
     * @async
     * @param {*} id
     * @returns {*}
     */
    async getExaminationPrintData(id) {
        const url = `/api/v2/examinations/print/${id}`;

        const res = await fetch({
            url,
            method: 'get',
        });
        return res.data;
    },

    async getInspectLabelPrintData(id) {
        const url = `/api/v2/examinations/apply-sheet/detail/print/${id}`;

        const res = await fetch({
            url,
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 发药单打印日志
     */
    async printDispensingLog(dispensingId,data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${dispensingId}/print/log`,
            method: 'put',
            data,
            disabledCancel: true,
        });
        return res.data;
    },

    /**
     * @desc 积分抵扣单打印日志
     */
    async fetchPointVoucherPrintData(patientId, pointsBillId) {
        const res = await fetch({
            url: `api/v2/crm/patients/${patientId}/points/bills/${pointsBillId}/print`,
            method: 'get',
        });
        return res?.data || {};
    },
    /**
     * @desc: 新增或修改收费业务的打印记录
     * @author: ff
     * @time: 2025/4/21
     */
    async addOrUpdateChargePrintLog(data) {
        const res = await fetch({
            url: '/api/v2/charges/business/print-record',
            method: 'post',
            data,
        });
        return res.data;

    },
};
