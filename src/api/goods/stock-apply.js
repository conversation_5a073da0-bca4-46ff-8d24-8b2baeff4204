// 领用
import fetch from 'utils/fetch';
import { createBlobForDownload } from '@/utils';
import Qs from 'qs';
import { exportFileByAxios } from 'utils/excel';
// import { exportListHint } from 'utils/excel';

export default {
    async list(data) {
        const ret = await fetch.post('/api/v3/goods/stocks/reception/orders/list', data);
        return ret?.data?.data;
    },

    async apply(data) {
        const ret = await fetch.post('/api/v3/goods/stocks/reception/orders', data);
        return ret?.data?.data;
    },

    async exportList(params) {
        const res = await fetch.post('/api/v3/goods/stocks/reception/orders/export', params, {
            responseType: 'arraybuffer',
            disabledCancel: true,
        });

        createBlobForDownload(res, {
            replaceStr: 'filename=',
        });

        return res;
    },

    async getById(id, params) {
        const ret = await fetch.get(`/api/v3/goods/stocks/reception/orders/${id}`,{
            params,
        });
        return ret?.data?.data;
    },
    // 确认入库
    async inConfirm(id, data) {
        const ret = await fetch.put(`/api/v3/goods/stocks/reception/orders/in-confirm/${id}`, data);
        return ret?.data?.data;
    },

    // 确认出库
    async outConfirm(id, data) {
        const ret = await fetch.put(`/api/v3/goods/stocks/reception/orders/out-confirm/${id}`, data);
        return ret?.data?.data;
    },
    // 审核
    async reviewOrder(id, data) {
        const ret = await fetch.put(`/api/v3/goods/stocks/reception/orders/review/${id}`, data);
        return ret?.data?.data;
    },
    // 撤回
    async revokeOrder(id, data) {
        const ret = await fetch.put(`/api/v3/goods/stocks/reception/orders/revoke/${id}`, data);
        return ret?.data?.data;
    },

    // 导出
    async exportById(id) {
        const url = `/api/v3/goods/stocks/reception/orders/${id}/export`;
        return exportFileByAxios({
            url,
        });
    },

    //     可退领用单列表
    async getRefundList(data) {
        const ret = await fetch.post('/api/v3/goods/stocks/reception/orders/orders-for-stock-out', data);
        return ret?.data?.data;
    },

    // 查询入库单草稿列表
    async getGoodsApplyDraftList(params) {
        const res = await fetch({
            url: '/api/v3/goods/stocks/reception/orders/draft',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res && res.data;
    },

    // 新增入库单草稿
    async createGoodsApplyDraft(data) {
        const res = await fetch({
            url: '/api/v3/goods/stocks/reception/orders/draft',
            method: 'post',
            data,
        });
        return res && res.data;
    },

    /**
     * 获取入库单草稿详情
     * @param draftId 草稿 id
     */
    async getGoodsApplyDraftDetail(draftId) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/reception/orders/draft/${draftId}`,
            method: 'get',
        });
        return res && res.data;
    },

    /**
     * 修改入库单草稿
     * @param draftId 草稿 id
     * @param data 草稿数据
     */
    async updateGoodsApplyDraft(draftId, data) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/reception/orders/draft/${draftId}`,
            method: 'put',
            data,
        });
        return res && res.data;
    },

    /**
     * 删除入库单草稿
     * @param draftId 草稿 id
     */
    async deleteGoodsApplyDraft(draftId) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/reception/orders/draft/${draftId}`,
            method: 'delete',
        });
        return res && res.data;
    },
};
