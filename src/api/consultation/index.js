//https://dev.abczs.cn/swagger-ui/?urls.primaryName=consultation#/
import BaseAPI from 'api/base-api.js';
import fetch from 'utils/fetch.js';
import Qs from 'qs';

export default class ConsultationAPI extends BaseAPI {

    /**
     * @desc 获取会诊ql列表
     * <AUTHOR>
     * @date 2023-02-03 14:47:52
     * @param {object} params
     * @param {string} params.keyword
     * @param {number} params.offset
     * @param {number} params.limit
     * @param {number} params.status
     * @param {number} params.tab
     * @param {number} params.patientOrderId 可查指定patientOrderId的患者会诊列表
     * @param {boolean} disabledCancel
     */
    static async fetchQuickList(params, disabledCancel = false) {
        const res = await fetch({
            url: '/api/consultation/list',
            params,
            disabledCancel,
            paramsSerializer (p) {
                return Qs.stringify(p, { indices: false });
            },
        });
        return res.data;
    }

    /**
     * @desc 创建会诊
     * <AUTHOR>
     * @date 2023-02-03 14:47:52
     */
    static async create(data) {
        const res = await fetch({
            url: '/api/consultation',
            method: 'post',
            data,
        });
        return res.data;
    }

    /**
     * @desc 修改会诊单
     * <AUTHOR>
     * @date 2023-02-03 14:47:52
     * @param {string} id - 会诊id
     * @param {object} data
     */
    static async update(id, data) {
        const res = await fetch({
            url: `/api/consultation/${id}`,
            method: 'put',
            data,
        });
        return res.data;
    }

    /**
     * @desc 获取会诊单详情
     * <AUTHOR>
     * @date 2023-02-03 14:47:52
     * @param {string} id - 会诊id
     */
    static async fetchDetail(id) {
        const res = await fetch({
            url: `/api/consultation/${id}`,
        });
        return res.data;
    }

    /**
     * @desc 更新会诊单状态
     * <AUTHOR>
     * @date 2023-02-03 14:47:52
     * @param {string} id - 会诊id
     * @param {object} data
     * @param {object} data.action
     */
    static async updateStatus(id, data) {
        const res = await fetch({
            url: `/api/consultation/${id}/status`,
            method: 'put',
            data,
        });
        return res.data;
    }

    /**
     * @desc 获取会诊记录列表
     * <AUTHOR>
     * @date 2023-08-31 19:21:22
     * @param {object} params
     * @param {string} params.consultationSheetId
     * @param {number} params.offset
     * @param {number} params.limit
     * @param {number} params.type
     */
    static async fetchRecords(params) {
        const res = await fetch({
            url: '/api/consultation/record/list',
            params,
            paramsSerializer (p) {
                return Qs.stringify(p, { indices: false });
            },
        });
        return res.data;
    }

    /**
     * @desc 提交会诊记录
     * <AUTHOR>
     * @date 2023-08-31 19:21:22
     * @param {object} data
     * @param {string} data.consultationSheetId
     * @param {string} data.content
     * @param {number} data.type
     */
    static async createRecord(data) {
        const res = await fetch({
            url: '/api/consultation/record',
            method: 'POST',
            data,
        });
        return res.data;
    }

    /**
     * @desc 删除会诊记录
     * <AUTHOR>
     * @date 2023-08-31 19:24:47
     * @param {string} id - 记录id
     */
    static async deleteRecord(id) {
        const res = await fetch({
            url: `/api/consultation/record/${id}`,
            method: 'DELETE',
        });
        return res.data;
    }

}
