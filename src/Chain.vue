<template>
    <div
        id="app"
        :class="[
            {
                'abc-app-is-full-screen': isFullScreen,
            },
            {
                'abc-app-has-left-nav': isFixedLeftNav
            }
        ]"
    >
        <router-view></router-view>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { AppEvents } from '@/core/index.js';
    import { logoutConfirm } from 'views/common/login-optimize.js';
    import ExpireAlert from 'views/edition/expire-alert/index.js';
    import { EditionErrorCode } from 'views/edition/configs/constants';

    export default {
        name: 'Chain',
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isFullScreen() {
                return this.viewDistributeConfig.isFullScreen;
            },
            isFixedLeftNav() {
                return this.viewDistributeConfig.AppHeader.isFixedLeftNav;
            },
        },
        created() {
            this.$app.on(AppEvents.APP_EVENT_UN_AUTH, (msg) => {
                logoutConfirm(msg);
            });
            this.$app.on(AppEvents.APP_EVENT_EDITION_EXPIRE, ({
                code, message,
            }) => {
                ExpireAlert.getInstance({
                    props: {
                        isExpired: code === EditionErrorCode.EXPIRED, // 版本到期，引导续费
                        isUnableAlert: code === EditionErrorCode.NOT_PURCHASED, // 功能不可用，提醒
                        isIneffective: code === EditionErrorCode.INEFFECTIVE, // 未生效，提醒
                        message, // 功能不可用信息
                    },
                });
            });
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
    @import 'styles/index.scss';
</style>
