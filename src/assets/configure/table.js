export default {
    manage$appointment: [
        {
            label: '班次',
            prop: 'name',
            width: 200,
        },
        {
            label: '时间段',
            prop: 'time',
            width: 200,
        },
        {
            label: '每个医生可预约人数',
            prop: 'limit',
            width: 200,
        },
        {
            label: '',
            prop: '_set',
            width: 102,
            titleStyle: {
                textAlign: 'right',
                paddingRight: '10px',
            },
            bodyStyle: {
                textAlign: 'right',
                color: '#0A8CEA',
                cursor: 'pointer',
                paddingRight: '10px',
            },
        },
    ],
    // eslint-disable-next-line
    manage$appointment_inner: [
        {
            label: '班次',
            prop: 'name',
            width: 300,
        },
        {
            label: '时间段',
            prop: 'time',
            width: 100,
        },
        {
            label: '预约人数限制',
            prop: 'limit',
            width: 200,
        },
        {
            label: '',
            prop: '_set',
            width: 102,
            titleStyle: {
                textAlign: 'right',
                paddingRight: '10px',
            },
            bodyStyle: {
                textAlign: 'right',
                color: '#0A8CEA',
                cursor: 'pointer',
                paddingRight: '10px',
            },
        },
    ],
    manage$related: [
        {
            label: '',
            prop: 'index',
            width: 36,
        },
        {
            label: '项目',
            prop: 'name',
            width: 224,
        },
        {
            label: '英文缩写',
            prop: 'ab',
            width: 120,
        },
        {
            label: '定量定性',
            prop: 'type',
            width: 120,
        },
        {
            label: '单位',
            prop: 'unit',
            width: 120,
        },
        {
            label: '参考范围',
            prop: '_ref',
            width: 160,
        },
    ],
    statistics: {
        medicine: [
            {
                label: '日期',
                prop: 'date',
                width: 90,
                bodyStyle: {},
            },
            {
                label: '入库/出库',
                prop: 'type',
                width: 72,
            },
            {
                label: '药品名称',
                prop: 'cadn',
                width: 170,
            },
            {
                label: '规格',
                prop: 'specification',
                width: 140,
            },
            {
                label: '生产批号',
                prop: 'batchNo',
                width: 120,
            },
            {
                label: '效期',
                prop: 'expiryDate',
                width: 90,
            },
            {
                label: '进价',
                prop: 'costUnitPrice',
                width: 84,
                titleStyle: {
                    textAlign: 'right',
                    paddingRight: '10px',
                    paddingLeft: '0px',
                },
                bodyStyle: {
                    textAlign: 'right',
                    paddingRight: '10px',
                    paddingLeft: '0px',
                },
            },
            {
                label: '售价',
                prop: 'unitPrice',
                width: 84,
                titleStyle: {
                    textAlign: 'right',
                    paddingRight: '10px',
                    paddingLeft: '0px',
                },
                bodyStyle: {
                    textAlign: 'right',
                    paddingRight: '10px',
                    paddingLeft: '0px',
                },
            },
            {
                label: '数量',
                prop: 'count',
                width: 84,
                titleStyle: {
                    textAlign: 'right',
                    paddingRight: '10px',
                    paddingLeft: '0px',
                },
                bodyStyle: {
                    textAlign: 'right',
                    paddingRight: '10px',
                    paddingLeft: '0px',
                },
            },
            {
                label: '操作人',
                prop: 'operater',
                width: 70,
            },
        ],
    },
    examination: [
        {
            label: '序号',
            prop: 'index',
            width: 38,
            bodyStyle: {
                left: '10px',
            },
        },
        {
            label: '项目',
            prop: 'name',
            width: 292,
        },
        {
            label: '上次结果',
            prop: 'lastValue',
            width: 80,
        },
        {
            label: '上次提示',
            prop: 'lastNature',
            width: 80,
        },
        {
            label: '检查结果',
            prop: 'value',
            width: 100,
        },
        {
            label: '提示',
            prop: 'nature',
            width: 80,
        },
        {
            label: '单位',
            prop: 'unit',
            width: 80,
        },
        {
            label: '参考范围',
            prop: '_ref',
            width: 100,
        },
    ],
    examination$print: [
        {
            label: '',
            prop: 'index',
            width: 42,
        },
        {
            label: '项目',
            prop: 'name',
            width: 246,
        },
        {
            label: '检查结果',
            prop: 'value',
            width: 116,
        },
        {
            label: '单位',
            prop: 'unit',
            width: 100,
        },
        {
            label: '',
            prop: 'nature',
            width: 60,
        },
        {
            label: '参考范围',
            prop: '_ref',
            width: 160,
            ellipsisHide: true,
        },
    ],
};
