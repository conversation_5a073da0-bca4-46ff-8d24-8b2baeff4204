import ExaminationAPI from 'api/examination';
import GoodsAPI from 'api/goods/index';
import GoodsStockInAPI from 'api/goods/stock-in';

export class CloudExamOrderListPresenter {
    constructor(view) {
        this.view = view;
    }

    async loadData(params) {
        try {
            this.view.showLoading();

            const {
                rows, total, totalReceivedFee, totalReceivableFee, totalDepositDeductionFee,
            } = await ExaminationAPI.getExamOrders(params);

            this.view.receiveData(
                {
                    list: rows || [],
                    page: {
                        count: total,
                    },
                    totalInfo: {
                        total,
                        totalReceivableFee,
                        totalDepositDeductionFee,
                        totalReceivedFee,
                    },
                },
            );
        } catch (e) {
            console.error(e);
        } finally {
            this.view.hideLoading();
        }
    }

    async refund(id) {
        return ExaminationAPI.refundExamOrder(id);
    }

    async cancel(id) {
        return ExaminationAPI.cancelExamOrder(id);
    }

    static async getPayOrderInfo(orderId) {
        const {
            id, totalFee,
        } = await ExaminationAPI.payExamOrder(orderId);

        return {
            payTransactionId: id,
            needPay: totalFee,
        };
    }

    // 拉取云检仪器相关的库存列表
    async loadMaterialStockList() {
        try {
            this.view.showLoading();

            const res = await GoodsAPI.goodsList({
                limit: 20,
                offset: 0,
                cloudSupplierFlag: 1,
                type: 2,
            });

            this.view.receiveMaterialsData({
                list: res.data?.rows || [],
            });
        } catch (e) {
            console.error(e);
        } finally {
            this.view.hideLoading();
        }
    }

    async loadExamApplyRecordList() {
        try {
            this.view.showLoading();
            const { data } = await GoodsStockInAPI.list({
                type: 6,
                pharmacyType: 0,
                offset: 0,
                limit: 100,
                withLogistics: 1,
            });
            this.view.receiveApplyRecordData({
                list: (data.rows || []).map((item) => ({
                    ...item,
                    stockOrderLogistics: item.stockOrderLogistics || {},
                })),
            });
        } catch (e) {
            console.error(e);
        } finally {
            this.view.hideLoading();
        }
    }
}
