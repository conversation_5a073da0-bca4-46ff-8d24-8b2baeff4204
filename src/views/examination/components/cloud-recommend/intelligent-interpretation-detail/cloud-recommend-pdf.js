import html2pdf from 'html2pdf.js';

export default class CloudRecommendPdf {
    constructor(data) {
        this.data = data;
        const {
            examinationCloudIntelligentReport,
        } = window.AbcPackages.AbcTemplates;
        const {
            printData,
        } = data;
        this.abcPrintInstance = new window.AbcPackages.AbcPrint({
            template: examinationCloudIntelligentReport,
            page: {
                size: 'A4',
                orientation: window.AbcPackages.AbcPrint.Orientation.portrait,
                pageSizeReduce: {
                    bottom: 10,
                    left: 10,
                    right: 10,
                    top: 10,
                },
            },
            originData: printData,
        });
        this.PDFOptions = {
            filename: 'AI 报告解读.pdf',
            jsPDF: {
                unit: 'mm',
                format: [210, 298],
                orientation: 'portrait',
            },
        };
    }
    get filename() {
        return this.PDFOptions.filename;
    }
    async savePDF() {
        await this.abcPrintInstance.init(true);
        const html = await this.abcPrintInstance.splitPreview();
        html2pdf(html, this.PDFOptions);
    }
}
