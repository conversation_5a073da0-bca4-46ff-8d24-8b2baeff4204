<template>
    <div id="examination-download-log-hover-popover" @mouseenter="isInContent = true" @mouseleave="handleLeave">
        <abc-flex
            v-if="loading"
            align="center"
            justify="center"
            style="width: 200px; height: 50px;"
        >
            <abc-loading-spinner
                small
                gray
            ></abc-loading-spinner>
        </abc-flex>

        <template v-else>
            <abc-flex
                vertical
                :gap="12"
                class="examination-device-data__download-log-wrapper"
            >
                <div
                    v-for="(log,idx) in logs"
                    :key="idx"
                    class="examination-device-data__download-log-item"
                >
                    <div>
                        下载时间：{{ formatDate(log.operatorTime, 'YYYY-MM-DD HH:mm:ss') }}
                    </div>
                    <div>
                        下载人员：{{ log.operatorName }}
                    </div>
                </div>
            </abc-flex>
        </template>
    </div>
</template>

<script>
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';
    import { FINISH_FETCH_KEY } from '@/directive/create-hover-popper';
    import { formatDate } from '@tool/date';
    import ExaminationAPI from 'api/examination';

    export default {
        components: {
            AbcLoadingSpinner,
        },

        props: {
            id: {
                type: String,
                required: true,
            },

            config: {
                type: Object,
                required: true,
            },

            destroyPopper: {
                type: Function,
            },
        },

        data() {
            return {
                isInContent: false,
                loading: false,
                logs: [],
            };
        },

        created() {
            this.fetchDownloadLogs();
        },

        methods: {
            formatDate,
            handleLeave() {
                if (this.config.trigger !== 'hover') return;
                this.isInContent = false;
                if (typeof this.destroyPopper === 'function') {
                    this.destroyPopper();
                }
                this.$destroy();
                $(this.$el).remove();
            },

            async fetchDownloadLogs() {
                try {
                    this.loading = true;
                    const data = await ExaminationAPI.fetchExaminationDownloadLogs(this.id);
                    this.logs = data.rows || [];
                } catch (e) {
                    console.error(e);
                }
                this.loading = false;

                // 数据加载完成后通知 popper 指令计算位置信息
                this.$nextTick(() => {
                    this.$emit(FINISH_FETCH_KEY);
                });
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/mixin.scss";

#examination-download-log-hover-popover {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1992;
    min-width: 200px;
    max-width: 680px;
    background: #fffdec;
    border: 1px solid #e6e3c4;
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

    .examination-device-data__download-log-wrapper {
        padding: 12px;
    }
}
</style>
