/*
 * <AUTHOR>
 * @DateTime 2020-09-23 17:34:57
 */
import OffLineTip from '../layout/off-line-tip.vue';
import OnlineReference from './component/online-reference.vue';
import {
    mapGetters, mapActions,
} from 'vuex';
export default {
    components: {
        OffLineTip,
        OnlineReference,
    },
    data() {
        return {
            clientWidth: 0,
        };
    },
    computed: {
        ...mapGetters([
            'userInfo',
        ]),
        isPc() {
            return this.clientWidth > 800;
        },
        isMobile() {
            return !this.isPc;
        },
    },
    created() {
        window.onresize = () => {
            this.setHtmlSize();

            this.clientWidth = document.documentElement.clientWidth;
        };
        window.onresize();
    },
    methods: {
        ...mapActions([
            'acGetEmployeesMe',
        ]),
        /**
         * 在视图宽度小于minWidth时，设置html标签fontSize，使rem随窗口适配
         * <AUTHOR>
         * @date 2023-03-20
         */
        setHtmlSize() {
            const baseSize = 75;
            const minWidth = 800;
            const { clientWidth } = document.documentElement;
            if (clientWidth <= minWidth) {
                const scale = clientWidth / 750;
                // 设置一个最大值
                const curSize = baseSize * Math.min(scale, 2);
                // curSize = curSize > 54 ? 54 : curSize
                // 设置页面根节点字体大小
                document.documentElement.style.fontSize = `${curSize}px`;
            } else {
                document.documentElement.style.fontSize = '';
            }
        },
        // /**
        //  * 处理页面滑动，头部固定
        //  * <AUTHOR>
        //  * @date 2023-03-20
        //  */
        // createScrollListenFunc() {
        //     const headerNavNode = document.querySelector('.header-nav');
        //     const scrollNode = headerNavNode.parentNode;
        //     const handleFunc = () => {
        //         //导航header的主题切换
        //         const top = scrollNode.scrollTop;
        //         const route = this.$route.path;
        //         if (route !== '/about') {
        //             if (top > 80) {
        //                 headerNavNode.classList.add('white-nav');
        //             } else {
        //                 headerNavNode.classList.remove('white-nav');
        //             }
        //             //移动端微信下滑，nav位置问题
        //             //position: sticky; 也可解决
        //             if (top > 0) {
        //                 headerNavNode.classList.remove('nav-abs');
        //             } else {
        //                 headerNavNode.classList.add('nav-abs');
        //             }
        //         } else {
        //             headerNavNode.classList.add('white-nav');
        //         }
        //     };
        //     return {
        //         addListener: () =>
        //             scrollNode.addEventListener('scroll', handleFunc, false),
        //         removeListener: () =>
        //             scrollNode.removeEventListener('scroll', handleFunc, false),
        //     };
        // },
    },
};
