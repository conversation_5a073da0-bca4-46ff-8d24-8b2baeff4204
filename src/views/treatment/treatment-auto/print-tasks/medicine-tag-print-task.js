import PrintTask from 'views/pharmacy/pharmacy-auto/pharmacy-print-task/print-task.js';

import PrintAPI from 'api/print.js';
import clone from 'utils/clone.js';
import { getAbcPrintOptions } from '@/printer/print-handler.js';
import AbcPrinter from '@/printer/index.js';
import { getViewDistributeConfig } from '@/views-distribute/utils.js';
import { SourceFormTypeEnum } from '@/service/charge/constants.js';
import WesternMedicineConfig from '@/assets/configure/western-medicine-config.js';
import Store from '@/store';
import Logger from 'utils/logger';

export class MedicineInfusionTagPrintTask extends PrintTask {
    async prepareData(params) {
        const { id } = params;
        const { data } = await PrintAPI.printChargeMedicineTag(id, true);

        Logger.report({
            scene: 'auto_print_treatment',
            data: {
                scene: 'medicine_tag_data',
                uuid: this.uuid,
                info: '执行站--请求用药标签数据',
                data: {
                    printData: data,
                },
            },
        });

        const printData = {
            w: [],c: [], patient: data.patient,
        };

        data.chargeForms.forEach((form) => {
            const newForm = clone(form);
            newForm.formItems = newForm.chargeFormItems;
            if (newForm.sourceFormType !== SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                printData.w.push(newForm);
            } else {
                printData.c.push(newForm);
            }
        });

        const forms = printData.w.concat(printData.c);
        // 获取收费单对应的皮试结果
        try {
            const infusionForms = forms.filter((form) => {
                return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN;
            });
            if (infusionForms && infusionForms.length) {
                const astResultData = await PrintAPI.getAstResultByCharge(id, true);
                const astResultList = astResultData.data?.list || [];
                infusionForms.forEach((infusionForm) => {
                    infusionForm.chargeFormItems.forEach((infusionFormItem) => {
                        const formItemAstData = astResultList.find((astResultItem) => astResultItem.formItemId === infusionFormItem.id);
                        if (formItemAstData) {
                            infusionFormItem.ast = formItemAstData.ast || 0;
                            infusionFormItem.astResult = formItemAstData.astResult;
                        }
                    });
                });
            }
        } catch (e) {
            console.error('执行站-自动打印用药标签-获取执行单皮试结果失败\n', e);
        }

        return {
            forms,
            patient: data.patient,
            doctorName: data.doctorName,
            patientOrderNo: data.patientOrderNo,
            clinicName: Store.getters.currentClinic.clinicName,
        };
    }

    // 计算打印次数
    calcPrintCount(usageInfo) {
        if (!usageInfo) return 1;
        const {
            freq, days,
        } = usageInfo;
        const _freqObj = WesternMedicineConfig.freq.find((it) => {
            return it.en === freq || it.name === freq;
        });
        let freqTime = 1;
        if (_freqObj) {
            freqTime = 24 / _freqObj.time;
        } else {
            if (freq) {
                const _num = +freq.replace(/[^0-9]/ig, '');
                if (/^q\d+d$/.test(freq)) {
                    freqTime = 1 / _num;
                } else if (/^q\d+w$/.test(freq)) {
                    freqTime = 24 / (168 * _num);
                } else if (/^q\d+h$/.test(freq)) {
                    freqTime = 24 / _num;
                }
            }
        }
        if (!freqTime || freqTime === Infinity) {
            freqTime = 1;
        }
        return (days || 1) * freqTime;
    }

    printHandler(data) {
        if (!data) {
            Logger.report({
                scene: 'auto_print_treatment',
                data: {
                    scene: 'medicine_tag_data_is_empty',
                    uuid: this.uuid,
                    info: '执行站--用药标签数据为空',
                    data: {
                        printData: data,
                    },
                },
            });
            return;
        }
        const printOptions = getAbcPrintOptions('用药标签', data);
        AbcPrinter.abcPrint({
            ...printOptions, isAutoPrint: true,
        }, this.uuid);

    }

    getTaskName() {
        return 'MedicineInfusionTagPrintTask';
    }

    static isEnable(config) {
        return config.isAutoPrintMedicineInfusionTag;
    }


    static isPrintAble(printable) {
        return printable && printable.medicineTag;
    }

    static getLabel() {
        return getViewDistributeConfig().Print.printOptions.MEDICINE_TAG.label;
    }
}
