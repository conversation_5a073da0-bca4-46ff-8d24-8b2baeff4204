// 记录用户打印设置
import LocalStore from 'utils/localStorage-handler';
import { CLINIC_TYPE } from 'views/common/clinic';
import { getApp } from '@/core';

const printOptionCacheKeyPrefix = 'PRINT_OPTION_CACHE';
const disablePrintOptionsInHospital = ['检查单', '检验单'];

export const CashierMeanwhilePrintPayModeEnum = {
    // 全部支付方式均，同时打印收费小票
    all: 1,
    // 仅医保支付时才，同时打印收费小票
    shebao: 2,
};

const _map = {
    // 此版本不默认勾选
    cashier: [],
    newCashierSheet: [],
    cashierMeanwhilePrintPayMode: CashierMeanwhilePrintPayModeEnum.all,
    outpatient: [],
    registration: false,
    recharge: [],
    // 住院收费站需要同时打印的单据
    hospitalCashier: [],

    // 小票
    directSmallNeedPrint: false,
    registrationNeedPrint: false,
    outpatientNeedPrint: false,
    rechargeNeedPrint: false,
    // 医院
    hospitalDispensingNeedPrint: false,

    // 住院收费站是否开启同时打印
    hospitalCashierMeanwhilePrint: false,
    // 住院收费站当次收费是否同时打印
    hospitalCashierNeedPrint: false,
};

function generatePrintOptionsCacheKey(clinicId) {
    return `${printOptionCacheKeyPrefix}_${clinicId}`;
}

export function migratePrintOptionsCache(clinic) {
    // 保证即使迁移出问题也不会影响正常加载
    try {
        const oldCache = LocalStore.get(printOptionCacheKeyPrefix, true);
        if (oldCache && !LocalStore.get(generatePrintOptionsCacheKey(clinic.id),true)) {
            // 医院需要过滤门诊设置的检查单和检验单
            if (clinic.hisType === CLINIC_TYPE.HOSPITAL) {
                console.warn(`医院版本，过滤${disablePrintOptionsInHospital}`);
                oldCache.outpatient = oldCache.outpatient.filter((item) => {
                    return !disablePrintOptionsInHospital.includes(item);
                });
                oldCache.cashier = oldCache.cashier.filter((item) => {
                    return !disablePrintOptionsInHospital.includes(item);
                });
                oldCache.recharge = oldCache.recharge.filter((item) => {
                    return !disablePrintOptionsInHospital.includes(item);
                });
            }
            LocalStore.set(`${printOptionCacheKeyPrefix}_${clinic.id}`,oldCache);
        }
    } catch (e) {
        console.error(e);
    }
}

export function setStore(value = {}) {
    Object.assign(_map , value);

    _map.cashier = _map.cashier.filter((item) => {
        return item !== '' && (item !== '打印清单' || item !== '打印收费单' || item !== '打印收费票据');
    });

    _map.outpatient = _map.outpatient.filter((item) => {
        return item !== '' && item !== '打印处方' && item !== '打印医嘱';
    });

    _map.recharge = _map.recharge.filter((item) => {
        return item !== '' && item !== '打印充值凭证';
    });

    LocalStore.set(generatePrintOptionsCacheKey(getApp().store.getters.currentClinic.id), _map);
}

export function getStore() {
    try {
        /**
         * 从普通诊所切换到医院管家时
         * 需要将原来诊所管家的打印设置迁移到医院管家
         */
        const {
            id, hisType,
        } = getApp().store.getters.currentClinic;
        const cache = LocalStore.get(generatePrintOptionsCacheKey(id), true) || {};
        if (hisType === CLINIC_TYPE.HOSPITAL) {
            if (cache.outpatient && cache.cashier && cache.recharge) {
                cache.outpatient = cache.outpatient.filter((item) => {
                    return !disablePrintOptionsInHospital.includes(item);
                });
                cache.cashier = cache.cashier.filter((item) => {
                    return !disablePrintOptionsInHospital.includes(item);
                });
                cache.recharge = cache.recharge.filter((item) => {
                    return !disablePrintOptionsInHospital.includes(item);
                });
                LocalStore.set(generatePrintOptionsCacheKey(id), cache);
                console.warn(`医院版本，过滤${disablePrintOptionsInHospital}`);
            }
        }
        return Object.assign(_map , cache);
    } catch (e) {
        console.error(e);
        return {};
    }
}
