import Main from './index.vue';
import Vue from 'vue';

const PayOrder = function (options) {
    const VmConstructor = Vue.extend(Main);
    options = options || {};
    const id = 'wallet_pay_order_dialog';
    const extendProps = {};
    if (options?.parent) {
        extendProps.parent = options.parent;
    }
    const instance = new VmConstructor({
        propsData: options,
        ...extendProps,
    });
    instance.id = id;
    instance.vm = instance.$mount();
    document.body.appendChild(instance.vm.$el);
    instance.vm.visible = true;
    instance.dom = instance.vm.$el;
    return instance.vm;
};

export default PayOrder;
