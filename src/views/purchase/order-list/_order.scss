@import '../../../styles/theme';

.purchase-order-wrapper {
    .goods-table-wrapper {
        .tr .td {
            padding-top: 8px;
            padding-bottom: 8px;
        }

        .text-row-1 {
            padding-top: 4px;
        }

        &.offline-order {
            .describe {
                height: 52px;
                min-height: 52px;
                max-height: 52px;
            }

            .one {
                width: 20%;
                min-width: 20%;
                max-width: 20%;
            }

            .two {
                width: 22%;
                min-width: 22%;
            }

            &.detail-order {
                .two {
                    width: 10%;
                    min-width: 10%;
                    max-width: 10%;
                }

                .three {
                    width: 30%;
                }
            }
        }

        .describe {
            height: 80px;
            min-height: 80px;
            max-height: 80px;
        }
    }

    .order-header-info {
        display: flex;
        width: 100%;
        padding: 16px 0;

        .courier-company {
            color: $T2;
        }

        .one,
        .two {
            width: 18%;
            min-width: 18%;
        }

        .three,
        .four {
            width: 22%;
            min-width: 22%;
        }

        .one,
        .two,
        .three,
        .four,
        .five {
            line-height: 20px;

            > div {
                min-height: 20px;
            }
        }

        .row-2 {
            margin-top: 8px;
        }

        .four,
        .five {
            width: 20%;
        }
    }

    .purchase-order-goods-table {
        .tr {
            .row-1 {
                position: relative;
                display: flex;
                align-items: center;
                font-weight: 400;
                line-height: 20px;

                .view-cost-price {
                    height: 20px;
                    line-height: 20px;
                }

                &.is-shortage {
                    color: $Y2;
                }
            }

            .row-2 {
                margin-top: 4px;
                font-size: 12px;
                line-height: 16px;
                color: $T2;
            }

            .price-text {
                color: #ff3333;
            }
        }
    }

    .dialog-footer {
        .total-info {
            margin-right: 24px;
            font-size: 14px;
            color: $T2;
        }

        .total-price {
            font-size: 22px;
            font-weight: bold;
            color: #ff3333;
        }
    }
}
