<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle"
        append-to-body
        class="purchase-order-wrapper"
        content-styles="width: 1200px; padding-top: 0;"
        full
    >
        <abc-form v-abc-loading="loading" style="height: 100%;">
            <goods-table
                :class="{ 'offline-order': !isMallPurchase }"
                :config="tableConfig"
                :data="order.items"
                :loading="!order"
                :show-amount="false"
                class="purchase-order-goods-table goods-middle-table-wrapper detail-order"
            >
                <div v-if="isMallPurchase" slot="describe-title" class="order-header-info">
                    <div class="one">
                        <div class="ellipsis">
                            创建人：{{ order.applicantName }}
                        </div>
                        <div class="row-2">
                            订单状态：
                            <span
                                :style="{
                                    color:
                                        order.status === purchaseOrderStatus.MALL_WAITING_PAY ? '#FF9933' : '#000000',
                                }"
                            >{{ order.statusName }}
                                <template v-if="order.status === purchaseOrderStatus.MALL_WAITING_PAY">{{
                                    formatLeftTime
                                }}</template>
                            </span>
                        </div>
                    </div>
                    <div class="two">
                        <div>采购方式：{{ orderType }}</div>
                        <div class="row-2">
                            商城订单：<abc-button type="text" @click="fetchMallInfo">
                                {{
                                    mallOrderInfo.orderId
                                }}
                            </abc-button>
                        </div>
                    </div>
                    <div class="three">
                        <div class="ellipsis">
                            供应商：{{ mallOrderInfo.vendorName }}
                        </div>
                        <div class="ellipsis row-2">
                            订单金额：<abc-money :is-show-space="true" :value="mallOrderInfo.totalPrice"></abc-money>
                            <template
                                v-if="mallOrderInfo.deliveryPrice"
                            >
                                (含运费<abc-money :is-show-space="true" :value="mallOrderInfo.deliveryPrice"></abc-money>)
                            </template>
                        </div>
                    </div>
                    <div class="four">
                        <div class="ellipsis">
                            采购门店：{{ order.applicantOrganName }}
                        </div>
                        <div v-if="showDealData" class="ellipsis row-2">
                            成交金额：<abc-money :is-show-space="true" :value="mallOrderInfo.dealTotalPrice"></abc-money>
                            <template v-if="mallOrderInfo.dealDeliveryPrice">
                                (含运费<abc-money :is-show-space="true" :value="mallOrderInfo.dealDeliveryPrice"></abc-money>)
                            </template>
                        </div>
                    </div>
                    <div class="five">
                        <div class="ellipsis">
                            <template v-if="mallOrderInfo.afterSaleStatus !== -1">
                                售后状态：<abc-button type="text" @click="fetchMallInfo">
                                    {{
                                        mallOrderInfo.afterSaleStatusName
                                    }}
                                </abc-button>
                            </template>
                        </div>
                        <delivery-detail
                            v-if="mallOrderInfo && mallOrderInfo.logistics"
                            :logistics="mallOrderInfo.logistics"
                            :order-id="mallOrderInfo.orderId"
                            class="row-2"
                            style=" display: flex; flex: 1;"
                        ></delivery-detail>
                    </div>
                </div>
                <div v-else slot="describe-title" class="order-header-info">
                    <div class="one">
                        创建人：{{ order.applicantName }}
                    </div>
                    <div class="two">
                        状态：{{ order.statusName }}
                    </div>
                    <div :title="order.applicantOrganName" class="three ellipsis">
                        采购门店：{{ order.applicantOrganName }}
                    </div>
                    <div class="four">
                        采购方式：线下采购
                    </div>
                </div>

                <template
                    #displayName="{
                        item: {
                            displayName, displaySpec, manufacture, hisGoodsInfo
                        }
                    }"
                >
                    <div class="row-1 text-row-1 ellipsis">
                        <span
                            v-if="hisGoodsInfo"
                            v-abc-goods-hover-popper:remote="{
                                goods: hisGoodsInfo,
                                openDelay: 500,
                                showPrice: true,
                            }"
                        >
                            {{ displayName || hisGoodsInfo.medicineCadn || hisGoodsInfo.name }}
                        </span>
                        <template v-else>
                            -
                        </template>
                    </div>
                    <div class="row-2">
                        <template v-if="compositePropVisible.displaySpec">
                            {{ displaySpec || '' }}
                        </template>
                        <template v-if="compositePropVisible.manufacture">
                            {{ manufacture || '' }}
                        </template>
                    </div>
                </template>

                <template #avgSell="{ item }">
                    <div class="row-1 text-row-1">
                        {{ item.avgSell || '-' }}
                    </div>
                    <div class="row-2"></div>
                </template>

                <template #profitRat="{ item }">
                    <div class="row-1 text-row-1 ellipsis">
                        {{ item.profitRat ? `${item.profitRat}%` : '-' }}
                    </div>
                    <div class="row-2"></div>
                </template>

                <template #lastPackageCostPrice="{ item }">
                    <div class="row-1 text-row-1">
                        <template v-if="item.lastPackageCostPrice">
                            <span>{{ item.lastPackageCostPrice }}/{{
                                isChineseMedicine(item.hisGoodsInfo) ? item.hisGoodsInfo.pieceUnit : item.hisGoodsInfo.packageUnit
                            }}</span>
                            <view-cost-price-dialog
                                :id="item.hisGoodsInfo.goodsId"
                                :clinic-id="order.applicantOrganId"
                                :max-display-num="7"
                                :open-delay="250"
                                :triangle="false"
                                slot-style="marginLeft: 4px"
                                style="position: relative;"
                            >
                                <i class="iconfont cis-icon-price_trend" style="color: #2680f7;"></i>
                            </view-cost-price-dialog>
                        </template>
                        <template v-else>
                            -
                        </template>
                    </div>
                    <div
                        v-if="compositePropVisible.lastSupplierName && item.lastSupplierName"
                        :title="item.lastSupplierName"
                        class="row-2 ellipsis"
                    >
                        {{ item.lastSupplierName }}
                    </div>
                </template>

                <!--门店售价-->
                <template #packagePrice="{ item }">
                    <div class="row-1 text-row-1 ellipsis">
                        <span v-if="item.packagePrice">{{ moneyDigit(item.packagePrice, 5) }}</span>
                        <span v-else>-</span>
                    </div>
                </template>


                <!--剩余库存-->
                <template
                    #dispGoodsCount="{
                        item: {
                            dispGoodsCount, hisGoodsStockStatInfo
                        }
                    }"
                >
                    <div class="row-1 text-row-1">
                        {{ dispGoodsCount || '-' }}
                    </div>
                    <div class="row-2">
                        <template v-if="hisGoodsStockStatInfo && hisGoodsStockStatInfo.turnoverDays">
                            周转：{{ hisGoodsStockStatInfo.turnoverDays }}天
                        </template>
                    </div>
                </template>

                <template #dispGoodsCountExpend>
                    <abc-popover
                        :offset="40"
                        placement="top-start"
                        popper-class="custom-popover dark"
                        style="display: inline-flex;"
                        theme="white"
                        trigger="hover"
                    >
                        <abc-icon
                            slot="reference"
                            :color="$store.state.theme.style.P3"
                            icon="info_bold"
                            size="14"
                            style="margin-left: 4px;"
                        >
                        </abc-icon>
                        <span style="font-size: 12px;">可售数量=当前库存-锁定/禁售数量</span>
                    </abc-popover>
                </template>
                <!--计划采购量-->
                <template
                    #planPurchaseCount="{
                        item: {
                            planPurchaseCount, hisGoodsStockStatInfo, hisGoodsInfo
                        }
                    }"
                >
                    <div class="row-1 text-row-1">
                        <template v-if="planPurchaseCount">
                            {{ planPurchaseCount }}
                        </template>
                        <template v-else>
                            -
                        </template>
                    </div>
                    <div class="row-2">
                        <template
                            v-if="
                                showAdviseCount &&
                                    hisGoodsStockStatInfo &&
                                    hisGoodsStockStatInfo.recommendView &&
                                    hisGoodsStockStatInfo.recommendView.recommendCount
                            "
                        >
                            建议：{{ hisGoodsStockStatInfo.recommendView.recommendCount
                            }}{{ isChineseMedicine(hisGoodsInfo) ? hisGoodsInfo.pieceUnit : hisGoodsInfo.packageUnit }}
                        </template>
                    </div>
                </template>

                <!--实际采购量-->
                <template
                    #realPurchaseCount="{
                        item: {
                            hisPurchaseCount, hisGoodsInfo, mallGoodsInfo, realPurchaseCount, realPurchaseGoodsName, realPurchaseGoodsDisplaySpec
                        }
                    }"
                >
                    <template v-if="isMallPurchase">
                        <div
                            v-if="isShortage(mallGoodsInfo)"
                            :class="{ 'is-shortage': isShortage(mallGoodsInfo) }"
                            class="row-1 text-row-1"
                        >
                            {{ realPurchaseCount || `${mallGoodsInfo.mallPurchaseCount}${mallGoodsInfo.salesUnit}` }}
                            <template v-if="isShortage(mallGoodsInfo)">
                                <abc-icon icon="Arrow_Rgiht_"></abc-icon>
                                {{ mallGoodsInfo.mallDealPurchaseCount }}
                                {{ mallGoodsInfo.salesUnit }}
                            </template>
                        </div>
                        <div v-else class="row-1 text-row-1">
                            {{ realPurchaseCount || `${mallGoodsInfo.mallPurchaseCount}${mallGoodsInfo.salesUnit}` }}
                        </div>
                        <div
                            v-if="mallGoodsInfo"
                            :title="(compositePropVisible.realPurchaseGoodsName ? realPurchaseGoodsName : '') + (compositePropVisible.realPurchaseGoodsDisplaySpec ? realPurchaseGoodsDisplaySpec : '')"
                            class="row-2 ellipsis"
                        >
                            <template v-if="compositePropVisible.realPurchaseGoodsName">
                                {{ realPurchaseGoodsName || '' }}
                            </template>
                            <template v-if="compositePropVisible.realPurchaseGoodsDisplaySpec">
                                {{ realPurchaseGoodsDisplaySpec || '' }}
                            </template>
                        </div>
                    </template>
                    <div v-else class="row-1 text-row-1">
                        <template v-if="realPurchaseCount">
                            {{ realPurchaseCount }}
                        </template>
                        <template v-else>
                            {{ hisPurchaseCount }}{{ isChineseMedicine(hisGoodsInfo) ? hisGoodsInfo.pieceUnit : hisGoodsInfo.packageUnit }}
                        </template>
                    </div>
                </template>

                <template
                    #realPurchaseGoodsCostPrice="{
                        item: {
                            realPurchaseGoodsCostPrice, mallGoodsInfo
                        }
                    }"
                >
                    <div class="row-1 text-row-1 price-text" style="justify-content: flex-end;">
                        <template v-if="mallGoodsInfo">
                            <template v-if="showDealData">
                                <abc-money :value="(realPurchaseGoodsCostPrice || mallGoodsInfo.mallDealTotalPrice)"></abc-money>
                            </template>
                            <template v-else>
                                <abc-money :value="(realPurchaseGoodsCostPrice || mallGoodsInfo.mallTotalPrice)"></abc-money>
                            </template>
                        </template>
                        <span
                            v-if="mallGoodsInfo && mallGoodsInfo.stockInOrderStatus === GOODS_IN_STATUS.GOODS_IN"
                            style=" margin-left: auto; font-size: 12px; color: #7a8794;"
                        >{{ mallGoodsInfo.stockInOrderStatusName }}</span>
                    </div>
                    <div class="row-2"></div>
                </template>

                <!--设置按钮-->
                <template #settingExpend>
                    <abc-icon icon="set-t" style="cursor: pointer;" @click="onSettingTableHeader"></abc-icon>
                </template>
                <!--删除-->
                <template slot="footer">
                    <div></div>
                    <ul v-if="order" class="total-info" style="padding-right: 0;">
                        <li>
                            品种：<span>{{ order.kindCount }}</span>
                        </li>
                        <li>
                            数量：<span>{{ order.count | number }} </span>
                        </li>
                    </ul>
                </template>
                <change-log slot="logs" :logs="order.logs"></change-log>
            </goods-table>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <abc-space style="margin-right: auto;">
                <abc-check-access>
                    <abc-button type="blank" @click="customExportCG">
                        导出
                    </abc-button>
                </abc-check-access>
                <abc-check-access>
                    <print-dropdown
                        @print="print"
                        @select-print-setting="openPrintConfigSettingDialog"
                    ></print-dropdown>
                </abc-check-access>
                <abc-check-access>
                    <abc-tooltip placement="top-start" content="随货单模板表可发给供应商或自行完善金额、批次信息，完善后可一键导入模板入库">
                        <abc-button variant="ghost" @click="exportCG">
                            生成随货单模板
                        </abc-button>
                    </abc-tooltip>
                </abc-check-access>
                <abc-button
                    v-if="showPreBtn"
                    variant="ghost"
                    :min-width="124"
                    :width="124"
                    :loading="btnLoading"
                    @click="preSubmit"
                >
                    修改并重新发起
                </abc-button>
                <abc-button
                    v-if="
                        purchaseOrderStatus.MALL_WAITING_RECV_GOODS === order.status ||
                            purchaseOrderStatus.MALL_WAITING_SEND_GOODS === order.status ||
                            purchaseOrderStatus.MALL_FINISHED === order.status
                    "
                    :disabled="!mallOrderInfo.canStartAfterSale"
                    variant="ghost"
                    @click="afterSellHandler"
                >
                    申请售后
                </abc-button>
                <abc-button
                    v-if="purchaseOrderStatus.MALL_CLOSED === order.status"
                    variant="ghost"
                    @click="reOrderHandler"
                >
                    重新下单
                </abc-button>
            </abc-space>
            <template v-if="isMallPurchase">
                <template v-if="purchaseOrderStatus.MALL_WAITING_PAY === order.status">
                    <span
                        class="total-info"
                    >总计：<span class="total-price">
                        <abc-money :is-show-space="true" :symbol-icon-size="14" :value="mallOrderInfo.totalPrice"></abc-money>
                    </span></span>
                    <abc-button type="payment" :disabled="mallOrderInfo.isPublicTransferAccount" @click="payHandler">
                        付款
                    </abc-button>
                </template>

                <abc-button
                    v-if="purchaseOrderStatus.MALL_WAITING_PAY === order.status"
                    type="blank"
                    @click="cancelOrder"
                >
                    取消订单
                </abc-button>
                <abc-button
                    v-if="
                        order.status === purchaseOrderStatus.MALL_WAITING_RECV_GOODS ||
                            order.status === purchaseOrderStatus.MALL_FINISHED
                    "
                    :class="{ 'abc-tipsy abc-tipsy--n': !hasStockInModule }"
                    :disabled="!canInbound || !hasStockInModule"
                    data-tipsy="无入库权限"
                    @click="inboundHandler"
                >
                    {{ order.status === purchaseOrderStatus.MALL_FINISHED ? '入库' : '签收入库' }}
                </abc-button>
            </template>
        </div>

        <plan-dialog
            v-if="showAddPlan"
            v-model="showAddPlan"
            type="add"
            @getPlanId="getPlanId"
        ></plan-dialog>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import {
        moneyDigit, paddingMoney,
    } from '@/utils';
    import { formatGoodsNameSpec } from '../../inventory/goods-purchase/common';
    import { purchaseOrderStatus } from '../constant';
    import {
        count1, isChineseMedicine,
    } from '@/filters/goods';
    import {
        formatGoodsStock, formatRecentAvgSell,
    } from '../utils';
    import { GOODS_IN_STATUS } from 'views/inventory/constant';

    import EnterEvent from 'views/common/enter-event';
    import Clone from 'utils/clone';

    import GoodsTable from '../../inventory/common/goods-table';
    import PlanDialog from '../offline-purchase-plan/plan-dialog';
    import DeliveryDetail from '../components/delivery-detail'; // api

    const ChangeLog = () => import('../../inventory/common/goods-change-log.vue');
    const ViewCostPriceDialog = () => import('../../inventory/common/view-cost-price-dialog');

    import PurchaseAPI from 'api/purchase';
    import AbcPrinter from '@/printer';
    import { ABCPrintConfigKeyMap } from '@/printer/constants.js';
    import PrintDropdown from 'views/print/print-dropdown';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import { CustomStatHeaderDialog } from 'views/statistics/common/custom-stat-header-dialog';
    import { createMallGoodsInOrder } from 'views/inventory/goods-in/utils/mall-order-in.js';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import { v2DisableStatusEnum } from 'views/we-clinic/frames/shop/goods-manage/goods-list/constant';

    const StockInStatus = {
        DISABLED_INBOUND: 0, // 不可入库
        INBOUND: 1, // 可入库
        PART_INBOUND: 2, // 部分入库
        INBOUNDED: 3, // 已入库
    };
    //  采购单为确认之前，布局的状态均为修改的状态，与新增不同
    //                   1.门店查看采购单，可以修改，调用update，更新采购单，button 为确定
    //                   2.总部查看采购单，可以修改，调用confirm函数，确认并修改采购单，button 为确认采购申请。
    //                     二次确认给出弹窗提示，通知申请人进入采购流程
    //  采购单确认之后，总部和门店查看采购单，均为只能查看的状态，按钮为关闭

    export default {
        name: 'OrderDetail',

        components: {
            GoodsTable,
            ChangeLog,
            ViewCostPriceDialog,
            PlanDialog,
            DeliveryDetail,
            PrintDropdown,
        },
        mixins: [EnterEvent],
        props: {
            value: {
                type: Boolean,
            },
            orderId: [String, Number],
            goodsId: [String, Number],
        },
        data() {
            return {
                purchaseOrderStatus,
                GOODS_IN_STATUS,
                StockInStatus,
                order: {
                    items: [],
                },
                btnLoading: false,
                kindCount: 0,
                loading: true,
                isFirstPrint: true,
                plans: [],
                planId: '',
                showAddPlan: false,
                changePlan: false,

                currentEditIndex: -1,
                currentEditItem: {},
                tableConfig: [],
                autoUnlockRemainTimeSecond: 0,
                preSubmitList: [],
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'isChainAdmin', 'modulePermission']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            hasStockInModule() {
                return this.modulePermission && this.modulePermission.hasStockInModule;
            },
            showPreBtn() {
                return this.isRejectOrCancel && !this.isChainAdmin;
            },
            // 审批被驳回或者自己撤销
            isRejectOrCancel() {
                return [purchaseOrderStatus.APPLY_WITHDRAW, purchaseOrderStatus.AUDIT_REJECT].includes(this.order.status);
            },
            /**
             * @desc 显示成交数据
             */
            showDealData() {
                return (
                    this.order.status === purchaseOrderStatus.MALL_WAITING_RECV_GOODS ||
                    this.order.status === purchaseOrderStatus.MALL_FINISHED
                );
            },
            orderType() {
                return this.isMallPurchase ? 'ABC商城' : '线下采购';
            },
            isMallPurchase() {
                return this.order.orderType === 10;
            },
            canUpdatePlan() {
                return this.isChainAdmin && this.order && this.order.plan && this.order.status === 1;
            },
            dialogTitle() {
                if (this.order) {
                    return `采购单 ${this.order.orderNo || ''}`;
                }
                return '采购单';

            },
            mallOrderInfo() {
                return this.order?.mallOrderInfo || {};
            },

            /**
             * @desc 已完成的单子不显示建议采购量，总部不显示建议采购量
             */
            showAdviseCount() {
                if (this.isChainAdmin) return false;
                if (!this.isMallPurchase && this.order.status !== purchaseOrderStatus.AUDITING) return false;
                if (
                    this.isMallPurchase &&
                    (this.order.status === purchaseOrderStatus.MALL_FINISHED ||
                        this.order.status === purchaseOrderStatus.MALL_CLOSED)
                ) {
                    return false;
                }
                return true;
            },
            // 表头复合字段，子项的展示受自定义表控制，但是不单独成一列。
            compositePropVisible() {
                if (!this.order?.tableHeader?.length) {
                    return {};
                }

                return this.order.tableHeader.reduce((res, item) => {
                    res[item.prop] = true;
                    return res;
                }, {});
            },
            // 秒数 转为 XX时XX分XX秒   time = 传入的秒数
            formatLeftTime() {
                const time = this.autoUnlockRemainTimeSecond;
                if (!time || time <= 0) {
                    return '';
                }
                let hours = Math.floor(time / 3600);
                let minute = Math.floor(Math.floor(time % 3600) / 60);
                let second = time % 60;
                let str = '';
                if (hours) {
                    hours = hours.toString().length === 1 ? `0${hours}` : hours;
                    str += `${hours}:`;
                }
                if (minute) {
                    minute = minute.toString().length === 1 ? `0${minute}` : minute;
                    str += `${minute}:`;
                }
                second = second.toString().length === 1 ? `0${second}` : second;
                str += `${second}`;
                return str;
            },
            /**
             * @desc 是否能签收入库
             */
            canInbound() {
                return (
                    this.mallOrderInfo?.inOrderStatus === StockInStatus.INBOUND ||
                    this.mallOrderInfo?.inOrderStatus === StockInStatus.PART_INBOUND
                );
            },
        },
        async created() {
            // this.tableConfig = orderDetailConfig;
            await this.fetchData();

            //  总部不展示建议采购量 or 已完成的单子不展示建议采购量
            // if (this.isChainAdmin || this.order.status !== GOODS_PURCHASE_STATUS.REVIEW) {
            //     this.tableConfig.splice(2, 2);
            // }

            if (this.isChainAdmin) {
                const data = await PurchaseAPI.fetchPurchasePlanSimple();
                this.plans = data;
            }
            // if (!this.isMallPurchase) {
            //     this.tableConfig = offlineOrderDetailConfig;
            // }
        },
        methods: {
            moneyDigit,
            async preSubmit() {
                this.btnLoading = true;
                const orders = this.order.items?.map((item) => {
                    return {
                        goodsId: item?.hisGoodsInfo?.goodsId,
                        hisPurchaseCount: item?.hisPurchaseCount || 0,
                    };
                });
                await this.fetchBatchGoods(orders);
                if (this.preSubmitList.length === 0) {
                    return this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '药品全部已停用/删除，无法重新发起申请',
                    });
                }
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: this.preSubmitList.length === orders.length ? '确定后，将重新发起采购申请' : '确定后，将重新发起未停用药品的采购申请',
                    onConfirm: () => {
                        this.$emit('preSubmitOrder', orders.filter((item) => {
                            return this.preSubmitList.find((it) => {
                                return it === item.goodsId;
                            });
                        }));
                        this.$nextTick(() => {
                            this.showDialog = false;
                        });
                    },
                });
            },

            async fetchBatchGoods(orders) {
                try {
                    const data = await GoodsAPIV3.getChargeFormItemBatchInfos({
                        goodsIds: orders?.map((item) => {
                            return item.goodsId;
                        }),
                    });
                    this.preSubmitList = data?.list?.filter((item) => {
                        return item.v2DisableStatus !== v2DisableStatusEnum.disableSale && item.v2DisableStatus !== v2DisableStatusEnum.canSale && item.status !== 99;
                    })?.map((item) => {
                        return item.goodsId;
                    }) || [];
                } catch (e) {
                    console.log(e);
                } finally {
                    this.btnLoading = false;
                }

            },
            onSettingTableHeader() {
                new CustomStatHeaderDialog({
                    customHeaderConfig: {
                        tableName: '采购',
                        tableKey: 'goods.stocks.purchaseOrder',
                        updateCustomHeaderSuccessHandler: this.fetchData,
                        customDataFilterFn: this.customDataFilterFn,
                        mode: 'draggle',
                        showFixed: true,
                    },
                }).generateDialog({ parent: this });
            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'goods-purchase' }).generateDialogAsync({ parent: this });
            },
            /**
             * @desc 锁单倒计时
             * <AUTHOR> Yang
             * @date 2020-09-21 18:16:15
             */
            lockOrderCountdown(autoUnlockRemainTimeSecond) {
                if (this._timer) {
                    clearInterval(this._timer);
                }

                this.autoUnlockRemainTimeSecond = autoUnlockRemainTimeSecond;
                if (autoUnlockRemainTimeSecond && autoUnlockRemainTimeSecond > 1) {
                    this._timer = setInterval(() => {
                        this.autoUnlockRemainTimeSecond--;
                        if (!this.autoUnlockRemainTimeSecond || this.autoUnlockRemainTimeSecond <= 0) {
                            clearInterval(this._timer);
                            /**
                             * @desc 可能此时倒计时后台还有1s，导致拉取的详情还是锁单状态，此时需要延时1s再拉取
                             * <AUTHOR> Yang
                             * @date 2020-09-27 10:26:11
                             */
                            const timer = setTimeout(() => {
                                this.fetchData(false);
                                this.$Toast({
                                    message: '超时未支付，采购单已关闭',
                                    type: 'error',
                                });
                                clearTimeout(timer);
                            }, 1000);
                        }
                    }, 1000);
                }
            },

            formatGoodsNameSpec,
            formatRecentAvgSell,
            formatGoodsStock,
            count1,
            /**
             * @desc 判断是否缺货，最终成交量小于购买量就是缺货状态
             */
            isShortage(item) {
                if (
                    this.order.status === purchaseOrderStatus.MALL_WAITING_RECV_GOODS ||
                    this.order.status === purchaseOrderStatus.MALL_FINISHED
                ) {
                    return item.mallPurchaseCount > item.mallDealPurchaseCount;
                }
                return false;
            },
            addPurchasePlan() {
                this.$refs.purchasePlanSelect.showPopper = false;
                this.showAddPlan = true;
            },
            async submitEdit() {
                try {
                    await PurchaseAPI.purchaseIntoPlan(this.planId, this.orderId);
                    await this.fetchData();
                    this.changePlan = false;
                } catch (err) {
                    this.changePlan = false;
                    this.planId = this.order.plan.planId;
                }
            },
            /**
             * @desc 取消修改name
             * <AUTHOR>
             * @date 2019/07/25 17:37:16
             */
            cancelEdit() {
                this.changePlan = false;
            },
            /**
             * @desc 新建采购计划后需要更新可选择列表
             * <AUTHOR>
             * @date 2019/07/26 14:57:30
             */
            async getPlanId(planId) {
                if (this.isChainAdmin) {
                    const data = await PurchaseAPI.fetchPurchasePlanSimple();
                    this.$nextTick(() => {
                        this.plans = data;
                        this.planId = planId;
                    });
                }
            },

            paddingMoney,
            isChineseMedicine,

            /**
             * @desc  获取采购单详情
             * <AUTHOR>
             * @date 2018/11/14 16:11:52
             * @params
             * @return
             */
            async fetchData(needTimer = true) {
                if (!this.orderId) return false;

                try {
                    this.loading = true;
                    const { data } = await PurchaseAPI.fetchOrderDetail(this.orderId);
                    this.order = data;
                    // 自定义表头
                    this.tableConfig = this.formatTableHeader(data.tableHeader);

                    this.order.list = this.order.items.map((item) => {
                        const {
                            hisGoodsInfo, hisGoodsStockStatInfo, id, hisPurchaseCount, mallGoodsInfo,
                        } = item;
                        return {
                            id,
                            goodsId: hisGoodsInfo.goodsId,
                            goods: hisGoodsInfo,
                            pieceCount: isChineseMedicine(hisGoodsInfo) ? hisPurchaseCount : '',
                            packageCount: isChineseMedicine(hisGoodsInfo) ? '' : hisPurchaseCount,
                            profitRat: hisGoodsStockStatInfo && hisGoodsStockStatInfo.profitRatio,
                            recentAvgSell: hisGoodsStockStatInfo && hisGoodsStockStatInfo.avgSell,
                            turnoverDays: hisGoodsStockStatInfo && hisGoodsStockStatInfo.turnoverDays,
                            stockPackageCount:
                                hisGoodsStockStatInfo &&
                                hisGoodsStockStatInfo.currentStock &&
                                hisGoodsStockStatInfo.currentStock.packageCount,
                            stockPieceCount:
                                hisGoodsStockStatInfo &&
                                hisGoodsStockStatInfo.currentStock &&
                                hisGoodsStockStatInfo.currentStock.pieceCount,
                            lastPackageCostPrice: hisGoodsStockStatInfo && hisGoodsStockStatInfo.lastPackageCostPrice,
                            lastSupplierName: hisGoodsStockStatInfo && hisGoodsStockStatInfo.lastSupplierName,
                            minPackageCostPrice: hisGoodsStockStatInfo && hisGoodsStockStatInfo.minPackageCostPrice,
                            recommend: hisGoodsStockStatInfo && hisGoodsStockStatInfo.recommendView,
                            mallGoodsInfo,
                        };
                    });
                    this.order.plan = this.order.plan || {
                        name: '', isComplete: 0,
                    };
                    this.planId = this.order.plan && this.order.plan.planId;

                    this.isFirstPrint = true;
                    if (this.goodsId) {
                        this.sortOrderList();
                    }
                    if (
                        this.order.mallOrderInfo &&
                        this.order.mallOrderInfo.payExpireTime &&
                        needTimer &&
                        this.order.status === purchaseOrderStatus.MALL_WAITING_PAY
                    ) {
                        const time = Math.ceil((new Date(this.order.mallOrderInfo.payExpireTime) - new Date()) / 1000);
                        if (time) {
                            this.lockOrderCountdown(time);
                        }
                    }
                    this.$nextTick(() => {
                        this.loading = false;
                    });
                } catch (e) {
                    this.loading = false;
                }
            },
            /**
             * @desc  打印函数
             * <AUTHOR>
             * @date 2018/11/24 10:36:02
             * @params
             * @return
             */
            print() {
                AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates.goodsPurchase,
                    printConfigKey: ABCPrintConfigKeyMap.CG,
                    data: this.order,
                });
            },
            // 对自定义表头数据，根据不同业务状态做一些过滤
            customDataFilterFn(tree = []) {
                return tree.reduce((res, item) => {
                    // 非商城场景下过滤库存信息中的 实采商品、实采商品规格、实际采购量、进价金额
                    if (item.key === 'goodsStockInfo' && !this.isMallPurchase) {
                        item.children = item.children.filter((e) => !(
                            [
                                'goodsStockInfo.realPurchaseCount',
                                'goodsStockInfo.purchaseGoodsName',
                                'goodsStockInfo.purchaseGoodsDisplaySpec',
                                'goodsStockInfo.purchaseGoodsCostPrice',
                            ].includes(e.key)
                        ));
                    }
                    res.push(item);
                    return res;
                }, []);
            },

            exportCG() {
                PurchaseAPI.exportById(this.orderId);
            },

            customExportCG() {
                PurchaseAPI.exportAccompanyingBillById(this.orderId);
            },

            // 取消关闭弹窗
            closeDialog() {
                this.$emit('refresh');
            },
            sortOrderList() {
                const newOrderList = Clone(this.order.list);
                newOrderList.sort((a, b) => {
                    return (b.goodsId === this.goodsId) - (a.goodsId === this.goodsId);
                });
                this.order.list = Clone(newOrderList);
            },
            formatTableHeader(headers = []) {
                const tableConfig = headers.reduce((res, item) => {
                    const {
                        label,prop,width,titleAlign,align,cellStyle = {},
                    } = item;

                    // 规格（displaySpec）、生产厂家（manufacture）合并到商品名称（displayName）下面展示
                    // 最近供应商（lastSupplierName）合并到最近进价下面展示
                    // 实际采购商品（realPurchaseGoodsName）、实际采购商品规格（realPurchaseGoodsDisplaySpec）合并到实际采购量（realPurchaseCount）下面展示
                    if (['displaySpec','manufacture','lastSupplierName','realPurchaseGoodsName','realPurchaseGoodsDisplaySpec'].includes(item.prop)) {
                        return res;
                    }
                    // 不是商城采购单过滤，实际采购量（realPurchaseCount）、采购价格（realPurchaseGoodsCostPrice）
                    if (!this.isMallPurchase && ['realPurchaseCount','realPurchaseGoodsCostPrice'].includes(item.prop)) {
                        return res;
                    }

                    res.push({
                        label,
                        prop,
                        width: prop === 'displayName' ? '' : width,// 商品名称自适应
                        expend: prop === 'dispGoodsCount',
                        titleStyle: {
                            textAlign: titleAlign || 'left',
                            paddingLeft: '4px',
                            paddingRight: '4px',
                        },
                        bodyStyle: {
                            textAlign: align || 'left',
                            paddingLeft: '4px',
                            paddingRight: '4px',
                            ...cellStyle,
                        },
                    });

                    return res;
                }, []);

                // 表格列增加设置按钮
                tableConfig.push({
                    label: '',
                    prop: 'setting',
                    width: 30,
                    expend: true,
                    titleStyle: {
                        'text-align': 'right',
                        'margin-left': 'auto',
                    },
                });

                return tableConfig;
            },

            /**
             * @desc 查看商城订单详情
             */
            async fetchMallInfo() {
                const mall = await this.$abcPlatform.module.mall;
                mall.service.OrderDetailDialog({
                    orderId: this.mallOrderInfo.orderId,
                });
            },
            /**
             * @desc 总部在采购计划未完成前，可以修改药品采购数量
             * <AUTHOR>
             * @date 2019/07/29 17:44:30
             */
            editCurrentItem(item, index) {
                this.currentEditItem = Clone(item);
                this.currentEditIndex = index;
            },
            /**
             * @desc 申请售后服务
             */
            async afterSellHandler() {
                const mall = await this.$abcPlatform.module.mall;
                mall.service.AfterSellDialog({
                    orderId: this.mallOrderInfo.orderId,
                    orderStatus: this.mallOrderInfo.mallOrderStatus,
                    successHandle: async () => {
                        await this.fetchData();
                    },
                });
            },
            async reOrderHandler() {
                try {
                    const { data } = await PurchaseAPI.reOrder(this.orderId);
                    const {
                        errorPurchaseItems, hisPurchaseOrderId, buyGoodsCount,
                    } = data;
                    if (!hisPurchaseOrderId) return;
                    let tips = '';
                    if (errorPurchaseItems && errorPurchaseItems.length) {
                        tips = `其中${errorPurchaseItems.length}种商品已下架，将提交其它${buyGoodsCount}种商品进行下单`;
                    }
                    if (tips) {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: tips,
                            onConfirm: async () => {
                                await this.mallReOrder(hisPurchaseOrderId);
                            },
                        });
                    } else {
                        await this.mallReOrder(hisPurchaseOrderId);
                    }
                } catch (e) {
                    console.warn('重新下单失败', e);
                }
            },
            async mallReOrder(hisPurchaseOrderId) {
                const mall = await this.$abcPlatform.module.mall;

                // 返回的 mall 对象即为 new AbcModule 时传入的 provide 对象
                mall.service.OrderSettleDialog({
                    purchaseOrderId: hisPurchaseOrderId,
                    paySuccess: () => {
                        this.showDialog = false;
                        this.$emit('refresh');
                    },
                    payCancel: async () => {
                        await this.fetchData();
                    },
                    onClose: async () => {
                        await this.fetchData();
                    },
                });
            },

            /**
             * @desc 取消订单
             */
            async cancelOrder() {
                const mall = await this.$abcPlatform.module.mall;
                mall.service.CancelOrderDialog({
                    orderId: this.mallOrderInfo.orderId,
                    successHandle: async () => {
                        this.showDialog = false;
                        this.$emit('refresh');
                    },
                });
            },
            /**
             * @desc 付款
             */
            async payHandler() {
                // 获取 module，返回的一个 Promise，因为可能该 Module 还没有加载，需要异步加载，交互上可以加一个 Loading 用于更好的用户体验
                const mall = await this.$abcPlatform.module.mall;

                // 返回的 mall 对象即为 new AbcModule 时传入的 provide 对象
                mall.service.CreatePayMallOrderDialog({
                    parent: this,
                    orderIds: [this.order.mallOrderInfo.orderId],
                    allowAutomaticGenerationAttorneyPaper: true,
                    onClose: () => {
                        this.fetchData(false);
                        // this.showDialog = false;
                        // this.$emit('refresh');
                    },
                });
            },
            /**
             * @desc 签收入库
             * <AUTHOR>
             * @date 2020-12-23 09:38:34
             */
            inboundHandler() {
                createMallGoodsInOrder({
                    mallOrderId: this.mallOrderInfo.orderId,
                    success: () => {
                        this.fetchData();
                    },
                });
            },
        },

        filters: {
            currentCount(item) {
                if (!item) return '';

                let {
                    stockPieceCount, stockPackageCount,
                } = item;
                const { goods } = item;

                let { pieceUnit } = goods || {};
                const {
                    packageUnit, type, subType,
                } = goods || {};

                stockPackageCount = stockPackageCount || 0;
                stockPieceCount = stockPieceCount || 0;

                if (stockPackageCount < 0 || stockPieceCount < 0) {
                    return (
                        `-${
                            this.currentCount({
                                stockPieceCount: Math.abs(stockPieceCount),
                                stockPackageCount: Math.abs(stockPackageCount),
                                goods,
                            })}`
                    );
                }

                if (type === 1 && subType === 2) {
                    pieceUnit = pieceUnit || 'g';
                }

                stockPieceCount = Number(stockPieceCount) || 0;
                stockPackageCount = Number(stockPackageCount) || 0;

                let out = '';
                if (stockPackageCount > 0) {
                    out += stockPackageCount + packageUnit;
                }
                if (stockPieceCount > 0) {
                    out += stockPieceCount + pieceUnit;
                }
                if (stockPackageCount === 0 && stockPieceCount === 0) {
                    if (type === 1 && subType === 2) {
                        out += stockPieceCount + pieceUnit;
                    } else {
                        out += stockPackageCount + packageUnit;
                    }
                }
                return out;
            },
        },
    };
</script>
<style lang="scss">
@import "./order";
</style>
