<template>
    <div class="deliver-info-detail">
        <span class="courier-company"> 快递配送： </span>
        <abc-popover
            v-if="logistics.logisticsNo"
            trigger="hover"
            placement="bottom-start"
            theme="yellow"
            @input="
                (val) => {
                    viewOrderLogistics(val);
                }
            "
        >
            <span
                slot="reference"
                class="logistics-no ellipsis"
                style="color: #2680f7;"
                :title="logistics.logisticsNo"
            >{{ logistics.logisticsNo }}</span>
            <div v-if="logisticsTraceList" class="mall-order-logistics-popper">
                <div v-abc-loading="viewOrderLogisticsLoading" class="order-logistics-wrapper">
                    <section
                        v-for="(it, index) in logisticsTraceList"
                        :key="it.ftime"
                        :class="{
                            active: index === 0,
                        }"
                    >
                        <p>{{ it.ftime }}</p>
                        <p>
                            {{ it.context }}
                        </p>
                    </section>
                </div>
            </div>
        </abc-popover>
        <span class="courier-company" style="margin-left: 8px;">{{ logistics.logisticsCompanyName }}</span>
    </div>
</template>

<script>
    import { deliveryInfoStr } from '../utils';
    export default {
        name: 'DeliveryDetail',
        props: {
            logistics: Object,
            orderId: String,
        },
        data() {
            return {
                logisticsTraceList: null,
                viewOrderLogisticsLoading: false,
            };
        },
        methods: {
            deliveryInfoStr,
            /**
             * @desc 查看物流详情
             * <AUTHOR> Yang
             * @date 2020-12-28 16:03:04
             */
            viewOrderLogistics(showPopover, order) {
                if (showPopover && !this.logisticsTraceList) {
                    this.fetchOrderLogistics(order);
                }
            },

            async fetchOrderLogistics() {
                this.viewOrderLogisticsLoading = true;
                const mall = await this.$abcPlatform.module.mall;
                const { data } = await mall.service.fetchOrderLogistics(this.orderId, this.logistics.id);
                this.logisticsTraceList = data.traceList;
                this.viewOrderLogisticsLoading = false;
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme';
    @import 'src/styles/mixin';

    .courier-company {
        .blue {
            color: $theme2;
        }
    }

    .deliver-info-detail {
        span.logistics-no {
            display: inline-block;
            max-width: 90px;
            font-size: 14px;
        }
    }

    .mall-order-logistics-popper {
        width: 240px;
        height: 264px;
        padding: 12px;
        overflow-y: auto;
        overflow-y: overlay;

        @include scrollBar;

        .order-logistics-wrapper {
            border-left: 1px dashed $P3;

            p {
                font-size: 12px;
                line-height: 16px;
                color: $T2;
            }

            p + p {
                margin-top: 4px;
            }

            section + section {
                margin-top: 12px;
            }

            section {
                position: relative;
                padding-left: 10px;

                &.active {
                    p {
                        color: $T1;
                    }

                    &::before {
                        background-color: $theme2;
                    }
                }

                &::before {
                    position: absolute;
                    top: 3px;
                    left: -5px;
                    width: 9px;
                    height: 9px;
                    content: ' ';
                    background-color: $P1;
                    border: 1px solid #ffffff;
                    border-radius: 9px;
                }
            }

            .abc-button {
                margin: 12px 0 0 10px;
            }
        }
    }
</style>
