<template>
    <div class="config-component-navbar">
        <config-component-title v-model="currentConfig.name"></config-component-title>

        <abc-form style="padding: 16px;">
            <abc-form-item label="页面名称">
                <abc-input v-model="currentConfig.content" :width="288" :max-length="10"></abc-input>
            </abc-form-item>
        </abc-form>
    </div>
</template>

<script>
    import ConfigComponentTitle from './title.vue';
    import Clone from 'utils/clone';
    export default {
        name: 'ConfigComponentNavbar',
        components: {
            ConfigComponentTitle,
        },
        props: {
            config: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        data() {
            return {
                currentConfig: Clone(this.config),
            };
        },
        watch: {
            currentConfig: {
                handler(val) {
                    this.$emit('change', val);
                },
                deep: true,
            },
        },
    };
</script>

<style lang="scss">
.config-component-navbar {
    // TODO
}
</style>
