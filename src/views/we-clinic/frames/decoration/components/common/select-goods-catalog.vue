<template>
    <abc-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="选择跳转的商品分类"
        append-to-body
        size="xlarge"
    >
        <goods-classification-table
            ref="goodsClassificationTable"
            is-single-select
            :selected-ids.sync="currentSelectedIds"
            :show-operate="false"
        ></goods-classification-table>

        <div slot="footer" class="dialog-footer">
            <abc-button :disabled="!currentSelectedIds.length" @click="handleClickConfirm">
                确定
            </abc-button>
            <abc-button variant="ghost" @click="dialogVisible = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import GoodsClassificationTable
        from 'views/we-clinic/frames/shop/goods-manage/goods-classification/classification-table.vue';

    export default {
        components: {
            GoodsClassificationTable,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            selectedIds: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                currentSelectedIds: this.selectedIds,
            };
        },
        computed: {
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        created() {
            this.$store.dispatch('weShop/fetchMallGoodsTypesList');
        },
        methods: {
            handleClickConfirm() {
                this.dialogVisible = false;
                this.$emit('change', this.$refs.goodsClassificationTable.selectedItems);
            },
        },
    };
</script>
