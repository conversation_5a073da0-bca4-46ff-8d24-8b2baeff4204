<template>
    <div class="jump-card">
        <abc-flex :gap="8">
            <div class="jump-card_image" @click="handleImageClick">
                <img
                    v-if="currentVal.imageUrl"
                    :src="currentVal.imageUrl"
                    alt="图片"
                    style="width: 100%; height: 100%; object-fit: cover;"
                />
                <abc-icon v-else icon="s-b-add-line-medium" :size="20"></abc-icon>
                <file-upload
                    ref="fileUploader"
                    :ref-id="currentVal.id"
                    :select-file-type="'image'"
                    :filter-func="fileFilter"
                    @error="handleUploadError"
                    @progress="handleProgress"
                    @uploadSuccess="handleUploadSuccess"
                ></file-upload>

                <div v-if="currentVal.imageUrl" class="jump-card_image-tip">
                    更换图片
                </div>
            </div>
            <abc-flex
                vertical
                justify="space-between"
                :gap="12"
                style="flex: 1;"
            >
                <abc-flex vertical>
                    <abc-flex style="width: 100%;" justify="space-between" align="center">
                        <abc-text theme="gray">
                            {{ titleLabel }}
                        </abc-text>
                        <abc-flex>
                            <template v-if="showSort">
                                <abc-button
                                    v-if="sortIndex !== 0"
                                    variant="text"
                                    icon="s-move-up-line"
                                    size="small"
                                    theme="default"
                                    style="margin-left: 0;"
                                    icon-color="var(--abc-color-T3)"
                                    @click="handleSortUp"
                                >
                                </abc-button>
                                <abc-button
                                    v-if="sortIndex !== sortLength - 1"
                                    variant="text"
                                    icon="s-move-down-line"
                                    size="small"
                                    theme="default"
                                    icon-color="var(--abc-color-T3)"
                                    style="margin-left: 0;"
                                    @click="handleSortDown"
                                >
                                </abc-button>
                            </template>
                            <abc-button
                                variant="text"
                                icon="s-delete-3-line"
                                size="small"
                                theme="default"
                                icon-color="var(--abc-color-T3)"
                                style="margin-left: 0;"
                                @click="handleDelete"
                            >
                            </abc-button>
                        </abc-flex>
                    </abc-flex>

                    <abc-input v-model="currentVal.imageTitle" style="width: 100%;"></abc-input>
                </abc-flex>

                <abc-flex vertical>
                    <abc-text theme="gray">
                        跳转链接
                    </abc-text>

                    <abc-space>
                        <abc-tag-v2
                            v-if="currentDisplayValue"
                            variant="outline"
                            theme="default"
                            shape="square"
                            icon="n-classify-fill"
                            ellipsis
                            style="max-width: 142px;"
                            closable
                            @close="handleClose"
                        >
                            {{ currentDisplayValue }}
                        </abc-tag-v2>

                        <abc-cascader
                            :options="JUMP_TYPE_OPTIONS"
                            :props="{
                                children: 'children',
                                label: 'label',
                                value: 'value'
                            }"
                            :placeholder="currentDisplayValue ? '修改' : '设置'"
                            reference-mode="text"
                            reference-icon="none"
                            :reference-text-style="{
                                color: $store.state.theme.style.B1
                            }"
                            :width="46"
                            :panel-max-height="200"
                            @change="handleChange"
                        >
                        </abc-cascader>
                    </abc-space>
                </abc-flex>
            </abc-flex>
        </abc-flex>

        <micro-setting-header-img-url-dialog
            v-if="microSettingHeaderImgUrlDialogVisible"
            v-model="microSettingHeaderImgUrlDialogVisible"
            :header-img-url="currentVal.imageUrl"
            :jump-app-id="currentVal.jumpConfig?.appId"
            :header-jump-url="currentVal.jumpConfig?.path"
            @modifyHeaderJumpConfig="handleModifyHeaderJumpConfig"
        ></micro-setting-header-img-url-dialog>

        <abc-image-cropper
            v-if="checkedBlob"
            :blob="checkedBlob"
            :option="cropperOption"
            @confirm="handleImgCrop"
            @cancel="handleImgCropCancel"
        ></abc-image-cropper>

        <select-we-shop-goods-dialog
            v-if="selectSystemGoodsDialogVisible"
            v-model="selectSystemGoodsDialogVisible"
            is-single-select
            :select-goods-list="currentSelectGoodsList"
            title="选择商品"
            @change="handleModifyGoodsJumpConfig"
        ></select-we-shop-goods-dialog>

        <select-goods-catalog
            v-if="selectGoodsCatalogDialogVisible"
            v-model="selectGoodsCatalogDialogVisible"
            :selected-ids="currentVal.jumpConfig?.paramRelatedId ? [currentVal.jumpConfig.paramRelatedId] : []"
            @change="handleModifyCatalogJumpConfig"
        >
        </select-goods-catalog>
    </div>
</template>

<script>
    import {
        JUMP_TYPE, JUMP_TYPE_OPTIONS,
    } from 'views/we-clinic/frames/decoration/components/decoration/constant';
    import {
        getDefaultJumpConfig,
    } from 'views/we-clinic/frames/decoration/components/decoration/utils';
    import MicroSettingHeaderImgUrlDialog
        from 'views/settings/micro-clinic/micro-clinic-decoration/micro-clinic-homepage/common/micro-setting-header-img-url-dialog.vue';
    import FileUpload from 'views/settings/micro-clinic/layout/upload-file.vue';
    import AbcImageCropper from 'views/layout/editor-img/index.vue';
    import { mapGetters } from 'vuex';
    import { GOODS_DISCOUNT_TYPE } from 'views/we-clinic/frames/shop/marketing-activities/constants';
    const SelectWeShopGoodsDialog = () => import('views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-goods-dialog.vue');
    const SelectGoodsCatalog = () => import('./select-goods-catalog.vue');


    export default {
        components: {
            AbcImageCropper,
            FileUpload,
            MicroSettingHeaderImgUrlDialog,
            SelectWeShopGoodsDialog,
            SelectGoodsCatalog,
        },
        props: {
            value: {
                type: Object,
                default: getDefaultJumpConfig,
            },
            titleLabel: {
                type: String,
                default: '图片标题',
            },
            cropperOption: {
                type: Object,
                default: () => ({
                    autoCropWidth: 686,
                    autoCropHeight: 344,
                    mode: '686px 344px',
                    fixedNumber: [686, 344],
                }),
            },
            sortIndex: {
                type: Number,
                default: 0,
            },
            sortLength: {
                type: Number,
                default: 1,
            },
            showSort: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                microSettingHeaderImgUrlDialogVisible: false,

                checkedBlob: null,
                checkedFile: null,

                uploadStatus: {
                    progress: 0,
                },

                selectSystemGoodsDialogVisible: false,
                selectGoodsCatalogDialogVisible: false,
            };
        },
        computed: {
            GOODS_DISCOUNT_TYPE() {
                return GOODS_DISCOUNT_TYPE;
            },
            ...mapGetters('weClinic', ['navigationMenu']),
            JUMP_TYPE_OPTIONS() {
                return JUMP_TYPE_OPTIONS.map((item) => {
                    if (item.value === JUMP_TYPE.SPECIFY_PAGE) {
                        return {
                            ...item,
                            children: this.navigationMenu.map((menu) => ({
                                label: menu.name,
                                value: menu.jumpConfig.paramRelatedId,
                            })),
                        };
                    }
                    return item;
                });
            },
            currentVal: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            currentDisplayValue() {
                if (!this.currentVal.jumpConfig) return '';
                const {
                    pathType, pathName,
                } = this.currentVal.jumpConfig;
                if (pathType === JUMP_TYPE.CUSTOM_PAGE) {
                    return '自定义';
                }
                if (pathType === JUMP_TYPE.SPECIFY_PAGE) {
                    return pathName;
                }
                if (pathType === JUMP_TYPE.GOODS_PAGE) {
                    return `${this.currentVal.jumpConfig.pathName}`;
                }
                if (pathType === JUMP_TYPE.GOODS_CATEGORY_PAGE) {
                    return pathName;
                }
                return '';
            },

            currentSelectGoodsList() {
                return this.currentVal.jumpConfig ? [{
                    goodsId: this.currentVal.jumpConfig.paramRelatedId,
                    name: this.currentVal.jumpConfig.pathName,
                    goodsDiscountType: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                }] : [];
            },
        },
        methods: {
            handleSortUp() {
                this.$emit('sort', this.sortIndex, this.sortIndex - 1);
            },
            handleSortDown() {
                this.$emit('sort', this.sortIndex, this.sortIndex + 1);
            },
            handleChange(val) {
                const [ level1, level2 ] = val;
                if (level2) {
                    const { value } = level2;
                    const currentSelectMenu = this.navigationMenu.find((menu) => menu.jumpConfig.paramRelatedId === value);
                    // 没有设置图片才设置图片
                    // 避免覆盖上传的图片
                    // 如果图片是内置的则可以替换
                    if (!this.currentVal.imageUrl || this.navigationMenu.some((menu) => menu.iconUrl === this.currentVal.imageUrl)) {
                        this.currentVal.imageUrl = currentSelectMenu.iconUrl;
                    }
                    this.currentVal.imageTitle = currentSelectMenu.name;
                    this.currentVal.jumpConfig = currentSelectMenu.jumpConfig;
                    this.currentVal.jumpConfig.pathType = JUMP_TYPE.SPECIFY_PAGE;
                } else {
                    if (level1.value === JUMP_TYPE.CUSTOM_PAGE) {
                        this.microSettingHeaderImgUrlDialogVisible = true;
                    }
                    if (level1.value === JUMP_TYPE.GOODS_PAGE) {
                        this.selectSystemGoodsDialogVisible = true;
                    }
                    if (level1.value === JUMP_TYPE.GOODS_CATEGORY_PAGE) {
                        this.selectGoodsCatalogDialogVisible = true;
                    }
                }
            },
            handleModifyHeaderJumpConfig(jumpConfig) {
                const {
                    appId, path,
                } = jumpConfig;
                if (!this.currentVal.jumpConfig) {
                    this.currentVal.jumpConfig = getDefaultJumpConfig().jumpConfig;
                }
                this.currentVal.jumpConfig.appId = appId;
                this.currentVal.jumpConfig.path = path;
                this.currentVal.jumpConfig.pathType = JUMP_TYPE.CUSTOM_PAGE;
                this.currentVal.jumpConfig.type = appId ? 0 : 1;
            },
            handleModifyGoodsJumpConfig(goods) {
                this.currentVal.jumpConfig = {
                    pathType: JUMP_TYPE.GOODS_PAGE,
                    paramRelatedId: goods[0]?.goodsId,
                    pathName: goods[0]?.name,
                };
            },
            handleModifyCatalogJumpConfig(catalogs) {
                this.currentVal.jumpConfig = {
                    pathType: JUMP_TYPE.GOODS_CATEGORY_PAGE,
                    paramRelatedId: catalogs[0]?.id,
                    pathName: catalogs[0]?.name,
                };
            },
            fileFilter(file) {
                return new Promise((resolve) => {
                    if (file.size > 1024 * 1024 * 10) {
                        resolve({
                            filterStatus: false,
                            message: '图片不能超过10M',
                        });
                        return;
                    }
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        let blob;
                        if (typeof e.target.result === 'object') {
                            blob = window.URL.createObjectURL(new Blob([e.target.result]));
                        } else {
                            blob = e.target.result;
                        }
                        this.checkedFile = file;
                        this.checkedBlob = blob;
                    };
                    reader.readAsArrayBuffer(file);
                    this.isLoading = true;
                });
            },
            handleImageClick() {
                if (this.uploadStatus.progress) {
                    return;
                }
                this.$refs.fileUploader.selectFile();
            },
            handleImgCropCancel() {
                this.checkedFile = null;
                this.checkedBlob = null;
            },
            handleImgCrop(blob) {
                const file = new File([blob], this.checkedFile.name, {
                    type: this.checkedFile.type,
                });
                this.checkedFile = null;
                this.checkedBlob = null;

                this.$refs.fileUploader.fileUpload(file);
            },
            resetUploadStatus() {
                this.uploadStatus = {
                    progress: 0,
                };
            },
            handleProgress(_, progress) {
                this.uploadStatus = {
                    progress,
                };
            },

            handleUploadError(_, e) {
                this.uploadError = {
                    message: e,
                };
                this.$confirm({
                    content: e,
                    showCancel: false,
                    onConfirm: () => {},
                });
                this.resetUploadStatus();
            },

            handleUploadSuccess(_, url) {
                this.currentVal.imageUrl = url;
                this.resetUploadStatus();
            },

            handleDelete() {
                this.$emit('delete');
            },

            handleClose() {
                this.currentVal.jumpConfig = getDefaultJumpConfig().jumpConfig;
            },
        },
    };
</script>
<style lang="scss">
.jump-card {
    width: 100%;
    height: 138px;
    padding: 8px;
    border: 1px solid $P8;
    border-radius: 5px;

    .jump-card_image {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        overflow: hidden;
        color: $T3;
        cursor: pointer;
        background: var(--abc-color-cp-grey3);
        border: 1px solid $P7;
        border-radius: var(--abc-border-radius-small);

        .jump-card_image-tip {
            position: absolute;
            right: 0;
            bottom: 0;
            left: 0;
            font-size: 12px;
            line-height: 20px;
            color: #ffffff;
            text-align: center;
            visibility: hidden;
            background: rgba(0, 0, 0, 0.4);
        }

        &:hover .jump-card_image-tip {
            visibility: visible;
        }
    }
}
</style>
