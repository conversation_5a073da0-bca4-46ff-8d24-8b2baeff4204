
<template>
    <div class="decoration-home-page-wrapper">
        <decoration-view
            ref="decorationPreview"
            :preview-url="previewUrl"
        >
            <div slot="component">
                <abc-flex vertical :gap="16">
                    <div v-for="group in componentsGroups" :key="group.key">
                        <abc-space class="group-title">
                            <div>
                                {{ group.label }}
                            </div>
                        </abc-space>

                        <abc-space class="component-list-wrapper" :size="10">
                            <abc-flex
                                v-for="item in group.list"
                                :key="item.key"
                                vertical
                                :gap="10"
                                class="component-item-des"
                                direction="vertical"
                                draggable="true"
                                @dragstart.native="e => handleDragStart(e, item.type)"
                                @dragend.native="handleDragend"
                                @click="handleClick(item.type)"
                            >
                                <abc-icon :color="$store.state.theme.style.T3" :icon="item.icon" :size="20"></abc-icon>
                                {{ item.label }}
                            </abc-flex>
                        </abc-space>
                    </div>
                </abc-flex>
            </div>
        </decoration-view>
    </div>
</template>

<script>
    import DecorationView from 'views/we-clinic/frames/decoration/components/decoration/index.vue';

    import { getPreviewBaseUrl } from 'views/settings/micro-clinic/decoration/config.js';
    import {
        WE_CLINIC_COMPONENTS_TYPE,
    } from 'views/we-clinic/frames/decoration/components/decoration/constant';
    import generateUUID from '@/medical-imaging-viewer/cornerstone-core/src/generateUUID';
    import {
        mapGetters, mapState,
    } from 'vuex';
    export default {
        name: 'HomePage',
        components: {
            DecorationView,
        },
        inject: ['$abcPage'],
        data() {
            return {
                currentNode: null,
            };
        },
        computed: {
            ...mapState('weClinic', ['config']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            previewUrl() {
                return `${getPreviewBaseUrl()}/home?mode=decorate&t=${generateUUID()}`;
            },
            supportWeShopPurchase() {
                return this.viewDistributeConfig.WeClinic.supportWeShopPurchase;
            },
            enableWeShop() {
                return this.$abcPage.$store.state.enableWeShop;
            },
            componentsGroups() {
                const baseGroups = {
                    base: {
                        label: '基础组件',
                        type: 'baseComponent',
                        list: [
                            {
                                label: '图片广告',
                                type: WE_CLINIC_COMPONENTS_TYPE.Banner,
                                icon: 's-b-picture-line',

                            },
                            {
                                label: '门店推荐',
                                type: WE_CLINIC_COMPONENTS_TYPE.ClinicRecommend,
                                icon: 's-b-store-line',
                            },
                        ],
                    },
                    business: {
                        label: '业务组件',
                        key: 'businessComponent',
                        list: [
                            {
                                label: '通知公告',
                                type: WE_CLINIC_COMPONENTS_TYPE.Bulletin,
                                icon: 's-b-announcement-line',
                            },
                            {
                                label: '业务导航',
                                type: WE_CLINIC_COMPONENTS_TYPE.BusinessNav,
                                icon: 's-b-navigation-line',
                            },
                            {
                                label: '科室导航',
                                type: WE_CLINIC_COMPONENTS_TYPE.DepartmentNav,
                                icon: 's-b-Department-Navigation-line',
                            },
                            {
                                label: '名医推荐',
                                type: WE_CLINIC_COMPONENTS_TYPE.DoctorRecommend,
                                icon: 's-b-doctor-line',
                            },
                            {
                                label: '机构介绍',
                                type: WE_CLINIC_COMPONENTS_TYPE.Brand,
                                icon: 's-b-data-1-line',
                            },
                        ],
                    },
                };
                if (this.enableWeShop && this.supportWeShopPurchase) {
                    return {
                        ...baseGroups,
                        mall: {
                            label: '商城组件',
                            key: 'mallComponent',
                            list: [
                                {
                                    label: '商品',
                                    type: WE_CLINIC_COMPONENTS_TYPE.MallGoods,
                                    icon: 's-b-commodity-line',
                                },
                            ],
                        },
                    };
                }

                return baseGroups;
            },
        },
        methods: {
            handleDragStart(e, componentType) {
                this.$refs.decorationPreview.handleDragStart(e, componentType);
            },
            handleDragend() {
                this.$refs.decorationPreview.handleDragend();
            },
            handleClick(componentType) {
                this.$refs.decorationPreview.handleClick(componentType);
            },
            async handleSaveDecorationConfig() {
                try {
                    const config = await this.$refs.decorationPreview.getDecorateConfig();
                    await this.$store.dispatch('weClinic/fetchConfig');
                    const updatedResult = await this.$store.dispatch('weClinic/updateHomepageConfig', {
                        ...config,
                        style: this.config.style,
                    });
                    if (updatedResult.error || updatedResult.message) {
                        if (updatedResult.message) {
                            this.$Toast({
                                message: updatedResult.message,
                                type: 'error',
                            });
                        }
                        return;
                    }
                    this.$refs.decorationPreview.updateDecorateConfig(updatedResult);
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.$emit('close');
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/mixin.scss";

.decoration-home-page-wrapper {
    width: 100%;
    height: 100%;

    .group-title {
        padding: 14px 16px;
    }

    .component-list-wrapper {
        display: flex;
        flex-wrap: wrap;
        padding: 0 16px;
    }

    .component-item-des {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 72px;
        height: 72px;
        font-size: 12px;
        cursor: pointer;
        border: 1px solid $P7;
        border-radius: var(--abc-border-radius-small);

        &:hover {
            border-color: $theme2;
        }
    }
}
</style>
