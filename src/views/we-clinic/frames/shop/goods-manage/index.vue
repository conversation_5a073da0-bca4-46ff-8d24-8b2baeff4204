<template>
    <we-clinic-no-permission v-if="!isShowGoodsManage"></we-clinic-no-permission>
    <div
        v-else
        class="goods-manage-wrapper"
    >
        <abc-tabs-v2
            v-abc-sticky-top
            class="tab-wrapper"
            :option="_tabOptions"
            size="huge"
            @change="changeTab"
        ></abc-tabs-v2>
        <router-view></router-view>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import WeClinicNoPermission from 'views/we-clinic/components/we-clinic-no-permission.vue';

    export default {
        name: 'GoodsManage',
        components: { WeClinicNoPermission },

        inject: {
            $abcPage: {
                default: {},
            },
        },

        computed: {
            ...mapGetters([
                'isChainAdmin',
                'isSingleStore',
            ]),
            weClinicModulePermission() {
                return this.$abcPage.$store.state.weClinicModulePermission;
            },

            isShowGoodsManage() {
                return this.weClinicModulePermission.goodsManage;
            },
        },

        created() {
            if (!this.isShowGoodsManage) {
                return;
            }
            this._tabOptions = [
                {
                    label: '商品列表',
                    value: 'goods-list',
                },
            ];

            if (this.isChainAdmin || this.isSingleStore) {
                this._tabOptions.push({
                    label: '商品分类',
                    value: 'goods-classification',
                }, {
                    label: '商品参数',
                    value: 'goods-parameter',
                });
            }
        },
        methods: {
            changeTab(value) {
                this.$router.push({
                    name: value,
                });
            },
        },
    };
</script>

<style lang="scss">
.goods-manage-wrapper {
    .tab-wrapper {
        padding-left: 8px;
    }
}
</style>
