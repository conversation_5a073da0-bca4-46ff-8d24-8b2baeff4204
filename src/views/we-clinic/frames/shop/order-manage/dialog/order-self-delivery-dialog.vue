<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        title="自提"
        size="small"
        class="we-clinic-self-delivery-tool-dialog-wrapper"
    >
        <abc-flex justify="center" vertical style="text-align: center;">
            <abc-flex vertical :gap="24">
                <abc-text bold>
                    请将自提码对准扫码盒
                </abc-text>
                <abc-flex
                    vertical
                    justify="center"
                    align="center"
                    :gap="8"
                >
                    <div class="image-scan-wrapper">
                        <img src="~assets/images/icon/<EMAIL>" alt="" />
                        <img class="lighting" src="~assets/images/icon/<EMAIL>" />
                    </div>
                    <abc-button
                        variant="text"
                        theme="default"
                        @click="openInput"
                    >
                        手动输入自提码
                    </abc-button>
                </abc-flex>
            </abc-flex>
        </abc-flex>
        <abc-flex slot="footer" justify="flex-end">
            <abc-button variant="ghost" @click="showDialog = false">
                取消
            </abc-button>
        </abc-flex>
        <div
            v-if="showCodeInput"
            class="pay-code-wrapper"
        >
            <abc-form
                v-abc-click-outside="
                    () => { showCodeInput = false }
                "
            >
                <abc-form-item required>
                    <abc-input
                        ref="barcodeInput"
                        v-model="inputBarcode"
                        :width="208"
                        :input-custom-style="{
                            paddingRight: '45px'
                        }"
                        type="number"
                        @keyup.enter.native="sendMallOrder"
                    >
                        <template #appendLabel>
                            <abc-button
                                class="input-append-unit"
                                type="text"
                                @click="sendMallOrder"
                            >
                                确认
                            </abc-button>
                        </template>
                    </abc-input>
                </abc-form-item>
            </abc-form>
        </div>
    </abc-dialog>
</template>
<script>
    import WeShopAPI from 'api/we-shop';
    import BarcodeScanner from 'utils/scanner-barcode-detector';

    export default {
        name: 'OrderSelfDeliveryDialog',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            orderId: {
                type: String,
                default: -1,
            },
        },
        data() {
            return {
                isLoading: false,
                isBtnLoading: false,
                orderDetail: {},
                inputBarcode: '',
                showCodeInput: false,
                reqData: {
                    items: [
                        {
                            dealUnitCount: '',
                            id: '',
                        },
                    ],
                    orderId: '',
                    selfPickUpCode: '',
                },
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        created() {
            this.getOrderDetail();
        },
        mounted() {
            const barcodeScanner = new BarcodeScanner((barcode) => {
                this.inputBarcode = barcode;
                if (!this.showCodeInput) {
                    this.sendMallOrder();
                }
            }, () => {
                return true;
            });

            barcodeScanner.startDetect();
            this.$on('hook:beforeDestroy', () => {
                barcodeScanner.destoryDetect();
            });
        },
        methods: {
            async getOrderDetail() {
                try {
                    this.isLoading = true;
                    const res = await WeShopAPI.fetchMallOrder(this.orderId);
                    this.orderDetail = res.data;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.isLoading = false;
                }
            },

            openInput() {
                this.showCodeInput = true;
                this.$nextTick(() => {
                    this.$refs.barcodeInput.$el.querySelector('input').focus();
                });
            },

            async sendMallOrder() {
                try {
                    this.isBtnLoading = true;
                    this.reqData.orderId = this.orderId;
                    this.reqData.selfPickUpCode = this.inputBarcode;
                    this.reqData.items = this.orderDetail.items.map((item) => {
                        return {
                            dealUnitCount: item.unitCount,
                            id: item.id,
                        };
                    });
                    await WeShopAPI.sendMallOrder(this.orderId,this.reqData);
                    this.$Toast({
                        type: 'success',
                        message: '自提成功',
                    });
                    this.$emit('input',false);
                    this.$emit('refresh');
                } catch (e) {
                    console.error(e);
                } finally {
                    this.isBtnLoading = false;
                }
            },
        },
    };
</script>
<style lang="scss">
.we-clinic-self-delivery-tool-dialog-wrapper {
    .image-scan-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 120px;
        background-color: #f5f7fb;
        border-radius: 50%;

        img {
            position: absolute;
            width: 54px;
            height: 54px;
        }

        .lighting {
            animation: scanner-light 0.6s linear 0.6s infinite alternate;
        }
    }

    .pay-code-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: hsla(0, 0%, 100%, 0.7);
        transition: opacity 0.3s;

        .abc-form {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 208px;
            background: #ffffff;
            border-bottom-right-radius: 6px;
            border-bottom-left-radius: 6px;
            box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.24);
            transform: translate(-50%, -50%);
        }

        .abc-form-item {
            margin-right: 0;
            margin-bottom: 0;
        }

        .input-append-unit {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 32px;
        }
    }
}
</style>
