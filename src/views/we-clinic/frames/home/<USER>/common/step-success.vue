<template>
    <div class="step-success-type">
        <div class="step-success-type-content">
            <abc-icon
                size="14"
                icon="chosen"
                style="margin-right: 4px;"
                color="#1EC761"
            ></abc-icon>
            <slot></slot>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'StepSuccess',
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';

.step-success-type {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 104px;
    height: 32px;
    color: $G2;

    &-content {
        display: flex;
        align-items: center;
        height: 32px;
        line-height: 32px;
        text-align: center;
    }
}
</style>
