<template>
    <abc-layout class="common-padding-container" preset="page-table">
        <abc-layout-header>
            <stat-toolbar
                ref="statToolbarRef"
                :enable-features="toolbarFeatures"
                :date-filter.sync="params.dateFilter$"
                :patient-placeholder="'患者'"
                :export-task-type="exportTaskType"
                :handle-export="handleExport"
                @change-date="handleDateChange"
            >
                <template #prepend>
                    <biz-select-tabs
                        v-model="currentDispensingRole"
                        :options="dispensingRoleOptions"
                        @change="handleDispensingRoleChange"
                    ></biz-select-tabs>
                </template>
                <clinic-select
                    v-if="isChainAdmin"
                    slot="custom-clinic"
                    v-model="params.clinicId"
                    :clinic-list="clinicOptions"
                    @change="handleClinicChange"
                >
                </clinic-select>
                <filter-select
                    slot="patient"
                    :key="roleEmployeeName"
                    v-model="params.employeeId"
                    :width="120"
                    :placeholder="roleEmployeeName"
                    :options="operationPersonOptions"
                    @change="handleEmployeeChange"
                >
                </filter-select>
                <filter-select
                    v-if="!isDispensingRole"
                    slot="patient"
                    key="dispensingStatus"
                    v-model="params.dispensingStatus"
                    :width="120"
                    placeholder="发药状态"
                    :options="dispensingStatusOptions"
                    @change="handleDispensingStatusChange"
                >
                </filter-select>
                <filter-select
                    v-if="showOperationTypeSelect"
                    slot="patient"
                    key="processType"
                    v-model="params.processType"
                    :width="120"
                    :placeholder="params.operationType === OperationTypeEnum.PROCESS ? '加工服务' : '加工类型'"
                    :options="processTypeOptions"
                    @change="handleProcessTypeChange"
                >
                </filter-select>
                <filter-select
                    v-if="params.operationType === OperationTypeEnum.PROCESS"
                    slot="patient"
                    key="processUsage"
                    v-model="params.processUsage"
                    :width="120"
                    placeholder="加工方式"
                    :options="processUsageOptions"
                    @change="handleProcessTypeChange"
                >
                </filter-select>
                <filter-select
                    v-if="showPrescriptionTypeSelect"
                    slot="patient"
                    key="prescriptionType"
                    v-model="params.prescriptionType"
                    :width="120"
                    placeholder="处方类型"
                    :options="prescriptionTypeOptions"
                    @change="handlePrescriptionTypeChange"
                >
                </filter-select>
                <stat-dimension-picker
                    slot="right"
                    v-model="params.dimension"
                    :options="dimensionOptions"
                    @change="handleDimensionChange"
                ></stat-dimension-picker>
            </stat-toolbar>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="tableFixed2Ref"
                :loading="loading"
                :data-list="tableData"
                :render-config="tableRenderHeader"
                :summary="displaySummaryData"
                :summary-render-keys="summaryRenderKeys"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="tablePagination"
                :count="count"
                :class="{ 'show-total': true }"
                @current-change="handlePageIndexChange"
            >
                <ul slot="tipsContent">
                    <li>
                        共 <span>{{ count }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import { mapGetters } from 'vuex';
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import StatDimensionPicker from 'views/statistics/common/stat-dimension-picker/stat-dimension-picker';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import PharmacyAPI from 'views/statistics/core/api/pharmacy.js';
    import ClinicSelect from 'views/statistics/common/clinic-select/clinic-select';
    import {
        resolveHeader, resolveHeaderV2,
    } from 'views/statistics/utils.js';
    import { isEqual } from 'utils/lodash';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import { getSummaryRenderKeys } from 'utils/table';
    import {
        dispensingEmployeeStaticConfig,
        deployEmployeeStaticConfig, processEmployeeStaticConfig, pharmacyDescriptionMap,
    } from 'views/statistics/performance/pharmacy/constants';
    import BizSelectTabs from '@/components-composite/biz-select-tabs/src/views/index.vue';

    const OperationTypeEnum = {
        CHECK: 3,
        PROCESS: 0,
        DEPLOY: 9,
        DISPENSING: 2,
    };
    const OperationTypeNameEnum = {
        3: '审核',
        0: '加工',
        9: '调配',
        2: '发药',
    };

    export default {
        components: {
            BizSelectTabs,
            StatToolbar,
            StatDimensionPicker,
            ClinicSelect,
            FilterSelect,
        },
        mixins: [ClinicTypeJudger, PickerOptions, DateParamsMixins],
        data() {
            return {
                OperationTypeEnum,
                params: {
                    dimension: 'employee',
                    clinicId: '',
                    employeeId: '',
                    dispensingStatus: '',
                    prescriptionType: '',
                    processType: '',
                    processUsage: '',
                    operationType: OperationTypeEnum.DISPENSING,
                    pageIndex: 0,
                    pageSize: 12,
                },
                sortConfig: {
                    orderBy: '',
                    orderType: '',
                },
                dimensionOptions: [
                    {
                        label: 'employee', name: '人员',
                    },
                    {
                        label: 'detail', name: '明细',
                    },
                ],
                loading: false,
                tableHeader: [],
                count: 0,
                exportTaskType: 'achievement-dispensing',
                calcStatTableInfo: {},
                currentDispensingRole: OperationTypeEnum.DISPENSING,
                dispensingStatusOptions: [],
                operationPersonOptions: [],
                prescriptionTypeOptions: [],
                processTypeOptions: [],
                processUsageOptions: [],
                clinicOptions: [],
                tableData: [],
                tableRenderHeader: {},
                summaryData: {},
                roleEmployeeName: '发药人',
            };
        },

        computed: {
            ...mapGetters('viewDistribute', ['featureProcess', 'viewDistributeConfig']),

            showProcessStat() {
                return this.viewDistributeConfig.Statistics.showProcessStat;
            },
            toolbarFeatures() {
                const features = [StatToolbar.Feature.DATE, StatToolbar.Feature.EXPORT];
                return features;
            },
            tablePagination() {
                return {
                    showTotalPage: false,
                    pageIndex: !this.isEmployeeTab ? this.params.pageIndex : 0,
                    pageSize: !this.isEmployeeTab ? this.params.pageSize : 0,
                    count: this.count,
                };
            },
            displaySummaryData() {
                if (this.isEmployeeTab) {
                    return this.tableData.length ? this.summaryData : null;
                }
                return null;

            },
            summaryRenderKeys() {
                return getSummaryRenderKeys(this.tableHeader);
            },
            renderTypeList() {
                return {
                    'slicesDoseAndUnitCountDescRender': () => {
                        return (
                         <div>
                            <p>
                               发药单中项目总数量合计。用来评估发药人员的工作量。
                            </p>
                            <p>例：</p>
                            <p>
                                * 发药单A中 三七 30g，当归 20g，共3剂，味数x剂数为 6。
                            </p>
                            <p>
                                * 发药单B中 三七 50g，当归 30g，共2剂，味数x剂数为 4。
                            </p>
                            <p>* 2笔发药单味数x剂数累计为 10</p>
                        </div>
                        );
                    },
                    'typeNumberDescRender': () => {
                        return (
                         <div>
                            <p>
                                发药单中的项目种类合计。用来评估发药人员的工作量。
                            </p>
                            <p>例：</p>
                            <p>
                                * 发药单A中 三七 30g，阿莫西林
                                3盒，发药种数统计为 2。
                            </p>
                            <p>
                                * 发药单B中 三七 50g，阿莫西林
                                1盒，发药种数统计为 2。
                            </p>
                            <p>* 2笔发药单发药种数累计为 4</p>
                        </div>
                        );
                    },
                };
            },
            isDispensingRole() {
                return this.params.operationType === OperationTypeEnum.DISPENSING;
            },

            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.currentClinic?.clinicId;
            },
            showOperationTypeSelect() {
                return this.isDetailTab && !this.isDispensingRole && this.params.operationType === OperationTypeEnum.PROCESS || this.params.operationType === OperationTypeEnum.PROCESS;
            },
            showPrescriptionTypeSelect() {
                const { operationType } = this.params;
                return this.isDetailTab && !this.isDispensingRole && (operationType === OperationTypeEnum.CHECK || operationType === OperationTypeEnum.DEPLOY);
            },
            isEmployeeTab() {
                return this.params.dimension === 'employee';
            },
            isDetailTab() {
                return this.params.dimension === 'detail';
            },
            dispensingRoleOptions() {
                const _arr = [
                    {
                        label: '发药业绩',
                        value: OperationTypeEnum.DISPENSING,
                    },
                    {
                        label: '审核业绩',
                        value: OperationTypeEnum.CHECK,
                    },
                    {
                        label: '调配业绩',
                        value: OperationTypeEnum.DEPLOY,
                    },
                ];
                if (this.featureProcess && this.showProcessStat) {
                    _arr.push({
                        label: '加工业绩',
                        value: OperationTypeEnum.PROCESS,
                    });
                }
                return _arr;
            },
        },
        created() {
            this.exportService = new ExportService();
            this.initSelection();
        },
        beforeDestroy() {
            this.exportService.destroy();
        },
        methods: {
            async handleMounted(data) {
                this.params.pageSize = (data.paginationLimit - 1) || 10;
                await this.getTableData();
            },
            handleSortChange() {},
            calcRoleEmployeeName(operationType) {
                this.roleEmployeeName = `${OperationTypeNameEnum[operationType]}人`;
            },
            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },
            resetParams() {
                this.params.prescriptionType = '';
                this.params.processType = '';
                this.params.processUsage = '';
            },
            handleEmployeeChange() {
                this.getTableData();
            },

            handleDispensingStatusChange() {
                this.getTableData();
            },
            handleProcessTypeChange() {
                this.getTableData();
            },
            handlePrescriptionTypeChange() {
                this.getTableData();
            },

            handleDateChange() {
                this.initSelection();
                this.getTableData();
            },

            async handleExport() {
                const params = this.getTableParams(true);
                if (this.isDetailTab) {
                    delete params.size;
                    delete params.offset;
                }
                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        ...params,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },
            handleDispensingRoleChange(id) {
                this.params.dimension = 'employee';
                this.calcRoleEmployeeName(id);
                this.params.operationType = id;
                this.params.employeeId = '';
                this.params.dispensingStatus = '';
                this.resetParams();
                this.initSelection();
                this.getTableData();
            },
            handleDimensionChange() {
                this.initSelection();
                this.resetParams();
                this.getTableData();
            },

            handleClinicChange() {
                this.initSelection();
                this.getTableData();
            },

            getTableParams(isExport = false) {
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    }, employeeId = '', dispensingStatus = '', processType = '', processUsage = '', operationType = '', prescriptionType = '', pageIndex, pageSize,
                } = this.params;

                const {
                    queryClinicId: clinicId = '', isEmployeeTab,
                } = this;
                const baseParams = {
                    beginDate,
                    endDate,
                    clinicId,
                    employeeId,
                    dispensingStatus,
                    processType: processType ? (isExport ? String(processType) : processType) : processType,
                    operationType,
                    processUsage,
                };

                if (isEmployeeTab) {
                    return baseParams;
                }
                return {
                    ...baseParams,
                    prescriptionType,
                    size: pageSize,
                    offset: pageSize * pageIndex,
                };
            },

            getSelectionParams() {
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                } = this.params;
                const { queryClinicId: clinicId } = this;
                return {
                    beginDate,
                    endDate,
                    clinicId,
                };
            },

            initSelection() {
                this.getDispensingStatusSelection();
                this.getOperationSelection();
                this.getPrescriptionTypeSelection();
                this.getProcessTypeSelection();
                this.getClinicSelection();
            },

            async getDispensingStatusSelection() {
                const params = this.getSelectionParams();
                try {
                    const { data } = await PharmacyAPI.getDispensingStatus({
                        ...params,
                    });
                    if (data) {
                        this.dispensingStatusOptions = data.map((item) => {
                            return {
                                ...item,
                                id: `${item.id}`,
                            };
                        });
                    }
                } catch (error) {
                    console.error(error);
                }
            },
            async getOperationSelection() {
                const params = this.getSelectionParams();
                const { operationType = 2 } = this.params;
                try {
                    const { data } = await PharmacyAPI.getOperationSelection({
                        ...params,
                        operationType,
                    });
                    this.operationPersonOptions = data;
                } catch (error) {
                    console.error(error);
                }
            },
            async getClinicSelection() {
                const params = this.getSelectionParams();
                const { operationType = 2 } = this.params;
                try {
                    const { data } = await PharmacyAPI.getClinic({
                        ...params,
                        operationType,
                    });
                    this.clinicOptions = data;
                } catch (error) {
                    console.error(error);
                }
            },
            async getPrescriptionTypeSelection() {
                const params = this.getSelectionParams();
                try {
                    const { data } = await PharmacyAPI.getPrescriptionType({
                        ...params,
                    });
                    this.prescriptionTypeOptions = data;
                } catch (error) {
                    console.error(error);
                }
            },
            async getProcessTypeSelection() {
                const params = this.getSelectionParams();
                try {
                    const { data } = await PharmacyAPI.getProcessType({
                        ...params,
                    });
                    this.processTypeOptions = data.processTypes;
                    this.processUsageOptions = data.processUsages?.map((item) => {
                        return {
                            name: item,
                            id: item,
                        };
                    });
                } catch (error) {
                    console.error(error);
                }
            },

            setTableData(isClear = false, tableData = {}) {
                if (isClear) {
                    this.tableHeader = [];
                    this.tableData = [];
                    this.summaryData = {};
                    this.count = 0;
                } else {
                    const {
                        header = [], data = [], total = {}, summary = { },
                    } = tableData || {};
                    this.tableHeader = header || [];
                    this.tableData = data || [];
                    this.summaryData = summary || {};
                    this.count = total?.count || 0;
                }
            },
            async getTableData(resetPageParams = true) {
                await this.$nextTick();

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }
                this.loading = true;

                const params = this.getTableParams();
                const { isEmployeeTab } = this;

                if (isEmployeeTab) {
                    try {
                        const { data } = await PharmacyAPI.getPersonnel({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data);
                        }
                    } catch (e) {
                        console.error(e);
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(true);
                        }
                    } finally {
                        this.loading = false;
                    }
                } else {
                    try {
                        const { data } = await PharmacyAPI.getDetail({
                            ...params,
                        });
                        this.setTableData(false, data);
                    } catch (e) {
                        console.error(e);
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(true);
                        }
                    } finally {
                        this.loading = false;
                    }
                }
                this.tableRenderHeader = this.createTableRenderHeader();
            },
            createTableRenderHeader() {
                if (this.params.dimension === 'employee') {
                    if (this.currentDispensingRole === OperationTypeEnum.DISPENSING) {
                        const config = resolveHeaderV2({
                            header: this.tableHeader,
                            staticConfig: dispensingEmployeeStaticConfig,
                            renderTypeList: this.renderTypeList,
                            descriptionMap: pharmacyDescriptionMap,
                        });
                        return {
                            hasHeaderBorder: true,
                            hasInnerBorder: true,
                            ...config,
                        };
                    }
                    if (this.currentDispensingRole === OperationTypeEnum.DEPLOY) {
                        const config = resolveHeaderV2({
                            header: this.tableHeader,
                            staticConfig: deployEmployeeStaticConfig,
                            renderTypeList: this.renderTypeList,
                            descriptionMap: pharmacyDescriptionMap,
                        });
                        return {
                            hasHeaderBorder: true,
                            hasInnerBorder: true,
                            ...config,
                        };
                    }
                    if (this.currentDispensingRole === OperationTypeEnum.PROCESS) {
                        const config = resolveHeaderV2({
                            header: this.tableHeader,
                            staticConfig: processEmployeeStaticConfig,
                            renderTypeList: this.renderTypeList,
                        });
                        return {
                            hasHeaderBorder: true,
                            hasInnerBorder: true,
                            ...config,
                        };
                    }
                }
                const list = resolveHeader(
                    this.tableHeader,
                    this.renderTypeList,
                    true,
                );
                list.forEach((item) => {
                    item.children.forEach((child) => {
                        child.autoSort = child.sortable;
                    });
                    item.autoSort = item.sortable;
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list,
                };
            },
        },
    };
</script>
