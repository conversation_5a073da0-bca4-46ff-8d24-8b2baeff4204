import { formatOralExamination2Html } from 'views/outpatient/common/medical-record/utils.js';
import { formatAge } from 'utils/index';
import { Popover as AbcPopover } from '@abc/ui-pc';
import {
    formatObstetricalHistory2Str,
} from 'src/views/outpatient/common/medical-record/utils';

export const outpatientLogRenderTypeList = {
    patientNameRender: (h, row) => {
        const value = row.patientName;
        let className = 'cell';
        if (row.isMember) {
            className += ' vip';
        }
        return (
            <abc-table-cell class={className} title={value}>
                {value}
            </abc-table-cell>
        );
    },
    oralExaminationsRender: (h, row) => {
        const value = row.oralExaminations;
        return (
            <abc-table-cell
                class="oral-examination"
            >
                <span class="ellipsis" domPropsInnerHTML={formatOralExamination2Html(value, true)}
                    title={formatOralExamination2Html(value, true)}></span>
            </abc-table-cell>
        );
    },

    prescriptionRender: (h, row) => {
        const { prescriptionSummary } = row;
        const className = 'table-cell';
        if (!prescriptionSummary) {
            return <div class={className}>-</div>;
        }
        return (
            <AbcPopover
                trigger="hover"
                placement="top-start"
                width="auto"
                theme="yellow"
                popperStyle={{ padding: '16px' }}
            >
                <abc-table-cell slot="reference" class="outpatient-stat__prescription-summary">{prescriptionSummary}</abc-table-cell>
                <div class="ms-list-popper">
                    {
                        row.prescriptionWesternForms &&
                        row.prescriptionWesternForms.map((EN, index) => {
                            let title = null;
                            if (
                                EN.prescriptionFormItems &&
                                EN.prescriptionFormItems.length > 0
                            ) {
                                title = (
                                    <div class="title">
                                        西药处方{index + 1}
                                    </div>
                                );
                            }

                            const content = (
                                <ul class="content">
                                    {EN.prescriptionFormItems &&
                                        EN.prescriptionFormItems.map(
                                            (it, index) => {
                                                return (
                                                    <li key={index}>
                                                        <span
                                                            class="ellipsis"
                                                            style="width: 200px;"
                                                        >
                                                            {it.name ?
                                                                it.name :
                                                                it.medicine_cadn}
                                                        </span>
                                                        <span
                                                            class="ellipsis"
                                                            style="width: 160px;"
                                                        >
                                                            {it.usage ?
                                                                `${it.usage}，` :
                                                                ''}
                                                            {it.freq ?
                                                                `${it.freq}，` :
                                                                ''}
                                                            {it.dosage +
                                                                it.dosage_unit}
                                                        </span>
                                                        <span>
                                                            {it.unit_count +
                                                                it.unit}
                                                        </span>
                                                    </li>
                                                );
                                            },
                                        )}
                                </ul>
                            );
                            return (
                                <div key={EN.id} class="prescription">
                                    {title}
                                    {content}
                                </div>
                            );
                        })
                    }
                    {
                        row.prescriptionInfusionForms &&
                        row.prescriptionInfusionForms.map((PN, index) => {
                            let title = null;
                            if (
                                PN.prescriptionFormItems &&
                                PN.prescriptionFormItems.length > 0
                            ) {
                                title = (
                                    <div class="title">
                                        输液处方{index + 1}
                                    </div>
                                );
                            }

                            const content = (
                                <ul class="content">
                                    {PN.prescriptionFormItems.map(
                                        (it, index) => {
                                            return (
                                                <li key={index}>
                                                    <span
                                                        class="ellipsis"
                                                        style="width: 200px;"
                                                    >
                                                        {it.name ?
                                                            it.name :
                                                            it.medicine_cadn}
                                                    </span>
                                                    <span
                                                        class="ellipsis"
                                                        style="width: 160px;"
                                                    >
                                                        {it.usage ?
                                                            `${it.usage}，` :
                                                            ''}
                                                        {it.freq ?
                                                            `${it.freq}，` :
                                                            ''}
                                                        {it.dosage +
                                                            it.dosage_unit}
                                                    </span>
                                                    <span>
                                                        {it.unit_count +
                                                            it.unit}
                                                    </span>
                                                </li>
                                            );
                                        },
                                    )}
                                </ul>
                            );
                            return (
                                <div key={PN.id} class="prescription">
                                    {title}
                                    {content}
                                </div>
                            );
                        })
                    }
                    {
                        row.prescriptionChineseForms &&
                        row.prescriptionChineseForms.map((CN, index) => {
                            let title = null;
                            if (
                                CN.prescriptionFormItems &&
                                CN.prescriptionFormItems.length > 0
                            ) {
                                title = (
                                    <div class="title">
                                        中药处方{index + 1}
                                    </div>
                                );
                            }

                            const content = (
                                <abc-flex wrap="wrap" class="content" style={{
                                    maxWidth: '604px',gap: '8px 32px',
                                }}>
                                    {
                                        CN.prescriptionFormItems.map(
                                            (it, idx) => {
                                                return (
                                                    <abc-flex key={idx} style={{ width: '180px' }} align="center" gap={8}>
                                                        <abc-text
                                                            theme="gray"
                                                            size="mini"
                                                            class="ellipsis"
                                                            style={{
                                                                flex: '0 1 auto', maxWidth: '100%',
                                                            }}
                                                            title={it.name || it.medicine_cadn}
                                                        >
                                                            {it.name ?
                                                                it.name :
                                                                it.medicine_cadn}
                                                        </abc-text>
                                                        <abc-text
                                                            theme="gray"
                                                            size="mini"
                                                            style={{ flexShrink: 0 }}
                                                        >
                                                            {(it.unit_count || '0') + (it.unit || 'g')}
                                                        </abc-text>
                                                        <abc-text
                                                            theme="gray"
                                                            size="mini"
                                                            style={{ flexShrink: 0 }}
                                                        >
                                                            { it.special_requirement ? `(${it.special_requirement})` : '' }
                                                        </abc-text>
                                                    </abc-flex>
                                                );
                                            },
                                        )
                                    }
                                </abc-flex>
                            );

                            let usage = null;
                            if (
                                CN.requirement ||
                                CN.freq ||
                                CN.dailyDosage ||
                                CN.usageLevel
                            ) {
                                usage = (
                                    <p
                                        class="layout__usage-tip"
                                        style="margin-top:8px;margin-bottom:4px;"
                                    >
                                        <strong>[用法用量]</strong>
                                        {CN.doseCount ?
                                            `共${CN.doseCount}剂，` :
                                            ''}
                                        {CN.requirement || ''}
                                        {CN.dailyDosage || ''}
                                        {CN.freq ? `，${CN.freq}` : ''}
                                        {CN.usageLevel ? `，${CN.usageLevel}` : ''}
                                    </p>
                                );
                            }

                            return (
                                <div key={CN.id} class="prescription">
                                    {title}
                                    {content}
                                    {usage}
                                </div>
                            );
                        })
                    }
                    {
                        row.prescriptionExternalTreatForms &&
                        row.prescriptionExternalTreatForms.map(
                            (external, index) => {
                                let title = null;
                                if (
                                    external.prescriptionFormItems &&
                                    external.prescriptionFormItems.length >
                                        0
                                ) {
                                    title = (
                                        <div class="title">
                                            外治处方{index + 1}
                                        </div>
                                    );
                                }

                                const content = (
                                    <ul
                                        class="content"
                                        style="white-space: normal;"
                                    >
                                        {external.prescriptionFormItems.map(
                                            (it, index,arr) => {
                                                const isLast = index === arr.length - 1;
                                                const style = `width: 100%; font-size: 12px; margin-right: ${
                                                    (index + 1) % 4 !== 0 ?
                                                        '9px' :
                                                        ''
                                                }`;
                                                const liStyle = `flex-direction: column;  border-bottom: ${
                                                    isLast ?
                                                        'none' :
                                                        '1px dashed #e6eaee'
                                                };margin-bottom:${isLast ? '0' : '4px'};padding-bottom:${isLast ? '0' : '4px'}`;
                                                //穴位
                                                let acupoints = '';
                                                it.acupointsList?.forEach((formItem) => {
                                                    acupoints += `[${formItem.position ?? '-'}]${formItem.name ?? '-'}；`;
                                                });
                                                //药品
                                                let externalGoodsItems = '';
                                                it.externalGoodsItemsList?.forEach((formItem) => {
                                                    externalGoodsItems += `${(formItem.name || formItem.medicineCadn) ?? '-'} ${formItem.unitCount}${formItem.unit}；`;
                                                });
                                                return (
                                                    <li
                                                        key={index}
                                                        style={liStyle}
                                                    >
                                                        <span
                                                            class="ellipsis"
                                                            style={style}
                                                        >
                                                            {it.name}&nbsp;&nbsp;
                                                            {it.unit_count}{it.unit}
                                                        </span>
                                                        <span
                                                            style="max-width:'400px';font-size:'12px'"
                                                        >
                                                            {externalGoodsItems}
                                                        </span>
                                                        <span
                                                            style="max-width:'400px';font-size:'12px'"
                                                        >
                                                            {acupoints}
                                                        </span>
                                                    </li>
                                                );
                                            },
                                        )}
                                    </ul>
                                );

                                return (
                                    <div key={external.id} class="prescription">
                                        {title}
                                        {content}
                                    </div>
                                );
                            },
                        )
                    }
                    {
                        row.prescriptionGlassesForms && row.prescriptionGlassesForms.glassesParams ? (
                            <div class="prescription">
                                <div class="title">配镜处方</div>
                                <div class={`glasses-form-wrapper ${row.prescriptionGlassesForms.glassesType ? 'is-contact' : ''}`} >
                                    <div class="glasses-form-wrapper-content">
                                        {
                                            row.prescriptionGlassesForms.glassesParams.map((item) => {
                                                return <div class={`glasses-form-item ${item.name === '基底' && (item.leftEyeValue || item.rightEyeValue) ? 'is-base' : ''}`}>
                                                    <span class={`${!row.prescriptionGlassesForms.glassesType ? '' : 'is-frame'}`} >{item.name}</span>
                                                    <span class={`${!row.prescriptionGlassesForms.glassesType ? '' : 'is-frame'}`} title={item.rightEyeValue}>{item.rightEyeValue}{item.rightEyeValue ? item.unit : ''}</span>
                                                    <span class={`${!row.prescriptionGlassesForms.glassesType ? '' : 'is-frame'}`} title={item.leftEyeValue}>{item.leftEyeValue}{item.leftEyeValue ? item.unit : ''}</span>
                                                </div>;
                                            })
                                        }
                                    </div>
                                    <div class="glasses-form-wrapper-footer">
                                        <span class="ellipsis"
                                            style={{
                                                maxWidth: `${row.prescriptionGlassesForms.glassesParams.length * 48 - 150}px`,
                                            }}>备注：{row.prescriptionGlassesForms.requirement}</span>
                                        <span class="ellipsis">验光师： {row.prescriptionGlassesForms.optometristName}</span>
                                    </div>
                                </div>
                            </div>
                        ) : null
                    }
                </div>
            </AbcPopover>
        );

    },
    ageRender: (h, row) => {
        const displayValue = formatAge(row.patientAge, {
            monthYear: 12, dayYear: 1,
        });
        return (
            <abc-table-cell> {displayValue || '-'} </abc-table-cell>
        );
    },
    obstetricalHistoryRender: (h, row) => {
        const displayValue = row.obstetricalHistory && row.obstetricalHistory !== '-' ? row.obstetricalHistory : '';
        const htmlText = formatObstetricalHistory2Str(displayValue);
        return displayValue && displayValue !== '-' ? (
            <abc-table-cell domPropsInnerHTML={htmlText}></abc-table-cell>
        ) : '-';
    },
};


export const outpatientLogStaticConfig = {
    'hasInnerBorder': true,
    'list': [{
        'label': '门诊日期',
        'key': 'created',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '148px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'center',
        },
    }, {
        'label': '诊号',
        'key': 'orderNo',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '患者姓名',
        'key': 'patientName',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '86px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '性别',
        'key': 'patientSex',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '48px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'center',
        },
    }, {
        'label': '年龄',
        'key': 'patientAge',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '90px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'center',
        },
    }, {
        'label': '证件号',
        'key': 'idNumber',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '160px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'center',
        },
    }, {
        'label': '挂号收费员',
        'key': 'registrationChargeBy',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '120px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '门诊收费员',
        'key': 'outpatientChargeBy',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '120px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '随访人',
        'key': 'revisitExecutor',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '84px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '联系电话',
        'key': 'patientMobile',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '120px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'center',
        },
    }, {
        'label': '住址',
        'key': 'address',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '职业',
        'key': 'profession',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '体重',
        'key': 'patientWeight',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'center',
        },
    }, {
        'label': '家长名',
        'key': 'parentName',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '86px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '工作单位',
        'key': 'company',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '137px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '初诊/复诊',
        'key': 'diagnoseStatus',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'center',
        },
    }, {
        'label': '患者来源',
        'key': 'patientSource',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '医生',
        'key': 'doctorName',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '86px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '医生科室',
        'key': 'departmentName',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '140px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '医助',
        'key': 'medicalAssistance',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '86px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '主诉',
        'key': 'chiefComplaint',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '个人史',
        'key': 'personalHistory',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '现病史',
        'key': 'presentHistory',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '既往史',
        'key': 'pastHistory',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '月经婚育史',
        'key': 'obstetricalHistory',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '流行病史',
        'key': 'epidemiologicalHistory',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '体格检查(主要症状与体征)',
        'key': 'physicalExamination',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '望闻切诊',
        'key': 'chineseExamination',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '口腔检查',
        'key': 'oralExaminations',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '辅助检查',
        'key': 'auxiliaryExaminations',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '辨证论治',
        'key': 'syndromeTreatment',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '诊断',
        'key': 'diagnosis',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '辨证',
        'key': 'syndrome',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '治法',
        'key': 'therapy',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '诊疗项目',
        'key': 'outpatientProductItem',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '处方',
        'key': 'prescription',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '医嘱事项',
        'key': 'doctorAdvice',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '费用',
        'key': 'fee',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'right',
        },
    }, {
        'label': '费别',
        'key': 'shebaoChargeType',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '就诊推荐',
        'key': 'visitSourceName',
        'description': '用于患者每次到店原因',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '处置',
        'key': 'disposals',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '200px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '就诊备注',
        'key': 'visitSourceRemark',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '体温',
        'key': 'temperature',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '血压',
        'key': 'bloodPressure',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '血糖',
        'key': 'bloodSugar',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }, {
        'label': '发病日期',
        'key': 'symptomTime',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'center',
        },
    }, {
        'label': '患者备注',
        'key': 'patientRemark',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '100px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    },{
        'label': '家族史',
        'key': 'familyHistory',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '140px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    },{
        'label': '过敏史',
        'key': 'allergicHistory',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '140px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    },{
        'label': '出生史',
        'key': 'birthHistory',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '140px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    },{
        'label': '方药',
        'key': 'chinesePrescription',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '140px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    },{
        'label': '目标',
        'key': 'target',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '140px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    },{
        'label': '预后',
        'key': 'prognosis',
        'sortable': false,
        'style': {
            'flex': 1,
            'width': '',
            'maxWidth': '',
            'minWidth': '140px',
            'paddingLeft': '',
            'paddingRight': '',
            'textAlign': 'left',
        },
    }],
};

export const outpatientLogRenderTypeMap = {
    'oralExaminations': 'oralExaminationsRender',
    prescription: 'prescriptionRender',
    patientAge: 'ageRender',
    obstetricalHistory: 'obstetricalHistoryRender',
};
