<template>
    <div class="stat__clinic-filter-item">
        <clinic-select
            v-model="clinicIdFilter"
            :width="width"
            :is-custom-clinic="false"
            @change="handleFilterSelectChange"
        >
        </clinic-select>
    </div>
</template>
<script>
    import ClinicSelect from 'views/statistics/common/clinic-select/clinic-select';
    export default {
        name: 'ClinicFilterItem',
        components: {
            ClinicSelect,
        },
        props: {
            placeholder: {
                type: String,
                default: '选择门店',
            },
            value: {
                type: String,
                required: true,
            },
            width: {
                type: [Number, String],
                default: 90,
            },
            filterEventName: {
                type: String,
                required: true,
            },
        },
        data() {
            return {
                clinicIdFilter: this.value,
            };
        },
        methods: {
            handleFilterSelectChange(val) {
                this.$emit(this.filterEventName, val);
            },
        },
    };
</script>
