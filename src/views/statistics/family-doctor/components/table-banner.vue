<template>
    <abc-card class="stat-table_key-data" radius-size="small">
        <abc-flex
            justify="space-between"
        >
            <div
                v-for="(item, index) in list"
                :key="index"
                class="sign-info-item"
                :item="item"
            >
                <span class="sign-info-item_count">
                    {{ item.value }}
                </span>
                <span class="sign-info-item_text">
                    <description-tips
                        v-if="item.tips"
                        color="#b6d7ff"
                        placement="top-start"
                        icon-position="right"
                        :width="item.width"
                    >
                        <span
                            slot="text"
                            class="label"
                            style="font-size: 14px; color: #0090ff;"
                        >{{ item.text }}</span>
                        <span>{{ item.tips }}</span>
                    </description-tips>
                    <span v-else>{{ item.text }}</span>
                </span>
            </div>
        </abc-flex>
    </abc-card>
</template>

<script>
    import DescriptionTips from '../../common/description-tips/description-tips.vue';
    export default {
        components: {
            DescriptionTips,
        },
        props: {
            list: {
                type: Array,
                default: () => [],
            },
        },

    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.stat-table_key-data {
    display: flex;
    height: 62px;
    padding: 16px;
    margin-bottom: 16px;
    background-color: #e8f3ff;
    border-radius: var(--abc-border-radius-small);
}

.sign-info-item {
    margin-right: 35px;
    color: $theme2;

    .sign-info-item_count {
        font-size: 24px;
        font-weight: 500;
    }

    .sign-info-item_text {
        font-size: 14px;
    }
}
</style>
