<template>
    <app-cont-card :use-radio="useRadio" sup-name="@stat/promotion">
        <router-view></router-view>
    </app-cont-card>
</template>

<script type="text/ecmascript-6">
    import Layout from 'views/common/layout';
    import clinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import { formatDate } from '@abc/utils-date';
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import AppContCard from '@/views/statistics/common/app-cont-card.vue';

    export default {
        name: 'Index',
        components: {
            AppContCard,
        },
        mixins: [ Layout, clinicTypeJudger ],

        computed: {
            ...mapGetters([ 'statisticsDateParams' ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            useRadio() {
                return this.viewDistributeConfig.Inventory.useRadio;
            },
        },
        created() {
            this.$store.dispatch('fetchChainSubClinics');
            this.$store.dispatch('treatment/fetchExecuteEnableCross');
            this.init();
        },
        methods: {
            ...mapActions([ 'setStatDateParams' ]),
            init() {
                const date = formatDate(new Date());

                const dateParams = {
                    begin: date,
                    end: date,
                    dateRange: [ date, date ],
                };

                this.setStatDateParams(dateParams);
            },
        },
    };
</script>
