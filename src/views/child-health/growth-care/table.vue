<template>
    <abc-flex
        :key="outpatientSheetId"
        v-abc-loading="loading"
        vertical
        gap="large"
    >
        <growth-record
            :post-data="postData"
            :disabled="disabled"
            :outpatient-sheet-id="outpatientSheetId"
            :table-data="bodyGrowthRecords"
            :height-and-weight-views="heightAndWeightViews"
            :head-size-and-age-views="headSizeAndAgeViews"
            :head-size-and-height-views="headSizeAndHeightViews"
            :height-and-age-views="heightAndAgeViews"
            :weight-and-age-views="weightAndAgeViews"
            :bmi-views="bmiViews"
            @update="updateHandler"
        ></growth-record>
        <vaccination-record
            :post-data="postData"
            :disabled="disabled"
            :table-data="inoculationRecords"
            @update="updateHandler"
        ></vaccination-record>
        <diet-record
            :post-data="postData"
            :disabled="disabled"
            :table-data="dietRecords"
            @update="updateHandler"
        ></diet-record>
        <sleep-record
            :post-data="postData"
            :disabled="disabled"
            :table-data="sleepRecords"
            @update="updateHandler"
        ></sleep-record>
        <oral-record
            :post-data="postData"
            :disabled="disabled"
            :table-data="mouthRecords"
            @update="updateHandler"
        ></oral-record>
    </abc-flex>
</template>

<script>
    import GrowthRecord from './growth-record/record';
    import VaccinationRecord from './vaccination-record/record';
    import DietRecord from './diet-record/record';
    import SleepRecord from './sleep-record/record';
    import OralRecord from './oral-record/record';

    import ChildHeathAPI from 'api/child-health/child-care';
    import { debounce } from '../../../utils/lodash';

    export default {
        name: 'GrowthCare',
        components: {
            GrowthRecord,
            VaccinationRecord,
            DietRecord,
            SleepRecord,
            OralRecord,
        },
        props: {
            outpatientInfo: {
                type: Object,
                required: false,
                default: () => {},
            },
            patient: {
                type: Object,
                required: true,
            },
            outpatientSheetId: {
                type: String,
                required: false,
                default: '',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            currentQLPatient: {
                type: Object,
                default: null,
            },
        },
        data() {
            return {
                tableData: {},
                loading: false,
                dietRecords: [], // 饮食记录列表
                sleepRecords: [], // 睡眠记录列表
                mouthRecords: [], // 口腔记录列表
                inoculationRecords: [], // 接种记录列表
                bodyGrowthRecords: [], // 生长记录列表

                headSizeAndAgeViews: [], // 头围年龄
                heightAndWeightViews: [], // 身高体重分布
                headSizeAndHeightViews: [], // 头围体重
                heightAndAgeViews: [], // 身长年龄
                weightAndAgeViews: [], // 体重年龄

                bmiViews: [], // bmi 年龄
            };
        },
        computed: {
            postData() {
                return {
                    id: this.outpatientSheetId,
                    patient: this.patient,
                    ...this.outpatientInfo,
                };
            },
        },
        watch: {
            currentQLPatient: {
                handler (obj) {
                    if (obj) {
                        const {
                            id, age = {
                                year: '', month: '', day: '',
                            },
                        } = this.patient || {};
                        if (obj.id !== id || obj.age !== age.day || obj.month !== age.month || obj.year !== age.year) {
                            this._fetchGrowthTable();
                        }
                    }
                },
                deep: true,
            },
        },
        async created() {
            this._fetchGrowthTable = debounce(this.fetchGrowthTable, 100, true);
            this.fetchGrowthTable(true);
        },
        methods: {
            /**
             * @desc
             * <AUTHOR> Yang
             * @date 2020-06-19 09:19:45
             * @params isCreated 第一次拉取直接读取 QL 上的patient
             * @return
             */
            async fetchGrowthTable() {
                this.loading = true;
                this.bodyGrowthRecords = [];
                this.sleepRecords = [];
                this.mouthRecords = [];
                this.inoculationRecords = [];
                this.dietRecords = [];
                try {
                    let params = null;
                    if (this.currentQLPatient) {
                        params = {
                            patientId: this.currentQLPatient.id,
                            ageDay: (this.currentQLPatient.age && this.currentQLPatient.age.day) || '',
                            ageMonth: (this.currentQLPatient.age && this.currentQLPatient.age.month) || '',
                            ageYear: (this.currentQLPatient.age && this.currentQLPatient.age.year) || '',
                            patientSex: this.currentQLPatient.sex,
                        };
                    } else {
                        params = {
                            patientId: this.patient.id,
                            ageDay: (this.patient.age && this.patient.age.day) || '',
                            ageMonth: (this.patient.age && this.patient.age.month) || '',
                            ageYear: (this.patient.age && this.patient.age.year) || '',
                            patientSex: this.patient.sex,
                        };
                    }
                    if (this.outpatientSheetId) {
                        params.outpatientSheetId = this.outpatientSheetId;
                    }

                    const { data } = await ChildHeathAPI.fetchGrowthTable(params);
                    this.bodyGrowthRecords = data.bodyGrowthRecords || [];
                    this.sleepRecords = data.sleepRecords || [];
                    this.mouthRecords = data.mouthRecords || [];
                    this.inoculationRecords = data.inoculationRecords || [];
                    this.dietRecords = data.dietRecords || [];

                    this.heightAndAgeViews = data.heightAndAgeViews || [];
                    this.weightAndAgeViews = data.weightAndAgeViews || [];

                    this.heightAndWeightViews = data.heightAndWeightViews || [];
                    this.headSizeAndAgeViews = data.headSizeAndAgeViews || [];

                    this.headSizeAndHeightViews = data.headSizeAndHeightViews || [];
                    this.bmiViews = data.bmiViews || [];
                    this.loading = false;
                } catch (e) {
                    console.warn(e);
                    this.loading = false;
                }
            },
            updateHandler() {
                this.fetchGrowthTable();
                this.$emit('update-child-health');
            },
        },
    };
</script>
