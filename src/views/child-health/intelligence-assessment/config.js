export const INTELLIGENCE_ASSESSMENT_TABLE_CONFIG = Object.freeze([
    {
        label: '名称',
        prop: 'formName',
        width: 300,
    },
    {
        label: '时间',
        prop: 'created',
        width: 180,
    },
    {
        label: '年龄',
        prop: 'age',
        width: 180,
    },
    {
        label: '分数/总分',
        prop: 'recordItemViews',
        width: 180,
    },
    // {
    //     label: '测试结果',
    //     prop: 'result',
    //     width: 440,
    // },
    {
        label: '操作',
        prop: 'operator',
        width: 120,
    },
]);

export const TABLE_QUESTION_DATA = Object.freeze([
    {
        _id: '5e9cf57deae5147c68a95c71',
        key: 'dayundong',
        kind: 'GroupComponent',
        label: '大运动',
        children: [
            {
                _id: '5e9cf57deae5147c68a95c72',
                key: 'qisanlun',
                kind: 'Radio',
                label: '能骑三轮脚踏车',
                tips: {
                    imageUrl: '/img/xxxx/sss.jpg',
                    title: '单臂环绕',
                    content: 'ssssxxxssss',
                },
                options: [
                    {
                        name: '经常做到',
                        value: 5,
                    },
                    {
                        name: '偶尔做到',
                        value: 3,
                    },
                    {
                        name: '很少做到',
                        value: 1,
                    },
                ],
            },
            {
                _id: '5e9cf57deae5147c68a95c73',
                key: 'qisanlun',
                kind: 'Checkbox',
                label: '双足并跳',
                tips: {
                    imageUrl: '...',
                    title: '...',
                    content: '...',
                },
                options: [
                    {
                        name: '经常做到',
                        value: 5,
                    },
                    {
                        name: '偶尔做到',
                        value: 3,
                    },
                    {
                        name: '很少做到',
                        value: 1,
                    },
                ],
            },
        ],
        resultRules: [
            {
                scoreRang: {
                    begin: 0,
                    end: 16,
                },
                result: '大运动品测异常，需要进一步评估',
                suggest: '多与父母进行室外活动，激励孩子进行站立,...',
            },
            {
                scoreRang: {
                    begin: 17,
                    end: 30,
                },
                result: '...',
                suggest: '...,...',
            },
        ],
    },
    {
        _id: '5e9cf57deae5147c68a95c71',
        key: 'dayundong',
        kind: 'GroupComponent',
        label: '大运动',
        children: [
            {
                _id: '5e9cf57deae5147c68a95c72',
                key: 'qisanlun',
                kind: 'Radio',
                label: '能骑三轮脚踏车',
                tips: {
                    imageUrl: '/img/xxxx/sss.jpg',
                    title: '单臂环绕',
                    content: 'ssssxxxssss',
                },
                options: [
                    {
                        name: '经常做到',
                        value: 5,
                    },
                    {
                        name: '偶尔做到',
                        value: 3,
                    },
                    {
                        name: '很少做到',
                        value: 1,
                    },
                ],
            },
            {
                _id: '5e9cf57deae5147c68a95c73',
                key: 'qisanlun',
                kind: 'Checkbox',
                label: '双足并跳',
                tips: {
                    imageUrl: '...',
                    title: '...',
                    content: '...',
                },
                options: [
                    {
                        name: '经常做到',
                        value: 5,
                    },
                    {
                        name: '偶尔做到',
                        value: 3,
                    },
                    {
                        name: '很少做到',
                        value: 1,
                    },
                ],
            },
        ],
        resultRules: [
            {
                scoreRang: {
                    begin: 0,
                    end: 16,
                },
                result: '大运动品测异常，需要进一步评估',
                suggest: '多与父母进行室外活动，激励孩子进行站立,...',
            },
            {
                scoreRang: {
                    begin: 17,
                    end: 30,
                },
                result: '...',
                suggest: '...,...',
            },
        ],
    },
]);

export const IMAGE_RADIO_QUESTION_DATA = Object.freeze([
    {
        _id: '5e9cf57deae5147c68a95c72',
        key: 'qisanlun',
        kind: 'ImageRadio',
        label: '能骑三轮脚踏车',
        options: [
            {
                name: '经常做到',
                value: 5,
                imgUrl: 'xxxx',
            },
            {
                name: '偶尔做到',
                value: 3,
                imgUrl: 'xxxx',
            },
            {
                name: '很少做到',
                value: 1,
                imgUrl: 'xxxx',
            },
        ],
    },
    {
        _id: '5e9cf57deae5147c68a95c73',
        key: 'qisanlun',
        kind: 'ImageRadio',
        label: '能骑三轮脚踏车',
        options: [
            {
                name: '经常做到',
                value: 5,
                imgUrl: 'xxxx',
            },
            {
                name: '偶尔做到',
                value: 3,
                imgUrl: 'xxxx',
            },
            {
                name: '很少做到',
                value: 1,
                imgUrl: 'xxxx',
            },
        ],
    },
]);
