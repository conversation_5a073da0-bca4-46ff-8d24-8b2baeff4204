<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        content-styles="width: 844px;"
        :title="title"
        @close="$emit('close')"
    >
        <question-result :data="data"></question-result>
        <div slot="footer" class="dialog-footer">
            <abc-button @click="submit">完成</abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import QuestionResult from '../../components/question-result';
    export default {
        name: 'QuestionResultDialog',
        components: {
            QuestionResult,
        },
        props: {
            data: {
                type: Object,
            },
            value: {
                type: Boolean,
                required: true,
            },
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            // 使用第一条结果问卷名作为dialog的title
            title() {
                const formAbstract = this.data.formAbstract;
                return ((formAbstract && formAbstract.name) || '') + '评测结果';
            },
        },
        methods: {
            submit() {
                this.$emit('submit');
            },
        },
    };
</script>
