/**
 * 读卡器驱动安装包目录规范
 * 整包为zip包
 * 参考
 * https://cis-static-common.oss-cn-shanghai.aliyuncs.com/id-card-reader/install/HuaShi.zip
 * https://cis-static-common.oss-cn-shanghai.aliyuncs.com/id-card-reader/install/XinZhongXin.zip
 *
 * ** x64          --64位目录(安装包相关的依赖可以放到当前目录)
 * **** setup.exe  --安装包可以是exe或者msi
 * **** 其他
 * ** x86          --32位目录(安装包相关的依赖可以放到当前目录)
 * **** setup.exe  --安装包可以是exe或者msi
 * **** 其他
 */
import {
    exec,
} from '@/printer/utils/electron.js';
import { sleep } from 'utils/delay';

export const ReaderInstallProcessEnum = {
    DOWNLOAD_START: 1,
    DOWNLOADING: 2,
    DOWNLOADED: 3,
    INSTALL_START: 4,
    ERROR: 5,
};

export default class IdCardReaderInstall {
    // 下载地址
    url = null;
    // 需要打开的安装包名称 ['setup.msi']
    setupNames = null;
    md5 = null;
    onProcess = null;

    constructor(options) {
        const {
            url,
            md5,
            onProcess = () => {},
            setupNames,
        } = options;
        this.url = url;
        this.md5 = md5;
        this.setupNames = setupNames;
        this.onProcess = onProcess;
        console.log('下载url', this.url);
    }

    async start() {
        const { electron } = window;
        if (electron?.downloadManager?.downloadFile) {
            const path = window.require('path');
            /**
             * 假设url https://cis-static-common.oss-cn-shanghai.aliyuncs.com/id-card-reader/install/HuaShi.zip
             * extName: .zip
             * baseName: HuaShi
             */
            const extName = path.extname(this.url);
            const baseName = path.basename(this.url, extName);
            // 加上时间戳是为了避免重名
            const fileName = `${baseName}${extName}`;
            const filepath = path.resolve(window.process.env.HOMEPATH, './Downloads/', fileName);
            const downloadPath = path.resolve(window.process.env.HOMEPATH, './Downloads/');
            this.onProcess(ReaderInstallProcessEnum.DOWNLOAD_START);
            await sleep(100);
            electron.downloadManager.downloadFile({
                url: this.url,
                file: filepath,
                md5: this.md5,
                onProcess: (currentProcess) => {
                    this.onProcess(ReaderInstallProcessEnum.DOWNLOADING, currentProcess);
                },
                onComplete: async () => {
                    try {
                        this.onProcess(ReaderInstallProcessEnum.DOWNLOADED);
                        await sleep(100);
                        // 解压
                        const AdmZip = window.require('adm-zip');
                        const zip = new AdmZip(filepath);
                        zip.extractAllTo(downloadPath, true);

                        // 判断操作系统版本
                        const arch = (window.process.arch === 'x64' || window.process.env.hasOwnProperty('PROCESSOR_ARCHITEW6432')) ? 'x64' : 'x86';

                        // 安装
                        this.onProcess(ReaderInstallProcessEnum.INSTALL_START);
                        await sleep(100);

                        // 打开对应的setup文件
                        for (let i = 0 ; i < this.setupNames.length; i++) {
                            const setupPath = path.resolve(downloadPath, baseName, arch, this.setupNames[i]);
                            console.debug(setupPath);
                            await exec(setupPath);
                        }
                    } catch (e) {
                        console.error(e);
                        this.onProcess(ReaderInstallProcessEnum.ERROR, e);
                    }
                },
            });
        } else {
            // location.href之后不会再执行了
            // 直接设置为下载完成
            this.onProcess(ReaderInstallProcessEnum.DOWNLOADED);
            location.href = this.url;
        }
    }
}
