.doctor-tags-wrapper {
    position: relative;
    display: inline-flex;
    outline: none;

    .abc-popover__reference {
        outline: none;
    }

    .add-tag-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 22px;
        cursor: pointer;
        border: 1px solid #8d9aa8;
        border-radius: 12px;
        outline: none;

        .iconfont {
            color: $T1;
        }
    }
}

.doctor-tags-popover {
    .select-tag-popover {
        position: relative;
        min-height: 154px;
        max-height: 368px;
        padding: 16px 8px;
        overflow-y: auto;
        overflow-y: overlay;

        > .cis-icon-set {
            position: absolute;
            top: 10px;
            right: 10px;
            color: $T2;
            cursor: pointer;

            &:hover {
                color: $B2;
            }
        }

        .tag-item {
            position: relative;
            display: inline-flex;
            height: 22px;
            padding: 0 8px;
            margin: 4px;
            color: $T2;
            cursor: pointer;
            background-color: $P5;
            border: 1px solid #e6eaed;
            border-radius: 12px;

            &:hover {
                color: $T1;
            }
        }

        .is-selected {
            color: $S2;
            background-color: $G2;
            border-color: $G2;

            &:hover {
                color: $S2;
            }
        }
    }
}

.tags-manager-dialog-content {
    .tags-list {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }

    .tag {
        position: relative;
        display: inline-flex;
        align-items: center;
        height: 22px;
        padding: 0 8px;
        margin: 4px;
        color: $T2;
        cursor: pointer;
        background-color: #ffffff;
        border: 1px solid #e6eaed;
        border-radius: 12px;

        &.is-disabled {
            color: $T3;
            cursor: not-allowed;
            background-color: $P5;
        }
    }

    .add-tag {
        position: relative;
        margin: 10px 4px 4px;
        border-radius: 12px;
    }
}

.tag-editor-wrapper {
    position: absolute;
    bottom: -36px;
    left: 0;
    display: flex;
    align-items: center;
    min-width: 212px;

    .abc-input-wrapper input {
        height: 34px;
        background-color: #ffffff;
        border-radius: 4px 0 0 4px;
    }

    .abc-button-group {
        display: flex;
        align-items: center;
        border: 1px solid $P1;
        border-left: 0;
        border-radius: 0 var(--abc-border-radius-small) var(--abc-border-radius-small) 0;

        > button {
            width: 36px;
            min-width: 36px;
            padding: 0;
            margin: 0;
            font-size: 12px;
            border: 0;
            border-radius: 0;
        }

        .abc-button.is-disabled:hover {
            border: 0;
        }
    }
}
