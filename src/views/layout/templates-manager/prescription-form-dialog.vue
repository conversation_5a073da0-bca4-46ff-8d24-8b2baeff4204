<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="postData.id ? '编辑模板' : '创建模板'"
        custom-class="template-form-dialog"
        append-to-body
        content-styles="width: 816px; height: 540px; padding: 24px 38px;"
    >
        <abc-form ref="addForm" label-position="left" :label-width="98">
            <abc-form-item-group label-position="inner" is-excel style="margin-bottom: 16px;">
                <abc-form-item required label="模板名称">
                    <abc-input
                        v-model="currentName"
                        style="height: 32px;"
                        class="template-name"
                        trim
                        max-length="30"
                        show-max-length-tips
                    ></abc-input>
                </abc-form-item>
            </abc-form-item-group>

            <abc-form ref="form" is-excel item-no-margin>
                <abc-descriptions
                    size="large"
                    background
                    :column="2"
                    :label-width="98"
                    grid
                >
                    <abc-descriptions-item label="分类" content-padding="0">
                        <abc-form-item>
                            <abc-edit-div
                                v-model="postData.classification"
                                class="classification"
                                disabled
                                spellcheck="false"
                            ></abc-edit-div>
                        </abc-form-item>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="来源" content-padding="0">
                        <abc-form-item class="tcm-syndrome">
                            <abc-edit-div
                                v-model="postData.source"
                                style="border-radius: 0 var(--abc-border-radius-small) 0 0;"
                                spellcheck="false"
                            ></abc-edit-div>
                        </abc-form-item>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="功用" :span="2" content-padding="0">
                        <abc-form-item>
                            <abc-edit-div v-model="postData.effect" style="border-radius: 0;" spellcheck="false"></abc-edit-div>
                        </abc-form-item>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="主治" :span="2" content-padding="0">
                        <abc-form-item>
                            <abc-edit-div v-model="postData.majorDisease" spellcheck="false"></abc-edit-div>
                        </abc-form-item>
                    </abc-descriptions-item>

                    <abc-descriptions-item
                        label="加减"
                        :span="2"
                        content-padding="0"
                    >
                        <abc-form-item>
                            <abc-edit-div
                                v-model="postData.revision"
                                style="border-radius: 0 0 var(--abc-border-radius-small) 0;"
                                :maxlength="1000"
                                spellcheck="false"
                            ></abc-edit-div>
                        </abc-form-item>
                    </abc-descriptions-item>
                </abc-descriptions>
            </abc-form>

            <prescription-group
                :post-data="postData"
                :need-check-stock="false"
                is-from-template
                :switch-setting="{
                    prescriptionTemplateSwitch: 0,
                    ...prescriptionKeys,
                }"
            ></prescription-group>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <abc-button
                :loading="buttonLoading"
                :class="{ 'abc-tipsy abc-tipsy--n': disabledSaveBtn }"
                data-tipsy="没有处方项目"
                :disabled="disabledSaveBtn"
                @click="save('addForm')"
            >
                确认
            </abc-button>
            <abc-button variant="ghost" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import OutpatientAPI from 'api/outpatient';
    import SettingsAPI from 'api/settings';

    import Clone from 'utils/clone';
    import prescriptionHandle from '../../outpatient/mixins/prescription-handle';
    import { OwnerType } from 'views/layout/templates-manager/constants';

    export default {
        name: 'PRTemplateFormDialog',
        components: {
            // template manager 在 prescription-group 调用，此处会循环，所以使用 webpack 的异步 import：
            PrescriptionGroup: () => import('../../outpatient/prescription-group/prescription-group'),
        },
        mixins: [prescriptionHandle],
        props: {
            value: Boolean,
            categoryId: {
                required: true,
                validator: (prop) => typeof prop === 'string' || typeof prop === 'number' || prop === null,
            },
            category: {
                type: Number,
                required: true,
            },
            catalogues: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            data: {
                required: true,
                validator: (prop) => typeof prop === 'object' || prop === null,
            },
        },

        data() {
            return {
                buttonLoading: false,
                postData: {
                    id: null,
                    name: '',
                    category: this.category,
                    parentId: this.categoryId,

                    classification: '',
                    source: '',
                    majorDisease: '',
                    effect: '',
                    revision: '',
                    prescriptionChineseForms: [],
                    prescriptionWesternForms: [],
                    prescriptionInfusionForms: [],
                    prescriptionExternalForms: [],
                    prescriptionGlassesForms: [],
                },
            };
        },

        computed: {
            ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig']),
            // 模板里面不看开关都要有，但是需要这个key存在，比如眼科就没有中药处方,眼科配镜不需要模板
            prescriptionKeys() {
                const config = this.outpatientEmployeeConfig;
                const { prescription } = config;
                const allKeys = {};
                for (const key in prescription) {
                    if (
                        key !== 'defaultOpenPrescription' &&
                        key !== 'glassesSwitch' &&
                        prescription.hasOwnProperty(key)
                    ) {
                        allKeys[key] = 1;
                    }
                }
                return allKeys;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            currentName: {
                get() {
                    return this.name || this.postData.name;
                },
                set(val) {
                    this.postData.name = val;
                },
            },

            disabledSaveBtn() {
                let length = 0;
                this.postData.prescriptionChineseForms.forEach((pr) => {
                    length += pr.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
                });
                this.postData.prescriptionWesternForms.forEach((pr) => {
                    length += pr.prescriptionFormItems.length;
                });
                this.postData.prescriptionInfusionForms.forEach((pr) => {
                    length += pr.prescriptionFormItems.length;
                });
                this.postData.prescriptionExternalForms.forEach((pr) => {
                    length += pr.prescriptionFormItems.filter((item) => item.goodsId).length;
                });
                return length === 0;
            },
        },

        created() {
            if (this.data) {
                const {
                    id, name, category, parentId, classification, source,majorDisease, effect, revision, detail, ownerType,
                } = this.data;

                if (detail.prescriptionChineseForms) {
                    detail.prescriptionChineseForms = detail.prescriptionChineseForms.map((form) => {
                        form.usage = form.usage || '';
                        form.dailyDosage = form.dailyDosage || '';
                        form.freq = form.freq || '';
                        form.usageLevel = form.usageLevel || '';
                        form.usageDays = form.usageDays || '';
                        form.requirement = form.requirement || '';
                        form.specialRequirement = form.specialRequirement || '';
                        return form;
                    });
                }

                this.postData = {
                    id,
                    name,
                    category,
                    parentId,
                    classification,
                    source,
                    majorDisease,
                    effect,
                    revision,
                    ownerType,
                    prescriptionChineseForms: Clone(detail.prescriptionChineseForms || []),
                    prescriptionWesternForms: Clone(detail.prescriptionWesternForms || []),
                    prescriptionInfusionForms: Clone(detail.prescriptionInfusionForms || []),
                    prescriptionExternalForms: Clone(detail.prescriptionExternalForms || []),
                };
                this.initProductInfo();
            }
            if (!this.categoryId && this.catalogues.length) {
                this.postData.parentId = this.catalogues[0].id;
            }
        },

        methods: {
            async initProductInfo() {
                const goodsIdsArray = [];
                const {
                    prescriptionWesternForms,
                    prescriptionInfusionForms,
                    prescriptionExternalForms,
                } = this.postData;
                if (prescriptionWesternForms.length) {
                    prescriptionWesternForms.forEach((form) => {
                        form.prescriptionFormItems.forEach((item) => {
                            if (!item.productInfo) {
                                this.$set(item, 'productInfo', null);
                            }
                            if (item.goodsId && goodsIdsArray.indexOf(item.goodsId) === -1) {
                                goodsIdsArray.push(item.goodsId);
                            }
                        });
                    });
                }
                if (prescriptionInfusionForms.length) {
                    prescriptionInfusionForms.forEach((form) => {
                        form.prescriptionFormItems.forEach((item) => {
                            if (!item.productInfo) {
                                this.$set(item, 'productInfo', null);
                            }
                            if (item.goodsId && goodsIdsArray.indexOf(item.goodsId) === -1) {
                                goodsIdsArray.push(item.goodsId);
                            }
                        });
                    });
                }
                prescriptionExternalForms.forEach((form) => {
                    form.prescriptionFormItems.forEach((item) => {
                        if (!item.acupoints.find((it) => !it.id && !it.name)) {
                            item.acupoints.push({
                                id: null,
                                name: '',
                                type: 0,
                                position: '单',
                            });
                        }
                    });
                });
                // 请求 处方模板中的库存  获取药品的库存信息
                // 查询输液和西药库存
                if (goodsIdsArray.length) {
                    // 得到 goodsId 请求相应的 goods 库存
                    const submitData = {
                        goodsIds: goodsIdsArray,
                    };
                    const { data } = await SettingsAPI.commonPrescription.fetchPrescriptionTemplateStock(submitData);
                    const stockGoodsArray = (data && data.list) || [];
                    // 使用新库存 覆盖 处方模板中的库存
                    this.updatePrescriptionForm(prescriptionWesternForms, stockGoodsArray);
                    this.updatePrescriptionForm(prescriptionInfusionForms, stockGoodsArray);
                }
            },

            save(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.postData.id ? this.updateSubmit() : this.createSubmit();
                    }
                });
            },

            formatPostData() {
                let prescriptionChineseForms = Clone(
                    this.postData.prescriptionChineseForms.filter((pr) => {
                        pr.prescriptionFormItems = pr.prescriptionFormItems.filter((item) => item.goodsId || item.name);
                        return pr.prescriptionFormItems.length;
                    }),
                );
                let prescriptionWesternForms = Clone(
                    this.postData.prescriptionWesternForms.filter((pr) => {
                        return pr.prescriptionFormItems && pr.prescriptionFormItems.length;
                    }),
                );
                let prescriptionInfusionForms = Clone(
                    this.postData.prescriptionInfusionForms.filter((pr) => {
                        return pr.prescriptionFormItems && pr.prescriptionFormItems.length;
                    }),
                );
                let prescriptionExternalForms = Clone(
                    this.postData.prescriptionExternalForms.filter((pr) => {
                        pr.prescriptionFormItems.forEach((item) => {
                            item.acupoints = item.acupoints.filter((it) => it.id || it.name);
                        });
                        return pr.prescriptionFormItems && pr.prescriptionFormItems.length;
                    }),
                );
                prescriptionChineseForms = this.getPrescriptionForm(prescriptionChineseForms);
                prescriptionWesternForms = this.getPrescriptionForm(prescriptionWesternForms);
                prescriptionInfusionForms = this.getPrescriptionForm(prescriptionInfusionForms);
                prescriptionExternalForms = this.getPrescriptionForm(prescriptionExternalForms);

                return {
                    name: this.postData.name,
                    classification: this.postData.ownerType === OwnerType.SYSTEM ? this.postData.classification : '',
                    source: this.postData.source,
                    majorDisease: this.postData.majorDisease,
                    effect: this.postData.effect,
                    revision: this.postData.revision,
                    detail: {
                        prescriptionChineseForms,
                        prescriptionWesternForms,
                        prescriptionInfusionForms,
                        prescriptionExternalForms,
                    },
                    category: this.category,
                    parentId: this.categoryId,
                };
            },

            async createSubmit() {
                try {
                    this.buttonLoading = true;
                    const postData = this.formatPostData();
                    await this.preCreateHandle(postData);
                    const { data } = await OutpatientAPI.createTemplate('prescription', postData);
                    this.$Toast({
                        message: '创建成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    this.buttonLoading = false;
                    this.$emit('change-success', data, 'add', this._parentData);
                } catch (e) {
                    console.error(e);
                    this.buttonLoading = false;
                }
            },

            async updateSubmit() {
                try {
                    this.buttonLoading = true;
                    const { data } = await OutpatientAPI.updateTemplate(
                        'prescription',
                        this.postData.id,
                        this.formatPostData(),
                    );
                    this.$Toast({
                        message: '编辑成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    this.buttonLoading = false;
                    this.$emit('change-success', data, 'edit');
                } catch (e) {
                    console.error(e);
                    this.buttonLoading = false;
                }
            },

            async preCreateHandle(postData) {
                if (!postData.parentId) {
                    const { data } = await OutpatientAPI.createCatalogueByType('prescription', {
                        name: '新的目录',
                        category: this.category,
                        isFolder: 1,
                        parentId: null,
                    });
                    postData.parentId = data.id;
                    this._parentData = data;
                }
            },
        },
    };
</script>
