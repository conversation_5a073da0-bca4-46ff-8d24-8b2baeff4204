<template>
    <div ref="suggestions" class="special-requirement-suggestion-wrapper">
        <ul>
            <li
                v-for="(sr, index) in options"
                :key="index"
                :class="[
                    { 'is-selected': currentValue && currentValue === sr.name },
                    { 'is-hover': hoverIndex === index },
                    { 'is-disabled': !sr.name },
                    { large: sr.name.length > 4 },
                ]"
                @click="selectItem(sr.name)"
            >
                {{ sr.name }}
            </li>
        </ul>
    </div>
</template>

<script type="text/ecmascript-6">
    import Popper from 'utils/vue-popper';
    import { mapGetters } from 'vuex';

    export default {
        name: 'AbcUsagesSelect',
        mixins: [ Popper ],
        props: {
            visible: Boolean,
            currentValue: String,
        },

        data() {
            return {
                hoverIndex: null,
                zsIndex: 0,
                qtIndex: 0,
            };
        },
        computed: {
            ...mapGetters([
                'chineseMedicineConfig',
            ]),
            GroupSelect() {
                let parent = this.$parent;
                while (parent && parent.$options && parent.$options.name !== 'AbcGroupSelect') {
                    parent = parent.$parent;
                }
                return parent;
            },

            options() {
                const _arr = this.chineseMedicineConfig.specialRequirement;
                // 过滤掉先煎半小时，先煎一小时
                return _arr.filter((item) => {
                    return !item.excludeCashier;
                });
            },
        },
        watch: {
            visible: {
                immediate: true,
                handler(val) {
                    this.showPopper = val;
                },
            },
            currentValue: {
                immediate: true,
                handler(val) {
                    this.options.forEach((item, index) => {
                        if (val && val === item.name) {
                            this.hoverIndex = index;
                        }
                    });
                },
            },
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.suggestions;
            this.referenceElm = this.$parent.$refs.abcinput;
        },

        beforeDestroy() {
            this.doDestroy();
        },
        methods: {
            selectItem(val) {
                if (val) {
                    this.GroupSelect.$emit('handleOptionClick', val);
                }
            },

            /**
             * @desc 父组件按 下
             * <AUTHOR>
             * @date 2018/08/06 13:59:50
             */
            down() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                // todo 中药煎法特殊处理
                if (this.hoverIndex + 4 <= this.options.length - 1) {
                    this.hoverIndex += 4;
                }
            },

            /**
             * @desc 父组件按 上
             * <AUTHOR>
             * @date 2018/08/06 14:00:30
             */
            up() {
                // todo 中药煎法特殊处理
                if (this.hoverIndex > 3) {
                    this.hoverIndex -= 4;
                }
            },

            /**
             * @desc 父组件按 左
             * <AUTHOR>
             * @date 2018/08/06 14:00:54
             */
            left() {
                if (this.hoverIndex === 1) {
                    this.hoverIndex = 0;
                } else if (this.hoverIndex > 0) {
                    this.hoverIndex -= 1;
                }
            },

            /**
             * @desc 父组件按 右
             * <AUTHOR>
             * @date 2018/08/06 14:01:12
             */
            right() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                if (this.hoverIndex === 0) {
                    this.hoverIndex = 1;
                } else if (this.hoverIndex + 1 <= this.options.length - 1) {
                    this.hoverIndex += 1;
                }
            },

            /**
             * @desc 父组件按 回车
             * <AUTHOR>
             * @date 2018/08/06 14:01:35
             */
            enter() {
                if (this.hoverIndex === null) return false;
                this.selectItem(this.options[ this.hoverIndex ].name);
            },
        },

    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .special-requirement-suggestion-wrapper {
        position: absolute;
        top: 24px;
        left: 0;
        z-index: 9999;
        box-sizing: border-box;
        width: 322px;
        padding: 0;
        margin-top: 2px;
        overflow: hidden;
        background-color: #ffffff;
        border: 1px solid var(--abc-color-P7);
        border-radius: var(--abc-border-radius-small);
        box-shadow: var(--abc-shadow-1);

        ul {
            width: 100%;
            font-size: 0;
        }

        li:not(.title) {
            box-sizing: border-box;
            display: inline-block;
            width: 80px;
            height: 36px;
            font-size: 14px;
            line-height: 36px;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            border-bottom: 1px solid $P6;

            &:nth-child(odd) {
                border-right: 1px solid $P6;
            }

            &:hover {
                /* <!--background-color: $theme4;--> */
                background-color: $P4;
            }

            &.is-hover {
                /* <!--background-color: $theme4;--> */
                background-color: $P4;
            }

            &.is-selected {
                color: #ffffff;

                /* <!--background-color: $theme2;--> */
                background-color: #00ace9;
            }

            &.is-disabled {
                cursor: default;

                &:hover {
                    background-color: #ffffff;
                }
            }

            &.large {
                width: 160px;
            }

            &.last-item {
                border-right: 0;
            }
        }

        .title {
            display: block;
            width: 100%;
            height: 12px;
            font-size: 14px;
            cursor: default;
            border-right: 0;
            border-bottom: 1px solid $P6;
        }
    }
</style>
