<template>
    <div class="hospital-cost-check-table-wrapper">
        <abc-table
            ref="abcTablePro"
            type="pro"
            theme="white"
            class="hospital-cost-check-table"
            fill-height
            :fill-reference-el="fillReferenceEl"
            :loading="loading"
            :show-content-empty="showContentEmpty"
            :render-config="renderConfig"
            :data-list="curList"
            :custom-tr-key="itemKey"
            :custom-tr-class="itemClassStr"
            @sortChange="sortChange"
            @handleClickTr="handleClickTr"
        >
        </abc-table>
    </div>
</template>

<script>
    import TableConfig from './index.js';
    import { HisChargeFormItemStatusEnum } from '@/views-hospital/cost/utils/constants.js';
    import {
        HOSPITAL_PRICE_SCOPE_TYPE, HOSPITAL_PRICE_SETTLE_TYPE,
    } from 'utils/constants';
    import Clone from 'utils/clone';

    export default {
        props: {
            list: {
                type: Array,
                default: () => [],
            },
            fillReferenceEl: {
                type: HTMLElement,
                default: null,
            },
            loading: {
                type: Boolean,
                default: false,
            },
            showContentEmpty: {
                type: Boolean,
                default: true,
            },
            priceScopeType: {
                type: Number,
                default: HOSPITAL_PRICE_SCOPE_TYPE.SOURCE,
            },
            priceSettleType: {
                type: Number,
                default: HOSPITAL_PRICE_SETTLE_TYPE.SOURCE,
            },
        },
        data() {
            return {
                HisChargeFormItemStatusEnum,
                curSelectTr: null,
                sortOrder: {},
            };
        },
        computed: {
            renderConfig() {
                const isSort = !!this.sortOrder.orderType;
                return TableConfig.getRenderConfig(this.list, this.priceScopeType, this.priceSettleType,isSort);
            },
            curList() {
                const _list = Clone(this.list);
                return this.sortList(_list,this.sortOrder);
            },
        },
        methods: {
            itemKey(item) {
                return TableConfig.getItemKey(item);
            },
            itemClassStr(item) {
                const _arr = ['clickable'];
                if (this.itemKey(this.curSelectTr) === this.itemKey(item)) {
                    _arr.push('is-selected');
                }
                if (item.status === HisChargeFormItemStatusEnum.REFUND) {
                    _arr.push('is-disabled');
                }
                return _arr.join(' ');
            },
            handleClickTr(item) {
                this.curSelectTr = item;
                this.$emit('click-tr', item);
            },
            handleClearSelected() {
                this.curSelectTr = null;
                const { handleClearSelected } = this.$refs.abcTablePro || {};
                handleClearSelected && handleClearSelected();
            },
            sortChange(res) {
                this.sortOrder = res;
            },
            sortList(list,sortOrder) {
                const {
                    orderBy = '',
                    orderType = '',
                } = sortOrder;
                if (!orderType) {
                    return list;
                }
                if (orderBy === 'name') {
                    return this.sortListByName(list, orderBy, orderType);
                }
                return list.sort((a,b) => {
                    let aVal = a[orderBy];
                    let bVal = b[orderBy];
                    if (orderBy === 'adviceCreated') {
                        aVal = new Date(aVal);
                        bVal = new Date(bVal);
                    }
                    return orderType === 'asc' ? aVal - bVal : bVal - aVal;
                });

            },
            sortListByName(list, orderBy, orderType) {
                return list.sort((a, b) => {
                    if (orderType === 'asc') {
                        return a[orderBy].localeCompare(b[orderBy]);
                    }
                    return b[orderBy].localeCompare(a[orderBy]);
                });
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .hospital-cost-check-table-wrapper {
        position: relative;

        .group-line {
            position: absolute;
            top: 20px;
            right: 10px;
            z-index: 9;
            display: block;
            width: 7px;
            content: '';
            border-top: 2px solid #8f8f8f;
            border-right: 2px solid #8f8f8f;
            border-bottom: 2px solid #8f8f8f;
        }
    }
</style>

