<template>
    <div
        ref="goodsRemark"
        v-abc-click-outside="closePopper"
        :style="inputWrapperStyle"
        :class="[
            'abc-input-wrapper',
            'goods-remark-wrapper',
            size ? `abc-input-${ size }-wrapper` : '',
            {
                'is-disabled': disabled,
                'is-focus': isFocus || showPopper,
            },
        ]"
        data-cy="goods-remark"
        :title="currentValue"
        @click="clickSelect"
    >
        <input
            ref="abcinput"
            v-model="currentValue"
            :class="[
                'abc-input__inner',
                { 'text-center': textCenter },
                { 'with-tag': showInputTag }
            ]"
            :style="inputStyle"
            :tabindex="tabindex"
            :disabled="disabled && !canEditRemark"
            :readonly="readonly"
            :maxlength="maxLength"
            :placeholder="showInputTag ? '' : placeholder"
            data-cy="input-goods-remark"
            @blur="handleBlur"
            @focus="handleFocus"
            @keydown.enter="handelEnter"
            @keydown.delete="handleDelete"
            @keydown.tab="closePopper"
            @keydown.down.prevent="down"
            @keydown.up.prevent="up"
            @keydown.left="left"
            @keydown.right="right"
            @change="handlerChange"
        />
        <abc-tag-v2
            v-show="showInputTag"
            class="input-tag"
            variant="outline"
            size="tiny"
            theme="primary"
            @click.native="handleClickTag"
        >
            {{ tagName }}
        </abc-tag-v2>

        <medicine-remarks
            v-if="showPopper"
            ref="childComponent"
            :visible.sync="showPopper"
            :current-value="currentValue"
            :placement="placement"
            :select-pharmacy-no="curSelectPharmacyNo"
            :default-pharmacy="defaultPharmacy"
            :show-medicine-remark-options="showMedicineRemarkOptions"
            :show-medicine-source-options="showMedicineSourceOptions"
            :show-no-charge="showNoCharge"
            :charge-type="prFormItem.chargeType"
            :product-info="curSelectProductInfo"
            @changePharmacySource="$emit('changePharmacySource', $event)"
            @click.native="onPopoverClick"
        ></medicine-remarks>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import common from 'components/common/form';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';

    import MedicineRemarks from './medicine-remarks.vue';
    import { resetStockByPharmacyNo } from 'views/layout/prescription/utils.js';
    import { PharmacyTypeEnum } from '@abc/constants';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';

    export default {
        name: 'AbcGroupSelect',
        components: {
            MedicineRemarks,
        },
        directives: {
            focus: {
                // 指令的定义
                inserted(el) {
                    el.addEventListener('click', () => {
                        $(el).prev()[0].focus();
                    });
                },
            },
        },
        mixins: [common],
        props: {
            value: [String, Number],
            placeholder: String,
            tabindex: [Number, String],
            width: Number,
            disabled: Boolean,
            readonly: {
                type: Boolean,
                default: true,
            },
            index: Number,
            placement: {
                type: String,
                default: 'bottom-end',
            },
            size: String,
            maxLength: {
                type: [Number, String],
                default: 30,
            },

            // 聚焦自动展开options
            focusShowOptions: {
                type: Boolean,
                default: false,
            },
            textCenter: {
                type: Boolean,
                default: false,
            },
            inputStyle: [Object, String],

            prFormItem: {
                type: Object,
            },

            // 是开单来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },

            // 支持标签展示
            supportTag: {
                type: Boolean,
                default: false,
            },

            showMedicineRemarkOptions: {
                type: Boolean,
                default: false,
            },
            showMedicineSourceOptions: {
                type: Boolean,
                default: false,
            },
            showNoCharge: {
                type: Boolean,
                default: false,
            },
            departmentId: String,
            wardAreaId: String,
            // 是否可以编辑备注，跟disabled状态无关
            canEditRemark: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                showPopper: false,
                isFocus: false,
                hoverPIndex: null,
                defaultPharmacy: {
                    no: undefined,
                    name: undefined,
                    type: undefined,
                },
            };
        },
        computed: {
            ...mapGetters([
                'enableLocalPharmacyList',
                'pharmacyRuleList',
                'multiPharmacyCanUse',
            ]),

            watchData() {
                return {
                    departmentId: this.departmentId,
                    wardAreaId: this.wardAreaId,
                };
            },

            curSelectPharmacyNo() {
                const {
                    pharmacyNo,
                } = this.prFormItem || {};
                return pharmacyNo;
            },

            curSelectProductInfo() {
                return this.prFormItem?.productInfo || null;
            },

            currentValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            inputWrapperStyle() {
                let width = '';
                if (typeof this.width === 'number') {
                    width = `${this.width}px`;
                } else {
                    width = this.width;
                }
                return { width };
            },
            tagName() {
                const {
                    chargeType,
                    pharmacyType,
                    pharmacyNo,
                } = this.prFormItem || {};

                if (chargeType === OutpatientChargeTypeEnum.NO_CHARGE) {
                    return '自备';
                }

                if (!this.multiPharmacyCanUse) return '';
                if (this.defaultPharmacy.no === undefined) return '';
                if (pharmacyType !== PharmacyTypeEnum.LOCAL_PHARMACY) {
                    return '';
                }
                if (this.defaultPharmacy.no !== pharmacyNo) {
                    const res = this.enableLocalPharmacyList.find((it) => it.no === pharmacyNo);
                    return res?.name.slice(0, 3) || '';
                }
                return '';
            },

            showInputTag() {
                return this.supportTag && this.tagName;
            },
        },

        watch: {
            watchData: {
                handler(val) {
                    this.initDefaultPharmacy(val);
                },
                immediate: true,
            },
        },

        mounted() {

        },

        created() {
            this.$on('handleOptionClick', this.handleOptionSelect);
            this.$on('handleSourceClick', this.handleSourceClick);
        },
        beforeDestroy() {
            this.$off('handleOptionClick', this.handleOptionSelect);
            this.$off('handleSourceClick', this.handleSourceClick);
        },
        methods: {
            initDefaultPharmacy(options) {
                const {
                    departmentId,
                    wardAreaId,
                } = options;
                this.defaultPharmacy = getDefaultPharmacy(this.pharmacyRuleList, {
                    departmentId,
                    wardAreaId,
                    goodsInfo: this.prFormItem?.productInfo,
                });
            },
            handleClickTag() {
                this.$nextTick(() => {
                    this.$refs.abcinput.focus();
                    this.$refs.abcinput.selectionStart = 0;
                    this.$refs.abcinput.selectionEnd = this.$refs.abcinput.value.length;
                });
            },
            up(e) {
                if (this.readonly) {
                    e.preventDefault();
                }

                if (!this.showPopper) {
                    this.$emit('up', e);
                    return;
                }
                e.stopPropagation();
                this.$refs.childComponent?.up();
            },

            down(e) {
                if (this.readonly) {
                    e.preventDefault();
                }

                if (!this.showPopper) {
                    this.$emit('down', e);
                    return;
                }

                e.stopPropagation();
                this.$refs.childComponent?.down();
            },

            left(e) {
                if (this.readonly) {
                    e.preventDefault();
                }
                // 当下拉框展开的时候不能左右键切走
                if (!this.showPopper) {
                    this.$emit('left', e);
                    return;
                }

                e.stopPropagation();
                this.$refs.childComponent?.left();

            },

            right(e) {
                if (this.readonly) {
                    e.preventDefault();
                }
                // 当下拉框展开的时候不能左右键切走
                if (!this.showPopper) {
                    this.$emit('right', e);
                    return;
                }

                e.stopPropagation();
                this.$refs.childComponent?.right();
            },

            handleFocus(event) {
                this.isFocus = true;
                this.$emit('focus', event);
                if (this.focusShowOptions && !this.disabled && !this.readonly) {
                    this._isFocusShow = true;
                    this.showPopper = true;
                }
            },
            handlerChange() {
                this.$emit('change', this.currentValue);
            },

            handleBlur() {
                this.isFocus = false;
                this._blurOccurred = true;
            },

            // blur 之后如果是点在popover上不触发blur，否则input宽度会抖
            onPopoverClick(e) {
                if (this._blurOccurred) {
                    const isInPopover = e.composedPath().some((item) => {
                        if (!item.className) return false;
                        if (typeof item.className !== 'string') return false;
                        return (
                            item.className?.includes('medicine-remark-suggestion-wrapper')
                        );
                    });
                    if (isInPopover) return;
                    this.$emit('blur', event);
                    this._blurOccurred = false;
                }
            },
            handleDelete(event) {
                this.isFocus = true;
                this.$emit('delete', event);

                if (!this.currentValue && this.supportTag) {
                    this.$set(this.prFormItem, 'chargeType', OutpatientChargeTypeEnum.DEFAULT);
                    this.prFormItem.pharmacyType = this.defaultPharmacy.type;
                    this.prFormItem.pharmacyNo = this.defaultPharmacy.no;
                    this.prFormItem.pharmacyName = this.defaultPharmacy.name;
                    resetStockByPharmacyNo(this.prFormItem);
                }
            },

            clickSelect() {
                if (this.disabled || this.readonly) return false;
                /**
                 * @desc 解决focus click冲突问题
                 * 用户点击 会先触发focus事件 后执行click事件
                 * 开启了 focusShowOptions focus事件就已经展开options，后执行的click事件不再响应
                 * <AUTHOR>
                 * @date 2022-01-26 17:35:58
                 */
                if (this._isFocusShow) {
                    this._isFocusShow = false;
                    return false;
                }
                this.showPopper = !this.showPopper;
            },

            handelEnter(event) {
                if (this.showPopper) {
                    this.$refs.childComponent?.enter();
                    this.$emit('enter', event);
                    this.showPopper = false;
                } else {
                    this.showPopper = !this.showPopper;
                }
            },

            closePopper() {
                if (this.showPopper) {
                    this.showPopper = false;
                    this.$emit('blur', this._blurEvent);
                }
            },

            handleOptionSelect(val) {
                val !== this.value && this.$emit('change', val, this.index);
                this.formItem && this.formItem.$emit('formFieldChange', val);
                this.currentValue = val;
                this.closePopper();
            },

            /**
             * @desc 药品来源选择
             * <AUTHOR>
             * @date 2022-10-14 11:55:15
             */
            handleSourceClick(val) {
                if (val.templateType === 0 && this.showNoCharge) {
                    this.$set(this.prFormItem, 'chargeType', OutpatientChargeTypeEnum.NO_CHARGE);
                    this.prFormItem.pharmacyType = this.defaultPharmacy.type;
                    this.prFormItem.pharmacyNo = this.defaultPharmacy.no;
                    this.prFormItem.pharmacyName = this.defaultPharmacy.name;
                    this.$nextTick(() => {
                        this.$Toast({
                            message: '备注选择【自备】，该药品将不会纳入划价收费',
                            duration: 1500,
                            referenceEl: this.$refs.goodsRemark,
                        });
                    });
                } else {
                    this.prFormItem.pharmacyType = val.pharmacyType;
                    this.prFormItem.pharmacyNo = val.no;
                    this.prFormItem.pharmacyName = val.name;
                    this.$set(this.prFormItem, 'chargeType', OutpatientChargeTypeEnum.DEFAULT);
                }
                resetStockByPharmacyNo(this.prFormItem);
                this.$emit('changeExtend', val);
                this.closePopper();
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.goods-remark-wrapper {
    position: relative;
    cursor: pointer;

    .abc-input__inner {
        width: 100%;
        height: 28px;
        padding: 3px 4px;
        line-height: 1;
        text-align: left;
        cursor: pointer;

        &.text-center {
            padding: 3px 0;
            text-align: center;
        }

        &.with-tag {
            padding-left: 44px !important;
        }
    }

    .input-tag {
        position: absolute;
        top: 12px;
        left: 4px;
        z-index: 3;
    }
}
</style>
