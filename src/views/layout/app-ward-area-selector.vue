<template>
    <app-header-business-selector
        :options="currentWardAreaList"
        :selected-id="currentWardAreaId"
        @change="handleChange"
    >
        <abc-tag-v2
            slot="append"
            size="mini"
            variant="light"
        >
            停用
        </abc-tag-v2>
    </app-header-business-selector>
</template>
<script>
    import { mapState } from 'vuex';
    import AppHeaderBusinessSelector from 'views/layout/app-header-business-selector.vue';
    import { RELATIVE_STATUS } from 'utils/constants';

    export default {
        name: 'AppWardAreaSelector',
        components: { AppHeaderBusinessSelector },
        computed: {
            ...mapState('hospitalGlobal', ['wardAreaList', 'currentWardAreaId']),
            currentWardAreaList() {
                return this.wardAreaList.map((it) => {
                    return {
                        ...it,
                        slot: it.status === RELATIVE_STATUS.DEACTIVE,
                        disable: it.status === RELATIVE_STATUS.DEACTIVE,
                        status: it.status,
                    };
                }).sort((a, b) => a.status - b.status);
            },
        },
        methods: {
            // 切换病区
            handleChange(wardArea) {
                this.$router.replace({
                    name: this.$route.name,
                    params: {
                        ...this.$route.params,
                        wardId: wardArea.id,
                    },
                });
            },
        },
    };
</script>
