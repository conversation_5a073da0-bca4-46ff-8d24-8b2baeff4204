<template>
    <div>
        <abc-descriptions
            class="normal-patient-card-info__descriptions"
            :column="2"
            :label-width="88"
            :bordered="false"
        >
            <abc-descriptions-item
                v-for="item in renderItems"
                :key="item.label"
                :label="item.label"
                :span="item.span || 1"
                :content-style="{ paddingRight: '8px' }"
            >
                <div v-abc-title.ellipsis="item.value"></div>
            </abc-descriptions-item>
        </abc-descriptions>
    </div>
</template>

<script>
    import { formatAddress } from '@/utils';
    import { formatDate } from '@abc/utils-date';
    import { MaritalStatusEnum } from 'views/crm/constants';
    import { RecommendService } from '@/service/recommend';


    export default {
        name: 'NormalPatientCard',

        props: {
            patient: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            visitSourceInfo() {
                if (!this.patient.patientSource) return '-';

                return this.initCascaderData(this.patient).map((o) => o.label).join('-');
            },

            scrmInfo() {
                return this.patient.scrmInfo?.customerCorpUserRelates?.map((item) => {
                    return item.employeeName || '未知员工';
                }).join(',') || '-';
            },

            renderItems() {
                return [
                    {
                        label: '生日', value: this.patient.birthday || '-',
                    },
                    {
                        label: '体重', value: this.patient.weight ? `${this.patient.weight}kg` : '-',
                    },
                    {
                        label: '单位', value: this.patient.company || '-',
                    },
                    {
                        label: '职业', value: this.patient.profession || '-',
                    },
                    {
                        label: '婚姻', value: this.getMarital(this.patient.marital),
                    },
                    {
                        label: '微信', value: this.patient.wxNickName || '-',
                    },
                    {
                        label: '到店', value: this.patient.visitReason || '-',
                    },
                    {
                        label: '企微', value: this.scrmInfo,
                    },
                    {
                        label: '来源', value: this.visitSourceInfo,
                    },
                    {
                        label: '档案号', value: this.patient.sn || '-',
                    },
                    {
                        label: '建档', value: formatDate(this.patient.created, 'YYYY-MM-DD'),
                    },
                    {
                        label: '医保号', value: this.patient.shebaoCardInfo ? this.patient.shebaoCardInfo.cardNo : '-',
                    },
                    {
                        label: '家长名', value: this.patient.parentName || '-',
                    },
                    {
                        label: '名族', value: this.patient.ethnicity || '-',
                    },
                    {
                        label: '备注',
                        value: this.patient.remark || '-',
                        span: 2,
                    },
                    {
                        label: '身份证',
                        value: this.patient.idCard || '-',
                        span: 2,
                    },
                    {
                        label: '地址',
                        value: formatAddress(this.patient.address) || '-',
                        span: 2,
                    },
                ];
            },
        },

        methods: {
            getMarital(marital) {
                switch (marital) {
                    case MaritalStatusEnum.single:
                        return '未婚';
                    case MaritalStatusEnum.married:
                        return '已婚';
                    case MaritalStatusEnum.divorced:
                        return '离异';
                    case MaritalStatusEnum.widow:
                        return '丧偶';
                    default:
                        return '-';
                }
            },

            initCascaderData(patient) {
                if (!patient.patientSource) return [];

                const {
                    id, sourceFromName, sourceFrom, name,
                } = patient.patientSource;

                return RecommendService.getInstance().initCascaderValue({
                    visitSourceId: id,
                    visitSourceName: name,
                    visitSourceFrom: sourceFrom,
                    visitSourceFromName: sourceFromName,
                });
            },
        },
    };
</script>

<style lang="scss">
.normal-patient-card-info__descriptions.abc-descriptions-wrapper {
    .abc-descriptions__view {
        padding: 0;
    }
}
</style>
