<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
    <div>
        <abc-dialog
            v-model="showDialog"
            title="班次管理"
            append-to-body
            size="large"
        >
            <div class="dialog-content">
                <abc-flex align="center" justify="space-between">
                    <abc-checkbox-button
                        v-model="modelConsultingRoomSetting"
                        @input="onToggleRoomSetting"
                    >
                        排班时设置诊室
                    </abc-checkbox-button>

                    <abc-button variant="ghost" @click="selectShift(null)">
                        增加班次
                    </abc-button>
                </abc-flex>

                <abc-table
                    :loading="tableLoading"
                    :render-config="renderConfig"
                    :data-list="data"
                    :empty-opt="{ label: `暂无数据` }"
                    style="height: 400px; margin-top: 12px;"
                >
                </abc-table>
            </div>
        </abc-dialog>

        <abc-dialog
            v-if="showShiftDialog"
            v-model="showShiftDialog"
            :title="postData.id ? '修改班次' : '增加班次'"
            size="small"
            append-to-body
        >
            <div class="dialog-content">
                <abc-form ref="form" item-no-margin>
                    <abc-form-item label="班次名称" required>
                        <abc-input
                            v-model="postData.name"
                            :width="312"
                            trim
                            :max-length="25"
                        ></abc-input>
                    </abc-form-item>

                    <abc-form-item label="时间段" required style="margin-top: 16px;">
                        <abc-space>
                            <abc-select v-model="postData.start" :width="144" scroll-to-value="06:00">
                                <abc-option
                                    v-for="item in filterStartOption"
                                    :key="`${item.value }start`"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>


                            <abc-flex align="center" justify="center" style="width: 8px;">
                                <abc-text theme="gray">
                                    -
                                </abc-text>
                            </abc-flex>

                            <abc-select v-model="postData.end" :width="144">
                                <abc-option
                                    v-for="item in filterEndOption"
                                    :key="`${item.value }end`"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-space>
                    </abc-form-item>
                </abc-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <abc-button @click="confirm">
                    保存
                </abc-button>
                <abc-button type="blank" @click="showShiftDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </div>
</template>

<script>
    import ScheduleAPI from 'api/registrations/schedule';
    import {
        mapActions, mapGetters,
    } from 'vuex';
    let timeOption = [];

    function genTimeOption() {
        timeOption = [];
        for (let i = 0; i <= 23; i++) {
            let str = i;
            if (i < 10) {
                str = `0${i}`;
            }

            const step = 5;
            for (let minute = 0; minute < 60; minute += step) {
                timeOption.push({
                    label: `${str}:${minute < 10 ? `0${minute}` : minute}`,
                    value: `${str}:${minute < 10 ? `0${minute}` : minute}`,
                });
            }
        }
    }

    export default {
        props: {
            value: Boolean,
            selected: Array,
            shifts: Array,
        },
        data() {
            return {
                tableLoading: false,
                showShiftDialog: false,
                data: this.shifts,

                modelConsultingRoomSetting: false,

                postData: {
                    name: '',
                    start: '',
                    end: '',
                },
            };
        },

        computed: {
            ...mapGetters(['consultingRoomSetting', 'isOpenCall']),

            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            // 是否支持排班时同时设置诊室
            isScheduleWithConsultingRoom() {
                return this.viewDistributeConfig.Settings.schedule.isScheduleWithConsultingRoom;
            },

            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            filterEndOption() {
                const list = this._timeOption;
                if (this.postData.start) {
                    const start = this.postData.start.split(':');
                    const startNum = start[0] * 100 + start[1] * 1;
                    return list.filter((item) => {
                        const arr = item.value.split(':');
                        return arr[0] * 100 + arr[1] * 1 > startNum;
                    });
                }
                return list;
            },

            filterStartOption() {
                const list = this._timeOption;
                if (this.postData.end) {
                    const end = this.postData.end.split(':');
                    const endNum = end[0] * 100 + end[1] * 1;
                    return list.filter((item) => {
                        const arr = item.value.split(':');
                        return arr[0] * 100 + arr[1] * 1 < endNum;
                    });
                }
                return list;
            },

            renderConfig() {
                const that = this;

                return {
                    list: [
                        {
                            label: '班次',
                            key: 'name',
                            style: {
                                minWidth: '328px',
                                width: '328px',
                                maxWidth: '328px',
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '时间',
                            key: ' time',
                            style: {
                                width: '126px',
                                maxWidth: '126px',
                                minWidth: '126px',
                                textAlign: 'left',
                            },
                            dataFormatter: (_, item) => `${item.start} - ${item.end}`,
                        },
                        {
                            label: '操作',
                            key: 'op',
                            style: {
                                width: '126px',
                                maxWidth: '126px',
                                minWidth: '126px',
                                textAlign: 'center',
                            },
                            customRender(h, row) {
                                return (
                                    <abc-table-cell>
                                        <abc-button variant="text" onClick={() => that.selectShift(row)}>
                                            修改
                                        </abc-button>
                                        <abc-button
                                            variant="text"
                                            theme="danger"
                                            onClick={() => that.deleteConfirm(row.id)}
                                        >
                                            删除
                                        </abc-button>
                                    </abc-table-cell>
                                );
                            },
                        },
                    ],
                };
            },

        },

        watch: {
            shifts: {
                handler (val) {
                    this.data = val;
                },
                deep: true,
            },
            consultingRoomSetting: {
                handler(newValue) {
                    this.modelConsultingRoomSetting = !!newValue;
                },
            },
        },

        created() {
            genTimeOption();
            this._timeOption = timeOption;
            this.modelConsultingRoomSetting = !!this.consultingRoomSetting;
        },
        methods: {
            ...mapActions(['setConsultingRoomSetting']),
            selectShift(item) {
                if (item) {
                    Object.assign(this.postData, item);
                } else {
                    this.postData = {
                        name: '',
                        start: '',
                        end: '',
                    };
                }
                this.showShiftDialog = true;
            },

            confirm() {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        if (this.postData.id) {
                            this.$confirm({
                                type: 'warn',
                                title: '提示',
                                content: '修改班次信息，将影响该班次已排班所有人员，是否确定？',
                                onConfirm: () => {
                                    this.submit();
                                },
                            });
                        } else {
                            this.submit();
                        }
                    }
                });
            },

            async submit() {
                try {
                    this.tableLoading = true;

                    if (this.postData.id) {
                        await ScheduleAPI.updateScheduleSetup(this.postData.id, this.postData);
                    } else {
                        await ScheduleAPI.addScheduleSetup(this.postData);
                    }
                    this.showShiftDialog = false;
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.$emit('refreshShifts');
                } catch (err) {
                    console.error(err);
                } finally {
                    this.tableLoading = false;
                }
            },

            async deleteConfirm(id) {
                const isExistedSchedule = await this.checkSchedule(id);
                let content = '';
                if (isExistedSchedule) {
                    content = [
                        '已有医生使用该班次，删除班次将取消医生该班次的所有排班，是否确定删除？',
                        '删除仅影响含今天的未来排班，不影响过去排班',
                    ];
                } else {
                    content = '删除班次，将取消该班次人员的所有排班，是否确定删除？';
                }
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content,
                    onConfirm: () => {
                        this.deleteSchedule(id);
                    },
                });
            },
            async checkSchedule(id) {
                const { data } = await ScheduleAPI.checkSchedule(id);
                return data && data.isExistedSchedule;
            },

            async deleteSchedule(id) {
                try {
                    this.tableLoading = true;
                    await ScheduleAPI.deleteScheduleSetup(id);

                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                    this.$emit('refreshShifts');
                    this.$emit('refreshSchedules');
                } catch (err) {
                    console.error(err);
                } finally {
                    this.tableLoading = false;
                }
            },

            /**
             * desc [切换排班诊室设置时]
             */
            onToggleRoomSetting(value) {
                if (!value && this.isOpenCall) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '已启用排队叫号功能，必须设置诊室才可正常使用。',
                        onClose: () => {
                            console.log('modelConsultingRoomSetting');
                            this.modelConsultingRoomSetting = true;
                        },
                    });
                } else {
                    this.setConsultingRoomSetting(value);
                }
            },
        },
    };
</script>
