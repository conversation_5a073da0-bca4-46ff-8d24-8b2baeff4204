<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="title"
        append-to-body
        content-styles="width: 400px"
        data-cy="address-editor-dialog"
    >
        <abc-form
            ref="form"
            v-abc-loading="loading"
            label-position="left"
            :label-width="52"
            item-block
        >
            <abc-form-item label="姓名" required style="margin-bottom: 16px;">
                <abc-input
                    v-model="postData.deliveryName"
                    v-abc-focus-selected
                    :max-length="20"
                    trim
                    required
                    :width="300"
                    data-cy="address-editor-name-input"
                >
                </abc-input>
            </abc-form-item>

            <abc-form-item
                v-abc-focus-selected
                label="手机"
                required
                style="margin-bottom: 16px;"
                :validate-event="handleMobileValidate"
            >
                <abc-input-mobile
                    v-model="postData.deliveryMobile"
                    :country-code.sync="postData.deliveryCountryCode"
                    :width="300"
                    data-cy="add-address"
                ></abc-input-mobile>
            </abc-form-item>

            <abc-form-item
                label="住址"
                required
                style="margin-bottom: 16px;"
                :validate-event="validateAddress"
            >
                <abc-address-selector v-model="postData" :width="300" data-cy="address-editor-selector"></abc-address-selector>
            </abc-form-item>

            <abc-form-item
                label=" "
                required
                :show-red-dot="false"
                style="align-items: flex-start; margin-bottom: 0;"
            >
                <abc-textarea
                    v-model="postData.addressDetail"
                    :width="300"
                    :rows="2"
                    trim
                    :maxlength="150"
                    placeholder="详细地址"
                    data-cy="address-editor-detail-textarea"
                >
                </abc-textarea>
            </abc-form-item>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button
                v-if="id"
                type="danger"
                :loading="deleteLoading"
                style="margin-right: auto;"
                data-cy="address-editor-delete"
                @click="clickDeleteEvent"
            >
                删除
            </abc-button>

            <abc-button
                type="primary"
                :loading="btnLoading"
                :disabled="!isUpdate"
                data-cy="address-editor-confirm"
                @click="submitPrev"
            >
                确定
            </abc-button>
            <abc-button type="blank" data-cy="address-editor-cancel" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import {
        validateMobile, validateAddress,
    } from 'utils/validate';
    import CrmAPI from 'api/crm.js';
    import { isEqual } from 'utils/lodash';
    import { defaultCountryCode } from 'utils/country-codes';

    export default {
        name: 'AddressEditor',
        props: {
            patientId: [String, Number],
            id: [String, Number],
            value: Boolean,
            deliveryInfo: Object,
        },
        data() {
            return {
                loading: false,
                btnLoading: false,
                deleteLoading: false,
                isUpdate: false,
                postData: {
                    deliveryName: '',
                    deliveryMobile: '',
                    deliveryCountryCode: defaultCountryCode,
                    addressProvinceName: '',
                    addressCityName: '',
                    addressDistrictName: '',
                    addressProvinceId: '',
                    addressCityId: '',
                    addressDistrictId: '',
                    addressDetail: '',
                },
            };
        },
        computed: {
            title() {
                return this.id ? '编辑收货地址' : '新增收货地址';
            },

            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        watch: {
            postData: {
                handler () {
                    this.isUpdate = !isEqual(this._postDataCache, this.postData);
                },
                deep: true,
            },
            deliveryInfo: {
                handler(val) {
                    this.postData = Object.assign({}, val, {
                        deliveryCountryCode: val.deliveryCountryCode || defaultCountryCode,
                    });
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            validateMobile,
            validateAddress,

            submitPrev() {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        this.id || this.postData.tempId ? this.updateSubmit() : this.createSubmit();
                    }
                });
            },

            async createSubmit() {
                try {
                    this.btnLoading = true;
                    if (this.patientId && this.patientId !== '00000000000000000000000000000000') {
                        await CrmAPI.createPatientDelivery(this.patientId, this.postData);
                        this.successHandler('新增');
                    } else {
                        this.btnLoading = false;
                        this.showDialog = false;
                        this.$emit('confirm', this.postData);
                    }
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            async updateSubmit() {
                try {
                    this.btnLoading = true;
                    if (this.patientId && this.patientId !== '00000000000000000000000000000000') {
                        await CrmAPI.updatePatientDelivery(this.patientId, this.id, this.postData);
                        this.successHandler('修改');
                    } else {
                        this.btnLoading = false;
                        this.showDialog = false;
                        this.$emit('confirm', this.postData);
                    }
                } catch (e) {
                    this.btnLoading = false;
                }
            },

            successHandler(typeStr) {
                this.btnLoading = false;
                this.deleteLoading = false;
                this.showDialog = false;
                this.$emit('refresh');
                this.$Toast({
                    message: `${typeStr}成功`,
                    type: 'success',
                });
            },

            clickDeleteEvent() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除地址后将无法恢复，是否确定？',
                    onConfirm: async () => {
                        await this.deleteSubmit();
                    },
                });
            },
            async deleteSubmit() {
                try {
                    this.deleteLoading = true;
                    await CrmAPI.deletePatientDelivery(this.patientId, this.id);
                    this.successHandler('删除');
                } catch (e) {
                    this.deleteLoading = false;
                }
            },
            /**
             * @description: 手机号校验
             * @date: 2023-07-05 15:56:15
             * @author: Horace
             * @param values: 事件返回的值，包含countryCode以及mobile
             * @param callback: 回调函数
             * @return
             */
            handleMobileValidate(values, callback) {
                const [countryCode = '', mobile = ''] = values;
                if (!countryCode) {
                    return callback({
                        validate: false, message: '无法确认手机号所在地区，请联系ABC客服！',
                    });
                }
                this.validateMobile(mobile, callback, this.postData.deliveryCountryCode);
            },
        },
    };
</script>
