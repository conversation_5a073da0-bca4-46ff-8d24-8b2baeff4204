<template>
    <div v-if="remoteCode" class="app-remote-code">
        ID: {{ remoteCode }}
    </div>
</template>

<script>
    import { getRemoteCode } from '@/core/electron-tab-helper';

    export default {
        name: 'AppRemoteCode',
        data() {
            return {
                remoteCode: '',
            };
        },
        created() {
            this.getRemoteCode();
        },
        methods: {
            async getRemoteCode() {
                this.remoteCode = await getRemoteCode();
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/abc-common.scss';

.app-remote-code {
    min-width: 32px;
    margin-right: 10px;
    color: $T4;
    opacity: 0.6;
}
</style>
