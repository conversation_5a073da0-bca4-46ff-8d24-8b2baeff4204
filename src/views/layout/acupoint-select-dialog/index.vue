<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        title="选择穴位"
        custom-class="acupoint-select-dialog"
        content-styles="padding:0; width: 700px"
        header-style="padding-top: 0; padding-bottom: 0; padding-left: 4px;"
    >
        <template #title>
            <abc-tabs-v2
                v-model="selectedTab"
                size="huge"
                :border="false"
                :option="scopeOption"
                @change="changeTab"
            ></abc-tabs-v2>
        </template>

        <div v-if="currentSelectedTab.label === BODY_PART_TITLE_ENUM.STBW" class="body-part-content">
            <div class="body-part-group">
                <div class="left-title"></div>
                <ul>
                    <li
                        v-for="position in '上下左右前后'"
                        :key="position"
                        :class="{
                            'is-selected': selectedPosition.indexOf(position) > -1
                        }"
                        @click="handleClickPosition(position)"
                    >
                        {{ position }}
                    </li>
                </ul>
            </div>
            <div v-for="group in catalogues" :key="group.name" class="body-part-group">
                <div class="left-title">
                    {{ group.name }}
                </div>
                <ul>
                    <li
                        v-for="item in group.children"
                        :key="item.name"
                        @click="handleClickBody(item)"
                    >
                        {{ item.name }}
                    </li>
                </ul>
            </div>
        </div>

        <div v-else class="acupoint-content">
            <div class="left-col">
                <abc-dropdown
                    v-if="currentSelectedTab.label === BODY_PART_TITLE_ENUM.LPXX"
                    :min-width="140"
                    @change="changeMainCategory"
                >
                    <div slot="reference" class="lpxx-content">
                        {{ currentSelectedLPXX }} <abc-icon icon="dropdown_triangle" size="12"></abc-icon>
                    </div>
                    <abc-dropdown-item
                        v-for="item in LPXXList"
                        :key="item.name"
                        :label="item.name"
                        :value="item"
                    ></abc-dropdown-item>
                </abc-dropdown>
                <ul>
                    <li
                        v-for="(item, index) in catalogues"
                        :key="item.name + index"
                        :class="{
                            'is-selected': item.name === selectedCategory,
                            'ellipsis': item.groupFlag !== 1,
                            'has-top-border': item.groupFlag === 1,
                        }"
                        @click="changeCategory(item)"
                    >
                        {{ item.name }}
                        <div class="selected-count">
                            {{ selectedCount(item) || '' }}
                        </div>
                    </li>
                </ul>
            </div>
            <div class="right-col">
                <ul class="acupoint-list">
                    <li
                        v-for="item in acupoints"
                        :key="item.id"
                        :class="{
                            'is-selected': acupointSelected(item),
                        }"
                        @click="handleClickAcupoint(item)"
                    >
                        {{ item.name }}
                        <i class="iconfont cis-icon-chosen"></i>
                    </li>
                </ul>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <ul class="selected-acupoint-list">
                <li v-for="(item, index) in selectedList" :key="item.id || item.name">
                    {{ item.name }}
                    <div class="delete-icon" @click="deleteSelected(index)">
                        <i></i>
                    </div>
                </li>
            </ul>
            <abc-button v-if="!disabled" style="margin-left: auto;" @click="confirmHandle">
                确定
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import Clone from 'utils/clone';
    import Store from '@/store';

    // 首字母命名
    export const BODY_PART_TITLE_ENUM = {
        SSJX: '十四经穴',
        JYQX: '经外奇穴',
        BWQX: '部位取穴',
        LPXX: '流派选穴',
        STBW: '身体部位',
    };

    export const AcupointTypeEnum = Object.freeze({
        ACUPOINT: 0,
        BODY_PART: 1,
    });

    export default {
        name: 'AcupointSelectDialog',
        components: {
        },
        props: {
            value: Boolean,
            selected: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            disabled: {
                type: Boolean,
                required: false,
            },
            onChange: {
                type: Function,
                required: true,
            },
        },
        data() {
            return {
                visible: false,
                selectedTab: 0,
                selectedCategory: '',
                BODY_PART_TITLE_ENUM,
                // 左侧顶部tabs
                scopeOption: [],
                // 左侧下面目录列表
                catalogues: [],
                // 右侧穴位列表
                acupoints: [],
                // 右侧下方选中列表
                selectedList: [],

                // 流派选穴列表
                currentSelectedLPXX: '',
                LPXXList: [],

                // 选中的身体部位方向
                selectedPosition: [],
            };
        },
        computed: {
            currentSelectedTab() {
                return this.scopeOption[this.selectedTab] || {};
            },
        },
        watch: {
            visible(newVal) {
                if (!newVal) {
                    this.destroyElement();
                }
            },
        },
        async created() {
            this.selectedList = Clone(this.selected.filter((item) => {return item.id || item.name;})); //不过滤自定义内容,只过滤掉没有穴位命名也没有穴位id的元素
            await this.initData();
        },
        methods: {
            async initData() {
                await Store.dispatch('cdssData/initCdssData');
                const {
                    acupuncture, bodyPosition,
                } = Store.state.cdssData;
                this.scopeOption = [];
                this.catalog = new Map();

                acupuncture.forEach((item, index) => {
                    this.scopeOption.push({
                        label: item.name,
                        value: index,
                        separation: index === acupuncture.length - 1,
                    });
                    this.catalog.set(item.name, item.children);
                    if (item.name === BODY_PART_TITLE_ENUM.LPXX) {
                        this.LPXXList = item.children;
                    }
                });
                this.scopeOption.push({
                    label: bodyPosition.name,
                    value: acupuncture.length,
                });
                this.catalog.set(bodyPosition.name, bodyPosition.children);
                // 默认选中第一个tab
                this.changeTab(0);
            },

            changeTab(index) {
                this.selectedTab = index;
                this.catalogues = this.catalog.get(this.currentSelectedTab.label);
                // 添加安全检查，确保 this.catalogues 存在
                if (!this.catalogues || !this.catalogues.length) return;
                const firstItem = this.catalogues[0];
                if (!firstItem) return;
                if (this.currentSelectedTab.label === BODY_PART_TITLE_ENUM.LPXX) {
                    this.changeMainCategory(firstItem);
                } else {
                    this.changeCategory(firstItem);
                }
            },

            // 切换流派选穴下拉
            changeMainCategory(item) {
                this.currentSelectedLPXX = item.name;
                this.catalogues = item.children;
                const firstItem = this.catalogues[0];
                firstItem && this.changeCategory(firstItem);
            },

            changeCategory(item) {
                this.selectedCategory = item.name;
                this.acupoints = item.children;
            },

            /**
             * @desc 选择方位，当选择确定身体部分后拼接，清空
             * <AUTHOR>
             * @date 2022-03-17 10:38:08
             */
            handleClickPosition(item) {
                if (this.disabled) return;
                const _index = this.selectedPosition.findIndex((it) => it === item);
                if (_index > -1) {
                    this.selectedPosition.splice(_index, 1);
                } else {
                    this.selectedPosition.push(item);
                }
            },

            /**
             * @desc 选择身体部位
             * <AUTHOR>
             * @date 2022-03-17 10:39:36
             */
            handleClickBody(item) {
                if (this.disabled) return;
                const name = this.selectedPosition.join('') + item.name;
                const index = this.selectedList.findIndex((it) => it.name === name);
                if (index === -1) {
                    this.$set(item, 'position', '');
                    this.selectedList.push({
                        ...item,
                        name,
                        acupointType: AcupointTypeEnum.BODY_PART,
                    });
                    this.selectedPosition = [];
                }
            },

            /**
             * @desc 选择穴位
             * <AUTHOR>
             * @date 2022-03-17 10:47:22
             */
            handleClickAcupoint(item) {
                if (this.disabled) return;
                const index = this.selectedList.findIndex((it) => {
                    return it.name && it.name === item.name;
                });
                if (index === -1) {
                    this.$set(item, 'position', '');
                    item.acupointType = AcupointTypeEnum.ACUPOINT;
                    this.selectedList.push(item);
                } else {
                    this.selectedList.splice(index, 1);
                }
            },

            selectedCount(item) {
                let count = 0;
                item.children.forEach((it) => {
                    if (this.selectedList.findIndex((selected) => {
                        return selected.name && selected.name === it.name;
                    }) > -1) {
                        count++;
                    }
                });
                return count;
            },

            acupointSelected({ name }) {
                return this.selectedList.findIndex((item) => {
                    return item.name && item.name === name;
                }) > -1;
            },

            deleteSelected(index) {
                this.selectedList.splice(index, 1);
            },

            confirmHandle() {
                this.onChange(this.selectedList);
                this.visible = false;
            },

            destroyElement() {
                this.$destroy();
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme';
    @import 'src/styles/mixin';

    .acupoint-select-dialog {
        .acupoint-content,
        .body-part-content {
            width: 100%;
            height: 400px;
        }

        .acupoint-content {
            display: flex;

            .left-col {
                display: flex;
                flex-direction: column;
                width: 150px;
                height: 100%;
                padding-top: 4px;
                background-color: #f9fafc;
                border-right: 1px solid $P6;

                .lpxx-content {
                    display: flex;
                    align-items: center;
                    width: 149px;
                    height: 32px;
                    padding: 0 9px 0 16px;
                    font-weight: bold;
                    border-bottom: 1px solid $P6;
                }

                ul {
                    flex: 1;
                    overflow-y: auto;
                    overflow-y: overlay;

                    @include scrollBar;

                    li {
                        position: relative;
                        display: flex;
                        align-items: center;
                        height: 30px;
                        padding: 0 16px;
                        font-size: 13px;
                        cursor: pointer;

                        &.has-top-border {
                            margin: 8px 0;

                            &::before {
                                position: absolute;
                                top: -4px;
                                left: 16px;
                                width: calc(100% - 30px);
                                height: 1px;
                                content: ' ';
                                background-color: $P6;
                            }
                        }

                        &.is-selected {
                            background-color: $P6;
                        }

                        &:not(.is-selected):hover {
                            background-color: $P4;
                        }
                    }

                    .selected-count {
                        margin-left: auto;
                        color: $G1;
                    }
                }
            }

            .right-col {
                position: relative;
                flex: 1;
                width: 0;
                height: 100%;

                .acupoint-list {
                    display: flex;
                    flex: 1;
                    flex-wrap: wrap;
                    align-content: flex-start;
                    height: 100%;
                    padding-right: 8px;
                    overflow-y: auto;
                    overflow-y: overlay;

                    @include scrollBar;

                    > li {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 20%;
                        height: 32px;
                        font-size: 13px;
                        cursor: pointer;
                        border-right: 1px dashed $P6;
                        border-bottom: 1px dashed $P6;

                        &:nth-child(5n) {
                            border-right: none;
                        }

                        &:hover {
                            background-color: $P4;
                        }

                        &:active {
                            background-color: $P3;
                        }

                        &.is-selected {
                            color: $G1;

                            .iconfont {
                                display: inline-block;
                            }
                        }

                        .iconfont {
                            position: absolute;
                            top: 4px;
                            right: 4px;
                            display: none;
                            font-size: 12px;
                            color: $G1;
                        }
                    }
                }
            }
        }

        .body-part-content {
            padding: 12px 16px 0;
            overflow-y: auto;
            overflow-y: overlay;

            @include scrollBar;

            .body-part-group {
                position: relative;
                display: flex;
                padding: 0 0 8px 0;

                &:not(:last-child) ul {
                    border-bottom: 1px solid $P6;
                }

                .left-title {
                    width: 42px;
                    padding: 2px 0;
                    color: $T2;
                }

                ul {
                    position: relative;
                    flex: 1;
                    width: 0;
                    padding-bottom: 5px;
                }

                li {
                    display: inline-block;
                    padding: 5px 7px;
                    line-height: 1;
                    color: $T1;
                    cursor: pointer;
                    user-select: none;
                    border-radius: var(--abc-border-radius-small);

                    &:hover {
                        background-color: #eff3f6;
                    }

                    &.is-readonly {
                        color: #7a8794;
                        cursor: not-allowed;
                        background-color: initial;
                    }

                    &.is-selected {
                        color: $theme2;
                    }
                }
            }
        }

        .abc-dialog-footer {
            display: flex;
            align-items: center;
            min-height: 52px;
            padding: 4px 16px 4px 4px;

            .dialog-footer {
                width: 100%;
            }

            .selected-acupoint-list {
                display: flex;
                flex: 1;
                flex-wrap: wrap;
                align-content: flex-start;

                li {
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 2px 6px;
                    margin: 4px;
                    font-size: 13px;
                    line-height: 13px;
                    color: $G2;
                    background: #f5fcf8;
                    border: 1px solid #bef4d3;
                    border-radius: 12px;

                    &:hover {
                        .delete-icon i {
                            display: inline-block;
                        }
                    }

                    .delete-icon {
                        position: absolute;
                        top: -8px;
                        right: -8px;
                        display: block;
                        width: 16px;
                        height: 16px;
                        line-height: 16px;
                        text-align: center;

                        i {
                            display: none;
                            width: 16px;
                            height: 16px;
                            font-size: 16px;
                            color: $P1;
                            text-align: right;
                            cursor: pointer;
                            background: url('~assets/images/<EMAIL>') no-repeat center;
                            background-size: contain;
                        }

                        &:hover {
                            i {
                                color: $T3;
                                background: url('~assets/images/<EMAIL>') no-repeat center;
                                background-size: contain;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
