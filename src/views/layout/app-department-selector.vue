<template>
    <app-header-business-selector
        :selected-id="currentDepartmentId"
        :options="currentDepartments"
        @change="handleChange"
    >
        <abc-tag-v2
            slot="append"
            size="mini"
            variant="light"
        >
            停用
        </abc-tag-v2>
    </app-header-business-selector>
</template>
<script type="text/ecmascript-6">
    import { mapState } from 'vuex';
    import AppHeaderBusinessSelector from 'views/layout/app-header-business-selector.vue';
    import { RELATIVE_STATUS } from 'utils/constants';

    export default {
        name: 'AppDepartmentSelector',
        components: { AppHeaderBusinessSelector },
        computed: {
            ...mapState('hospitalGlobal', ['departmentList', 'currentDepartmentId']),
            currentDepartments() {
                return this.departmentList.map((it) => {
                    return {
                        ...it,
                        slot: it.status === RELATIVE_STATUS.DEACTIVE,
                        disable: it.status === RELATIVE_STATUS.DEACTIVE,
                        status: it.status,
                    };
                }).sort((a, b) => a.status - b.status);
            },
        },
        methods: {
            // 切换科室
            async handleChange(department) {
                this.$router.replace({
                    name: this.$route.name,
                    params: {
                        ...this.$route.params,
                        departmentId: department.id,
                    },
                });
            },
        },
    };
</script>
