import { MaritalStatusLabel } from 'views/crm/constants.js';
import {
    OrderStatusLabelEnum,
    PEBusinessTypeLabel, PEOrderTypeLabel, PEPayStatusLabel,
} from 'views/physical-examination/constants';
import { formatDate } from '@abc/utils-date';
const PEPrintLabel = Object.freeze({
    0: '未打印',
    10: '已打印',
});


export const WE_CLINIC_RELEASE_STATUS = Object.freeze({
    RELEASE: 1,
    NOT_RELEASE: 0,
});

export const WE_CLINIC_RELEASE_STATUS_LABEL = Object.freeze({
    [WE_CLINIC_RELEASE_STATUS.RELEASE]: '已发送',
    [WE_CLINIC_RELEASE_STATUS.NOT_RELEASE]: '未发送',
});

export const TEAM_TABLE_LIST = [
    {
        key: 'orderNo',
        label: '订单号',
        style: {
            width: '240px',
            maxWidth: '240px',
            textAlign: 'left',
        },
    }, {
        key: 'orderName',
        label: '订单名称',
        style: {
            flex: 1,
            minWidth: '160px',
            textAlign: 'left',
        },
    },{
        key: 'created',
        label: '创建时间',
        colType: 'time',
        style: {
            width: '240px',
            maxWidth: '240px',
            textAlign: 'left',
        },
    },{
        key: 'examinedCount',
        label: '已检人数',
        style: {
            width: '100px',
            maxWidth: '100px',
            textAlign: 'left',
        },
    }, {
        key: 'salesEmployeeName',
        label: '销售人',
        style: {
            width: '180px',
            maxWidth: '180px',
            textAlign: 'left',
        },
    },{
        key: 'peOrganContactName',
        label: '企业联系人',
        style: {
            width: '240px',
            maxWidth: '240px',
            textAlign: 'left',
        },
    },{
        key: 'payStatus',
        label: '结算状态',
        style: {
            width: '180px',
            maxWidth: '180px',
            textAlign: 'left',
        },
        customRender: (h,row) => {
            const { payStatus } = row;
            const str = PEPayStatusLabel[payStatus] || '';
            return (
                <div class="table-cell">{str}</div>
            );
        },
    },{
        key: 'printStatus',
        label: '打印状态',
        style: {
            width: '180px',
            maxWidth: '180px',
            textAlign: 'left',
        },
        customRender: (h,row) => {
            const { printStatus } = row;
            const str = PEPrintLabel[printStatus] || '';
            return (
                <div class="table-cell">{str}</div>
            );
        },
    },{
        key: 'printTime',
        label: '首次打印时间',
        colType: 'time',
        style: {
            width: '180px',
            maxWidth: '180px',
            textAlign: 'left',
        },
    }];

export const INDIVIDUAL_TABLE_LIST = [
    {
        label: ' ',
        isCheckbox: true,
        style: {
            width: '36px',
            maxWidth: '36px',
            'textAlign': 'left',
        },
    }, {
        key: 'no',
        label: '体检单号',
        'colType': 'text',
        style: {
            width: '120px',
            maxWidth: '120px',
            'textAlign': 'left',
        },
    }, {
        key: 'patient',
        label: '客户姓名',
        'colType': 'text',
        style: {
            width: '140px',
            maxWidth: '140px',
            'textAlign': 'left',
        },
        customRender: (h,row) => {
            const { patient = {} } = row;
            return (
                <div class="table-cell">
                    <span class="ellipsis" style="width: 57px;" title={patient?.name}>{patient?.name}</span>
                    <span style="margin:0 5px">{patient?.sex}</span>
                    {patient?.age.year ? `${patient?.age.year}岁` : ''}
                </div>
            );
        },
    },{
        key: 'marital',
        label: '婚姻状况',
        'colType': 'text',
        style: {
            width: '80px',
            maxWidth: '80px',
            'textAlign': 'left',
        },
        customRender: (h,row) => {
            const { patient = {} } = row;
            const str = MaritalStatusLabel[patient?.marital] || '';
            return (
                <div class="table-cell">{str}</div>
            );
        },
    },{
        key: 'name',
        label: '订单名称',
        'colType': 'text',
        style: {
            flex: 1,
            minWidth: '160px',
            'textAlign': 'left',
        },
    }, {
        key: 'type',
        label: '个检/团检',
        'colType': 'text',
        style: {
            width: '80px',
            maxWidth: '80px',
            'textAlign': 'left',
        },
        customRender: (h,row) => {
            const { type } = row;
            const str = PEOrderTypeLabel[type] || '';
            return (
                <div class="table-cell">{str}</div>
            );
        },
    },{
        key: 'businessType',
        label: '体检类型',
        'colType': 'text',
        style: {
            width: '80px',
            maxWidth: '80px',
            'textAlign': 'right',
        },
        customRender: (h,row) => {
            const { businessType } = row;
            const str = PEBusinessTypeLabel[businessType] || '';
            return (
                <div class="table-cell">{str}</div>
            );
        },
    },{
        key: 'totalFee',
        label: '订单金额',
        style: {
            width: '80px',
            maxWidth: '80px',
            'textAlign': 'right',
        },
        colType: 'money',
    },{
        key: 'salesEmployee',
        label: '销售人',
        'colType': 'text',
        style: {
            width: '80px',
            maxWidth: '80px',
            'textAlign': 'left',
        },
        customRender: (h,row) => {
            const { salesEmployee } = row;
            return (
                <div class="table-cell">{salesEmployee?.name}</div>
            );
        },
    },{
        key: 'chargeStatus',
        label: '结算状态',
        'colType': 'text',
        style: {
            width: '80px',
            maxWidth: '80px',
            'textAlign': 'center',
        },
        customRender: (h,row) => {
            const { chargeStatus } = row;
            const str = PEPayStatusLabel[chargeStatus] || '';
            return (
                <div class="table-cell">{str}</div>
            );
        },
    },{
        key: 'status',
        label: '订单状态',
        'colType': 'text',
        style: {
            width: '80px',
            maxWidth: '80px',
            'textAlign': 'center',
        },
        customRender: (h,row) => {
            const { status } = row;
            const str = OrderStatusLabelEnum[status] || '';
            return (
                <div class="table-cell">{str}</div>
            );
        },
    },{
        key: 'reportApproved',
        label: '总评时间',
        style: {
            width: '180px',
            maxWidth: '180px',
            'textAlign': 'left',
        },
        dataFormatter: (val) => {
            return formatDate(val, 'YYYY-MM-DD HH:mm');
        },
    },{
        key: 'reportReleased',
        label: '发布时间',
        style: {
            width: '180px',
            maxWidth: '180px',
            'textAlign': 'left',
        },
        dataFormatter: (val) => {
            return formatDate(val, 'YYYY-MM-DD HH:mm');
        },
    },
    {
        key: 'weClinicReleaseStatus',
        label: '微信',
        style: {
            width: '80px',
            maxWidth: '80px',
            'textAlign': 'center',
        },
        dataFormatter: (val) => {
            return WE_CLINIC_RELEASE_STATUS_LABEL[val] || '';
        },
    },
    {
        key: 'reportReleasedBy',
        label: '发布人',
        'colType': 'text',
        style: {
            width: '100px',
            maxWidth: '100px',
            'textAlign': 'left',
        },
        customRender: (h,row) => {
            const { reportReleasedBy } = row;
            const str = reportReleasedBy?.name || '';
            return (
                <div class="table-cell">{str}</div>
            );
        },
    },{
        key: 'printStatus',
        label: '打印状态',
        'colType': 'text',
        style: {
            width: '80px',
            maxWidth: '80px',
            'textAlign': 'left',
        },
        customRender: (h,row) => {
            const { printStatus } = row;
            const str = PEPrintLabel[printStatus] || '';
            return (
                <div class="table-cell">{str}</div>
            );
        },
    },{
        key: 'printTime',
        label: '首次打印时间',
        style: {
            width: '180px',
            maxWidth: '180px',
            'paddingRight': '',
        },
        dataFormatter: (val) => {
            return formatDate(val, 'YYYY-MM-DD HH:mm');
        },
    }];


export const ReportListType = Object.freeze({
    1: INDIVIDUAL_TABLE_LIST, //个人
    2: TEAM_TABLE_LIST, // 团体
    3: INDIVIDUAL_TABLE_LIST, //公卫
});


export const ReportTypeEnum = Object.freeze({
    INDIVIDUAL: 1,
    TEAM: 2,
    COMMON_HEALTHY: 3,
});

export const PART_TYPE = {
    cover: 'cover',
    guide: 'guide',
    summary: 'summary',
    ageStage: 'age-stage',
    examStatus: 'exam-status',
    abnormalStatistic: 'abnormal-statistic',
    abnormalDetail: 'abnormal-detail',
    endGuide: 'end-guide',
};

export const PEGroupReportDownloadStatusEnum = {
    atLine: 0, // 队列中
    processing: 10, // 转换中
    created: 20, // 已创建
    downloaded: 30, // 已下载
    failed: 40, // 失败
};
