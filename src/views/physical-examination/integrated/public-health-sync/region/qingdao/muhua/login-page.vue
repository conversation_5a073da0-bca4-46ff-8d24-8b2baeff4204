<script>
    import { defineComponent } from 'vue';
    import {
        UnitBindStatus,
    } from 'views/physical-examination/integrated/public-health-sync/region/huhehaote/login-page/model';

    export default defineComponent({
        name: 'QingdaoMuhuaLoginPage',
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
        },
        created() {
            this.postPublicHealthLogin();
        },

        methods: {
            /**
             * 调用公卫体检系统登录接口
             * @param params
             * @return {Promise<*>}
             */
            async postPublicHealthLogin() {
                // 1. 登录获取用户信息及机构信息
                const response = await window.$abcPcPublicHealth.login();
                if (response.status === false) {
                    console.error('postPublicHealthLogin', response);
                    this.$emit('input', false);
                    return;
                }
                const {
                    accountInfo = {},
                    organizationInfoList = [{}],
                } = response.data;

                const {
                    id: userId,
                    realName: username,
                } = accountInfo;

                const {
                    id: orgCode,
                    name: orgName,
                } = organizationInfoList[0];

                // 2. 通过获取到的信息，校验机构是否绑定
                const {
                    bindStatus,
                    bindInfo,
                } = await this.$abcPage.loginService.checkUnitBindStatus(orgCode);

                if (bindStatus === UnitBindStatus.UN_MATCH) {
                    // 2.1 绑定则检查是否一致，不一致则提示并中断流程
                    await this.showUnMatchInfo(bindInfo);
                    return;
                }

                if (bindStatus === UnitBindStatus.UN_BIND) {
                    // 2.2 未绑定则提示绑定
                    const res = await this.showBindConfirm(orgName);
                    if (!res) {
                        return;
                    }
                    // 发起绑定请求
                    try {
                        await this.$abcPage.loginService.bindUnitInfo({
                            unitId: orgCode,
                            unitName: orgName,
                        });
                    } catch (e) {
                        throw new Error('绑定失败，请重试');
                    }
                }

                // 设置用户信息，关闭弹窗
                this.$abcPage.setUserInfo({
                    id: userId,
                    name: username,
                    // unitId: orgCode,
                });
                this.$emit('input', false);
                return response.data;
            },

            async showUnMatchInfo(bindInfo) {
                await new Promise((resolve) => {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: [
                            '当前登录公卫机构不符合绑定的公卫机构，无法登录',
                            `<div style="color: ${this.$store.state.theme.style.T2}; margin-top: 8px">已绑定公卫机构“${bindInfo.organName}”</div>`,
                        ],
                        onConfirm: () => {
                            resolve();
                        },
                        onCancel: () => {
                            resolve();
                        },
                    });
                });
                this.handleCancelLogin();
            },

            async showBindConfirm(unitName) {
                const res = await new Promise((resolve) => {
                    this.$confirm({
                        type: 'warn',
                        title: '【重要】公卫机构绑定提示',
                        content: [
                            '当前登录公卫机构为：',
                            `“${unitName}”`,
                            '<div style="margin: 24px 0;">请确认绑定？</div>',
                            `<span style="color: ${this.$store.state.theme.style.Y2}">* 绑定后，不可更改</span>`,
                        ],
                        onConfirm: () => {
                            resolve(true);
                        },
                        onCancel: () => {
                            resolve(false);
                        },
                    });
                });
                if (!res) {
                    this.handleCancelLogin();
                }
                return res;
            },

            handleCancelLogin() {
                this.$emit('input', false);
                this.$abcPage.loginService.logout();
            },
        },

        render() {
            return '';
        },
    });
</script>
