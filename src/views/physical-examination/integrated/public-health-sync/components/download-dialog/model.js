import Vue from 'vue';

export const DownloadProgressStatus = Object.freeze({
    WAITING: 0,
    DOWNLOADING: 1,
    INTERRUPTED: 3,
    FINISHED: 4,
    FAILED: 5,
});

/**
 *
 * @return {{pageIndex: number, currentSyncPersonList: {}, currentCount: number, updatedCount: number, totalCount: number, addCount: number, status: number}}
 */
export const useModel = (userInfo) => {
    const state = Vue.observable({
        userInfo,
        // 分页 index
        pageIndex: 0,
        // 分页 size
        pageSize: 20,
        // 当前同步的数量
        currentCount: 0,
        // 总共同步的数量
        totalCount: 0,
        // 本次新增的数量
        addCount: 0,
        // 本次更新的数量
        updatedCount: 0,
        // 当前更新的状态
        status: DownloadProgressStatus.WAITING,
        // 当前正在同步的人员列表信息
        currentSyncPersonList: [],
    });

    const reset = () => {
        state.pageIndex = 0;
        state.currentCount = 0;
        state.totalCount = 0;
        state.addCount = 0;
        state.updatedCount = 0;
        state.status = DownloadProgressStatus.WAITING;
        state.currentSyncPersonList = [];
    };

    const update = (data) => {
        Object.assign(state, data);
    };

    return {
        state,
        reset,
        update,
    };
};
