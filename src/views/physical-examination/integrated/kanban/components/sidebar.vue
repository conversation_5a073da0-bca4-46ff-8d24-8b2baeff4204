<template>
    <abc-container-right>
        <div class="integrated-kanban-sidebar sidebar-container">
            <div class="abc-tabs">
                <div class="abc-tabs-item abc-tabs-item-active">
                    体检队列<template v-if="groupName">
                        ·
                    </template> {{ groupName }}
                </div>
            </div>

            <div v-abc-loading="loading" class="sidebar-content-wrapper">
                <div v-for="item in list" :key="item.id" class="patient-item">
                    <abc-avatar
                        class="img-wrapper"
                        :is-male="item.patientSex === '男'"
                    ></abc-avatar>
                    <div class="name ellipsis">
                        {{ item.patientName }}
                    </div>
                    <div class="age">
                        {{ item.patientAge }}
                    </div>
                    <div class="mobile">
                        {{ item.patientMobile }}
                    </div>
                    <div
                        class="status"
                        :class="{
                            'is-waiting': item.status === 0
                        }"
                    >
                        {{ item.statusName }}
                    </div>
                </div>
            </div>
        </div>
    </abc-container-right>
</template>

<script type="text/ecmascript-6">
    import AbcAvatar from 'src/views/layout/abc-avatar/index.vue';
    import PhysicalExaminationAPI from 'api/physical-examination/pe-order.js';
    import { formatAge } from 'utils';

    export default {
        name: 'IntegratedKanbanSidebar',

        components: {
            AbcAvatar,
        },

        props: {
            groupId: {
                type: String,
                required: true,
            },
            groupName: {
                type: String,
                required: true,
            },
            queryParams: {
                type: Object,
                required: true,
            },
        },

        data() {
            return {
                loading: false,
                list: [],
            };
        },
        watch: {
            groupId(val) {
                this.fetchKanbanDetail(val);
            },
        },

        methods: {
            async fetchKanbanDetail(groupId) {
                if (!groupId) {
                    this.list = [];
                    return;
                }
                this.loading = true;
                const res = await PhysicalExaminationAPI.fetchKanbanDetail(groupId, this.queryParams);
                if (groupId !== this.groupId) return;
                this.list = res.rows || [];
                this.list = this.list.map((item) => {
                    const displayObj = {
                        statusName: item.status === 0 ? '待检' : '已检',
                    };
                    if (item.patient) {
                        displayObj.patientName = item.patient.name || '';
                        displayObj.patientAge = formatAge(item.patient.age) || '';
                        displayObj.patientMobile = item.patient.mobile || '';
                        displayObj.patientSex = item.patient.sex || '';
                    }
                    return {
                        ...item,
                        ...displayObj,
                    };
                });
                this.loading = false;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import "src/styles/theme.scss";
    @import "src/styles/mixin.scss";

    .integrated-kanban-sidebar {
        .patient-item {
            display: flex;
            align-items: center;
            height: 44px;

            .name {
                width: 66px;
            }

            .age {
                width: 38px;
            }

            .status {
                width: 40px;
                margin-left: auto;

                &.is-waiting {
                    color: $B2;
                }
            }

            .age,
            .mobile {
                color: $T2;
            }

            div + div {
                margin-left: 10px;
            }
        }

        .sidebar-content-wrapper {
            padding: 0 0 10px 10px;
        }
    }
</style>
