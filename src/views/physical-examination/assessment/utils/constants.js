import { INSPECT_TYPE } from '@abc/constants';

export const DEVICE_TYPE_NAME = {
    'CT': 1,
    'DR': 2,
    'MR': 9,
    '彩超': 8,
    '心电图': 5,
    '内窥镜': 10,
    '其他': 13,
    '骨密度': 6,
    '一般检查': 14,
    '内科检查': 15,
    '外科检查': 16,
    '耳鼻喉检查': 17,
    '口腔检查': 18,
};

export const inspectTypeOptions = [
    {
        label: '一般检查',
        value: INSPECT_TYPE.NORMAL,
    },
    {
        label: '内科检查',
        value: INSPECT_TYPE.INTERNAL,
    },
    {
        label: '外科检查',
        value: INSPECT_TYPE.SURGERY,
    },
    {
        label: '耳鼻喉检查',
        value: INSPECT_TYPE.ENT,
    },
    {
        label: '口腔检查',
        value: INSPECT_TYPE.MOUTH,
    },
    {
        label: '彩超',
        value: INSPECT_TYPE.CDU,
    },
    {
        label: 'CT',
        value: INSPECT_TYPE.CT,
    },
    {
        label: 'DR',
        value: INSPECT_TYPE.DR,
    },
    {
        label: 'MR',
        value: INSPECT_TYPE.MR,
    },
    {
        label: '内窥镜',
        value: INSPECT_TYPE.GASTROSCOPE,
    },
    {
        label: '骨密度',
        value: INSPECT_TYPE.BONE_DENSITY,
    },
    {
        label: '心电图',
        value: INSPECT_TYPE.EEG,
    },
];

export const hasTipsDeviceType = [14,15,17];

export const SaveAssessmentActionType = Object.freeze({
    SUBMIT: 70, //完成总评
    SAVE: 72, //保存总评
});

export const AbnormalTipTypeEnum = {
    text: 10,
    arrowUp: 20,
    arrowDown: 30,
};
