<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        content-styles="padding: 24px;"
        :title="title"
        custom-class="invoice-number-dialog"
        append-to-body
    >
        <div class="dialog-content clearfix">
            <abc-form
                ref="invoiceForm"
                item-block
                label-position="left"
                :label-width="72"
            >
                <abc-form-item label="发票类型" required>
                    <abc-select
                        v-model="postData.type"
                        :disabled="optType === 'delete'"
                        :width="270"
                    >
                        <abc-option :value="PaperInvoiceType.REGISTRATION" label="挂号发票"></abc-option>
                        <abc-option :value="PaperInvoiceType.OUTPATIENT" label="门诊收费发票"></abc-option>
                        <abc-option :value="PaperInvoiceType.HOSPITAL" label="住院发票"></abc-option>
                        <abc-option :value="PaperInvoiceType.GENERAL" label="通用发票"></abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item label="发票代码" required>
                    <abc-input
                        v-model="postData.code"
                        v-abc-focus-selected
                        :disabled="optType === 'delete'"
                        variant="text"
                        size="small"
                        :width="270"
                        :max-length="16"
                    ></abc-input>
                </abc-form-item>

                <div class="double-form-item">
                    <abc-form-item label="发票票号" required :validate-event="validateNumber">
                        <abc-input
                            v-model="postData.startNumber"
                            v-abc-focus-selected
                            :width="120"
                            :max-length="16"
                        ></abc-input>

                        <div v-if="postData.startNumber && postData.endNumber && numberCount > 0" class="number-tips">
                            {{ optTypeStr }} {{ numberCount }} 份
                        </div>
                    </abc-form-item>
                    <span class="split-form-item">至</span>
                    <abc-form-item required :validate-event="validateInvoiceNumber">
                        <abc-input
                            v-model="postData.endNumber"
                            v-abc-focus-selected
                            :width="120"
                            :max-length="16"
                        ></abc-input>
                    </abc-form-item>
                </div>
            </abc-form>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="saveLoading" @click="confirm">
                确定
            </abc-button>
            <abc-button variant="ghost" @click="no">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import { PaperInvoiceType } from '../constants.js';
    import InvoiceAPI from 'api/invoice/index.js';
    import localStorage from 'utils/localStorage-handler.js';
    export default {
        name: 'InvoiceNumberDialog',
        props: {
            title: String,
            optType: String,
            originData: Object,
            submit: Function,
            cancel: Function,
            defaultCode: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                PaperInvoiceType,
                visible: false,
                saveLoading: false,
                postData: {
                    type: PaperInvoiceType.OUTPATIENT,
                    code: '',
                    startNumber: '',
                    endNumber: '',
                },
                availableNumberList: [],
            };
        },
        computed: {
            numberCount() {
                const {
                    startNumber,
                    endNumber,
                } = this.postData;
                return endNumber - startNumber + 1;
            },
            optTypeStr() {
                if (this.optType === 'add') {
                    return '录入';
                } if (this.optType === 'delete') {
                    return '删除';
                }
                return '';
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },
        async created() {
            if (this.optType === 'add') {
                const {
                    code = '',
                } = this.originData || {};
                this.postData.code = code;
                if (this.defaultCode) {
                    this.postData.code = this.defaultCode;
                }
            } else if (this.optType === 'delete') {
                const {
                    type,
                    code,
                } = this.originData || {};
                this.postData.type = type;
                this.postData.code = code;

                /**
                 * @desc 获取可用的发票号，计算删除的号码
                 * <AUTHOR>
                 * @date 2022-04-28 16:51:53
                 */
                await this.calcDeleteNumbers();

            }
        },
        methods: {
            validateNumber(val, callback) {
                if (!val) {
                    callback({ validate: true });
                    return;
                }
                if (isNaN(val)) {
                    callback({
                        validate: false, message: '请输入数字',
                    });

                } else {
                    callback({ validate: true });
                }
            },
            /**
             * @desc 获取发票可用号码
             * <AUTHOR>
             * @date 2022-04-28 16:56:01
             */
            async queryInvoiceNumberList() {
                const macAddress = localStorage.get('mac_address', true);
                try {
                    const { data } = await InvoiceAPI.queryInvoiceNumberList({
                        managementId: this.originData.id,
                        mac: macAddress,
                    });
                    return data?.rows || [];
                } catch (e) {
                    console.warn(e);
                }
            },
            async calcDeleteNumbers() {
                const numberList = await this.queryInvoiceNumberList();
                this.availableNumberList = numberList;
                // 可用票号可能不存在
                if (!numberList?.length) return;
                // 只有一个票号，删除吱声就好
                if (numberList.length === 1) {
                    this.postData.startNumber = numberList[0].invoiceNumber;
                    this.postData.endNumber = this.postData.startNumber;
                } else {
                    // 先排序， 从小到大
                    numberList.sort((a, b) => {
                        return b.invoiceNumber > a.invoiceNumber;
                    });
                    // 循环至第一个不连续的号码
                    this.postData.startNumber = numberList[0].invoiceNumber;
                    for (let i = 1; i < numberList.length; i++) {
                        // 找到第一个不连续的发票号
                        if (Number(numberList[i].invoiceNumber - numberList[i - 1].invoiceNumber) > 1) {
                            this.postData.endNumber = numberList[i - 1].invoiceNumber;
                            break;
                        }
                        if (i === numberList.length - 1) {
                            this.postData.endNumber = numberList[i].invoiceNumber;
                        }
                    }
                }
            },
            validateInvoiceNumber(value, callback) {
                const { startNumber } = this.postData;
                const endNumber = value;
                if (!value) {
                    callback({ validate: true });
                    return;
                }
                if (isNaN(value)) {
                    callback({
                        validate: false,
                        message: '请输入数字',
                    });
                    return;
                }

                if (!startNumber && !endNumber) {
                    callback({ validate: true });
                    return;
                }
                if (+startNumber > +endNumber) {
                    callback({
                        validate: false,
                        message: '发票起始号码不能大于终止号码',
                    });
                } else {
                    callback({ validate: true });
                }
            },


            async confirm() {
                this.$refs.invoiceForm.validate(async (valid) => {
                    if (valid) {
                        if (typeof this.submit === 'function') {
                            try {
                                this.saveLoading = true;
                                const postData = {
                                    ...this.postData,
                                    availableNumberList: this.availableNumberList,
                                };
                                await this.submit(postData, (data) => {
                                    if (data) {
                                        this.visible = false;
                                    }
                                    this.saveLoading = false;
                                });
                            } catch (e) {
                                this.saveLoading = false;
                            }
                        } else {
                            this.visible = false;
                        }
                    }
                });
            },

            async no() {
                if (typeof this.cancel === 'function') {
                    this.cancel();
                }
                this.visible = false;
            },

            destroyElement() {
                this.$destroy();
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme';

    .invoice-number-dialog {
        width: 390px;

        .split-form-item {
            display: inline-block;
            width: 30px;
            margin-bottom: 24px;
            color: $T2;
            text-align: center;
        }

        .double-form-item {
            display: flex;
            align-items: center;
        }

        .number-tips {
            position: absolute;
            bottom: -24px;
            left: 0;
            font-size: 14px;
            color: $T2;
        }
    }
</style>
