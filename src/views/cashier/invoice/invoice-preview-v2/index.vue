<template>
    <div ref="previewWrapper" class="invoice-preview">
        <div v-if="showRefund" class="cancellation-wrapper">
            <img src="~assets/images/<EMAIL>" alt="" />
        </div>
        <div :class="{ 'is-refund-preview': showRefund }">
            <div
                v-show="previewHTML"
                ref="previewContent"
                class="preview-content"
            >
                <iframe
                    ref="iframe"
                    scrolling="no"
                    :srcdoc="previewHTML"
                    frameborder="0"
                ></iframe>
            </div>
            <abc-flex v-if="isUploadInvoice" ref="previewPDF" justify="center">
                <abc-result
                    v-if="loadError"
                    status="warning"
                    title="pdf加载失败"
                >
                </abc-result>
            </abc-flex>
        </div>
    </div>
</template>

<script>
    import {
        ElectronInvoiceType,
        InvoiceCategory,
        InvoiceBusinessScene,
    } from 'views/cashier/invoice/constants.js';
    import { matchTemplate } from 'views/settings/print-config/medical-bills/bill';
    import AbcPrinter from '@/printer';
    import store from '@/store';
    import { off } from 'utils/dom';
    import PreAbcPrint from '@/printer/index-v2/pre-print-handler';
    import { getPDFPageInfo } from 'views/examination/util';
    import Logger from 'utils/logger';
    import clone from 'utils/clone';

    export default {
        name: 'InvoicePreview',
        props: {
            printData: {
                validator: (prop) => typeof prop === 'object' || prop === null,
                required: true,
            },
            printBillConfig: {
                type: Object,
                required: true,
            },
            invoiceCategory: {
                type: Number,
                require: true,
            },
            isRefundInvoice: {
                type: Boolean,
                default: false,
            },
            paperInvoiceScene: {
                type: String,
                require: true,
            },
            showRefund: Boolean,
            normalInvoice: Object,
            showPaperInvoice: Boolean,
            isUploadInvoice: {
                type: Number,
                default: 0,
            },
            isOpenInvoice: Boolean,
            isOpenMedicalInvoice: Boolean,
            digitalInvoice: Object,
        },
        data() {
            return {
                previewHTML: '',
                loadError: false,
            };
        },
        computed: {
            format() {
                return this.printBillConfig && this.printBillConfig.format || '';
            },

            isPaperInvoice() {
                return this.invoiceCategory === InvoiceCategory.PAPER;
            },
            // 电子发票
            isEInvoice() {
                return this.invoiceCategory === InvoiceCategory.ELECTRONIC;
            },
            isZhejiangRegInvoice() {
                return this.isPaperInvoice && this.paperInvoiceScene === InvoiceBusinessScene.REGISTRATION && this.$abcSocialSecurity.config.isZhejiangHangzhou;
            },
            isGuangdongRegInvoice() {
                return this.isPaperInvoice && this.paperInvoiceScene === InvoiceBusinessScene.REGISTRATION && this.$abcSocialSecurity.config.isGuangdongZhanjiang;
            },
            isTianjinRegInvoice() {
                return this.isPaperInvoice && this.paperInvoiceScene === InvoiceBusinessScene.REGISTRATION && this.$abcSocialSecurity.config.isTianjin;
            },
            // 医疗电子发票
            isMedicalEInvoice() {
                return this.invoiceCategory === ElectronInvoiceType.MEDICAL_ELECTRONIC;
            },
            isHospital() {
                return this.paperInvoiceScene === InvoiceBusinessScene.HOSPITAL_CHARGE;
            },
            supportEInvoice() {
                return this.isOpenInvoice || this.isOpenMedicalInvoice;
            },
            isTaxFullDigitalInvoice() {
                return this.invoiceCategory === InvoiceCategory.TAX_FULLY_DIGITAL;
            },
            // 医院住院发票
            isHisHospitalInvoice() {
                return this.paperInvoiceScene === InvoiceBusinessScene.HIS_HOSPITAL;
            },
        },

        watch: {
            printData: {
                handler() {
                    this.mountPrintInstance();
                },
                deep: true,
                immediate: true,
            },
        },

        async created() {
            await store.dispatch('fetchPrintAllConfigIfNeed');
            AbcPrinter.setGlobalConfig();
            PreAbcPrint.setGlobalConfig();
            this.bindGodModeKeyPress();
        },

        beforeDestroy() {
            off(document, 'keyup', this._keyupEvent);

        },

        methods: {
            bindGodModeKeyPress() {
                this._keyupEvent = (event) => {
                    const key = event.keyCode;
                    const KEY_F = 70;
                    if (key === KEY_F && event.ctrlKey && event.altKey) {
                        this.$emit('devToolsPreviewInvoice');
                    }
                    if (key === 79) {
                        this.printInstance._errorHandler(Error('手动上报模板'));
                    }
                };
                document.addEventListener('keyup', this._keyupEvent);
            },
            async mountPrintInstance(needNotifyRendered = true) {
                try {
                    if (needNotifyRendered) {
                        this.previewHTML = '';
                    }
                    const tempPrintData = clone(this.printData);
                    // 渲染pdf
                    if (this.isUploadInvoice) {
                        if (this.printData.invoiceUrl) {
                            this.clearPDFView();
                            await this.previewPDF(this.printData.invoiceUrl);
                        }
                        return;
                    }
                    if (!this.printData) {
                        return console.error('没有打印预览数据');
                    }
                    let templateName = '';
                    const { format } = this;
                    if (format) {
                        templateName = this.showPaperInvoice ? `medicalBill${format[0].toUpperCase()}${format.slice(1)}` : '';
                    }
                    if ((this.isHospital || this.isHisHospitalInvoice) && format) {
                        templateName = `medicalHospitalBill${format[0].toUpperCase()}${format.slice(1)}`;
                    }

                    if (this.isEInvoice) {
                        templateName = 'eInvoice';
                    }
                    // 医疗电子发票使用 medicalEInvoice + 地区 作为模板匹配
                    if (this.isMedicalEInvoice) {
                        const regionName = this.printData?.invoiceAreaCode;
                        if (regionName) {
                            if (regionName.includes('jiangsu')) {
                                templateName = 'medicalEInvoiceJiangsu';
                            } else {
                                templateName = `medicalEInvoice${regionName[0].toUpperCase()}${regionName.slice(1)}`;
                            }
                        }
                    }
                    if (this.isTaxFullDigitalInvoice) {
                        templateName = 'taxFullDigitalInvoice';
                    }
                    if (this.isZhejiangRegInvoice) {
                        templateName = 'zhejiangInvoice';
                    }
                    if (this.isGuangdongRegInvoice) {
                        templateName = 'guangdongRegInvoice';
                    }
                    if (this.isTianjinRegInvoice) {
                        templateName = 'tianjinRegInvoice';
                    }

                    if (this.needNotifyRendered) {
                        this.$emit('rendering');
                    }

                    if (!templateName) {
                        return console.error('没有匹配到模版，无法挂载AbcPint');
                    }
                    console.log(`正在初始化模板:[${templateName}]`);
                    const printData = {
                        ...tempPrintData,
                        normalInvoice: this.normalInvoice,
                        digitalInvoice: this.digitalInvoice,
                    };
                    const printInstance = new window.AbcPackages.AbcPrint({
                        template: window.AbcPackages.AbcTemplates[templateName],
                        page: {
                            size: '',
                            orientation: window.AbcPackages.AbcPrint.Orientation.portrait,
                        },
                        originData: printData,
                        extra: {
                            $abcSocialSecurity: this.$abcSocialSecurity,
                        },
                        matchTemplateCallback: ({ template: { templates = [] } }) => {
                            return matchTemplate(format, templates, this.printBillConfig);
                        },
                    });
                    await printInstance.init();
                    const {
                        html, height,
                    } = await printInstance.splitPreview1();
                    this.previewHTML = html;

                    await this.$nextTick();
                    if (this.$refs.previewContent) {
                        this.$refs.previewContent.style.height = `${height}px`;
                        this.$refs.previewContent.style.width = printInstance.splitSize.width;
                        const previewContentWidth = parseInt(getComputedStyle(this.$refs.previewContent).width);
                        const previewWrapperWidth = $('.invoice-modal-wrapper .modal-content-wrapper').width();
                        if (previewContentWidth > previewWrapperWidth) {
                            this.$refs.previewContent.style.width = '100%';
                            if (needNotifyRendered) {
                                this.renderPageViewStyle();
                            }
                        }
                    }
                    this.printInstance = printInstance;

                    if (needNotifyRendered) {
                        //    计算票据消耗的张数
                        const pageList = window.AbcPackages.AbcPrint.splitAbcPageHTML(this.previewHTML);
                        this.$emit('getBillPages', pageList.length);

                        this.$emit('rendered');
                    }

                } catch (e) {
                    console.error('AbcPrint挂载失败', e);
                }
            },
            async updatePreview() {
                const {
                    html,
                } = await this.printInstance.splitPreview1();
                this.previewHTML = html;
            },
            renderPageViewStyle() {
                const iframe = this.$refs?.iframe;
                if (iframe) {
                    iframe.onload = () => {
                        const iframeDom = iframe.contentDocument;
                        const $abcPageHtml = iframeDom.querySelector('html');
                        if ($abcPageHtml && $abcPageHtml.style) {
                            $abcPageHtml.style.zoom = '0.77';
                        }
                        const $abcPageView = iframeDom.querySelector('.abc-page_preview');
                        if ($abcPageView) {
                            $abcPageView.style.borderColor = '#e0e2eb';
                            $abcPageView.style.boxShadow = '2px 2px 8px rgba(0, 0, 0, .1)';
                        }
                        if (this.isTaxFullDigitalInvoice) {
                            if ($abcPageView && $abcPageView.style) {
                                $abcPageHtml.style.zoom = '0.92';
                                $abcPageView.style.width = '100%';
                            }
                        }

                    };
                }
            },
            async previewPDF(url) {
                if (!url) return;
                this.loadError = false;
                try {
                    const pageList = await getPDFPageInfo(url, false, {
                        width: '627px',
                        height: 'auto',
                    });
                    this.clearPDFView();
                    const list = pageList.map((page) => page.canvas);
                    this.$refs.previewPDF.$el.appendChild(list[0]);
                } catch (e) {
                    Logger.error({
                        scene: 'invoice-preview-error',
                        error: e,
                    });
                    this.loadError = true;
                }
            },
            clearPDFView() {
                if (!this.$refs.previewPDF?.$el) return;
                const previewPDFContainer = this.$refs.previewPDF.$el;
                while (previewPDFContainer.children.length) {
                    previewPDFContainer.removeChild(previewPDFContainer.firstChild);
                }
            },
            toInvoiceConfig() {
                this.$emit('to-invoice-config');
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.invoice-preview {
    min-height: 100%;

    .preview-content {
        position: relative;
        margin: 0 auto;
        overflow: hidden;
        overflow-x: hidden;
        color: #2a82e4;

        iframe {
            width: inherit;
            height: inherit;
        }
    }

    .is-refund-preview {
        margin-top: -440px;
    }

    .cancellation-wrapper {
        position: sticky;
        top: 0;
        left: 0;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 440px;
        background-color: rgba(255, 255, 255, 0.69);

        img {
            width: 124px;
        }
    }
}
</style>
