<template>
    <div ref="previewWrapper" class="invoice-preview">
        <div
            v-show="previewHTML"
            ref="previewContent"
            class="preview-content"
            :class="{ 'is-big-preview': isBigBill }"
        >
            <div v-if="isRefundInvoice" class="cancellation-wrapper">
                <img src="~assets/images/<EMAIL>" alt="" />
            </div>
            <iframe
                ref="iframe"
                scrolling="no"
                :srcdoc="previewHTML"
                frameborder="0"
            ></iframe>
        </div>
    </div>
</template>

<script>
    import {
        ElectronInvoiceType,
        InvoiceCategory,
        InvoiceBusinessScene,
    } from 'views/cashier/invoice/constants.js';
    import { matchTemplate } from 'views/settings/print-config/medical-bills/bill';
    import AbcPrinter from '@/printer';
    import store from '@/store';
    import { off } from 'utils/dom';
    import PreAbcPrint from '@/printer/index-v2/pre-print-handler';

    export default {
        name: 'InvoicePreview',
        props: {
            printData: {
                validator: (prop) => typeof prop === 'object' || prop === null,
                required: true,
            },
            printBillConfig: {
                type: Object,
                required: true,
            },
            invoiceCategory: {
                type: Number,
                require: true,
            },
            isRefundInvoice: {
                type: Boolean,
                default: false,
            },
            paperInvoiceScene: {
                type: String,
                require: true,
            },
        },
        data() {
            return {
                previewHTML: '',
            };
        },
        computed: {
            format() {
                return this.printBillConfig && this.printBillConfig.format || '';
            },
            isBigBill() {
                return this.format === 'henan';
            },
            isPaperInvoice() {
                return this.invoiceCategory === InvoiceCategory.PAPER;
            },
            // 电子发票
            isEInvoice() {
                return this.invoiceCategory === InvoiceCategory.ELECTRONIC;
            },
            isZhejiangRegInvoice() {
                return this.isPaperInvoice && this.paperInvoiceScene === InvoiceBusinessScene.REGISTRATION && this.$abcSocialSecurity.config.isZhejiangHangzhou;
            },
            isGuangdongRegInvoice() {
                return this.isPaperInvoice && this.paperInvoiceScene === InvoiceBusinessScene.REGISTRATION && this.$abcSocialSecurity.config.isGuangdongZhanjiang;
            },
            isTianjinRegInvoice() {
                return this.isPaperInvoice && this.paperInvoiceScene === InvoiceBusinessScene.REGISTRATION && this.$abcSocialSecurity.config.isTianjin;
            },
            // 医疗电子发票
            isMedicalEInvoice() {
                return this.invoiceCategory === ElectronInvoiceType.MEDICAL_ELECTRONIC;
            },
            isHospital() {
                return this.paperInvoiceScene === InvoiceBusinessScene.HOSPITAL_CHARGE;
            },
        },

        watch: {
            isEInvoice() {
                this.mountPrintInstance();
            },
            isMedicalEInvoice() {
                this.mountPrintInstance();
            },
        },

        async created() {
            await store.dispatch('fetchPrintAllConfigIfNeed');
            AbcPrinter.setGlobalConfig();
            PreAbcPrint.setGlobalConfig();
            await this.mountPrintInstance();
            this.bindGodModeKeyPress();
        },

        beforeDestroy() {
            off(document, 'keyup', this._keyupEvent);

        },

        methods: {
            bindGodModeKeyPress() {
                this._keyupEvent = (event) => {
                    const key = event.keyCode;
                    const KEY_F = 70;
                    const KEY_Q = 81;
                    if (key === KEY_F && event.ctrlKey) {
                        this.$emit('devToolsPreviewInvoice');
                    }
                    if (key === KEY_Q && event.ctrlKey) {
                        this.$emit('devToolsPreviewFeeList');
                    }
                    if (key === 79) {
                        this.printInstance._errorHandler(Error('手动上报模板'));
                    }
                };
                document.addEventListener('keyup', this._keyupEvent);
            },
            async mountPrintInstance() {
                try {
                    const { format } = this;
                    if (!format) {
                        return console.error('没有format，无法挂载AbcPint');
                    }
                    if (!this.printData) {
                        return console.error('没有打印预览数据');
                    }
                    let templateName = `medicalBill${format[0].toUpperCase()}${format.slice(1)}`;
                    if (this.isEInvoice) {
                        templateName = 'eInvoice';
                    }
                    // 医疗电子发票使用 medicalEInvoice + 地区 作为模板匹配
                    if (this.isMedicalEInvoice) {
                        const regionName = this.printData?.invoiceAreaCode;
                        if (regionName) {
                            templateName = `medicalEInvoice${regionName[0].toUpperCase()}${regionName.slice(1)}`;
                        }
                    }
                    if (this.isZhejiangRegInvoice) {
                        templateName = 'zhejiangInvoice';
                    }
                    if (this.isGuangdongRegInvoice) {
                        templateName = 'guangdongRegInvoice';
                    }
                    if (this.isTianjinRegInvoice) {
                        templateName = 'tianjinRegInvoice';
                    }
                    if (this.isHospital) {
                        templateName = `medicalHospitalBill${format[0].toUpperCase()}${format.slice(1)}`;
                    }
                    console.log(`正在初始化模板:[${templateName}]`);
                    const printInstance = new window.AbcPackages.AbcPrint({
                        template: window.AbcPackages.AbcTemplates[templateName],
                        page: {
                            size: '',
                            orientation: window.AbcPackages.AbcPrint.Orientation.portrait,
                        },
                        originData: this.printData,
                        extra: {
                            $abcSocialSecurity: this.$abcSocialSecurity,
                        },
                        matchTemplateCallback: ({ template: { templates = [] } }) => {
                            return matchTemplate(format, templates, this.printBillConfig);
                        },
                    });
                    await printInstance.init();
                    const {
                        html, height,
                    } = await printInstance.splitPreview1();
                    this.previewHTML = html;
                    this.$refs.previewContent.style.height = `${height}px`;
                    this.$refs.previewContent.style.width = printInstance.splitSize.width;
                    const previewContentWidth = parseInt(getComputedStyle(this.$refs.previewContent).width);
                    const previewWrapperWidth = parseInt(getComputedStyle(this.$refs.previewWrapper).width);
                    if (previewContentWidth > previewWrapperWidth) {
                        this.$refs.previewContent.style.width = `${previewWrapperWidth}px`;
                    }
                    this.printInstance = printInstance;

                    //    计算票据消耗的张数
                    const pageList = window.AbcPackages.AbcPrint.splitAbcPageHTML(this.previewHTML);
                    this.$emit('getBillPages', pageList.length);

                } catch (e) {
                    console.error('AbcPrint挂载失败', e);
                }
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.invoice-preview {
    .preview-content {
        position: relative;
        margin: 0 auto;
        overflow: hidden;
        color: #2a82e4;

        iframe {
            width: inherit;
            height: inherit;
        }

        &.is-big-preview {
            transform: scale(0.8947);
            transform-origin: left;
        }
    }

    .preview-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .cancellation-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.69);

            img {
                width: 124px;
            }
        }
    }

    .invoice-template {
        position: relative;
    }
}
</style>
