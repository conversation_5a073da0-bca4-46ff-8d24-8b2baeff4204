import AbcAudioQueue from 'views/common/auto-play/audio-queue.js';
import AbcPlayer from 'views/common/auto-play/player.js';
import { ChargeSheetTypeEnum } from '@/service/charge/constants.js';
import store from 'store/index.js';

export default class ChargeAutoPlaySocketListening {
    static TAG = 'ChargeAutoPlaySocketListening';

    constructor(socket) {
        this._socket = socket;
        this.audioQueue = AbcAudioQueue.getInstance();
        this._handleMessage = this.handleMessage.bind(this);
    }

    handleMessage(socketData) {
        const autoPlayVoice = store.getters['cashierSettings/autoPlayVoice'];

        const isOutpatient = socketData?.type === ChargeSheetTypeEnum.OUTPATIENT;
        const isContinueSheet = socketData?.type === ChargeSheetTypeEnum.CLONE_PRESCRIPTION;
        const isOnlineSheet = socketData?.type === ChargeSheetTypeEnum.OUTPATIENT && socketData?.isOnline;

        // 新门诊单
        if (autoPlayVoice.onlineCharge && isOnlineSheet) { // 网诊
            const player = new AbcPlayer(
                '//static-common-cdn.abcyun.cn/media/online-charge.mp3',
                'online-sheet');
            this.audioQueue.add(player);
        } else if (autoPlayVoice.outpatientCharge && isOutpatient) {
            const player = new AbcPlayer(
                '//static-common-cdn.abcyun.cn/media/outpatient-sheet-charge.mp3',
                'outpatient-sheet',
            );
            this.audioQueue.add(player);
        }
        // 续方
        if (autoPlayVoice.continueSheetCharge && isContinueSheet) {
            const player = new AbcPlayer(
                '//static-common-cdn.abcyun.cn/media/continue-sheet-charge.mp3',
                'continue-sheet');
            this.audioQueue.add(player);
        }
    }

    start() {
        console.log('收费单语音播报开始监听');
        store.dispatch('cashierSettings/initCashierSettings');
        this._registrationSocketListener();
    }

    stop() {
        console.log('收费单语音播报解除监听');
        this._unregisterSocketListener();
    }

    _registrationSocketListener() {
        this._socket.on('clone.prescription.msg', this._handleMessage);
    }

    _unregisterSocketListener() {
        this._socket.off('clone.prescription.msg', this._handleMessage);
    }
}
