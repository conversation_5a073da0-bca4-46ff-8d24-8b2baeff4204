<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        :title="title"
        content-styles="width: 700px;max-height: 468px"
        custom-class="charge-notice-dialog"
        data-cy="charge-notice-dialog"
    >
        <div v-if="headTips" class="head-tips">
            {{ headTips }}
        </div>
        <abc-table :render-config="_tableConfig" :data-list="data">
            <!--药品-->
            <template #cadn="{ trData: item }">
                <abc-table-cell>
                    <span
                        v-if="isStockGoods(item)"
                        v-abc-goods-hover-popper="{
                            goods: item,
                            showF1: item.productType !== GoodsTypeEnum.MATERIAL && item.productType !== GoodsTypeEnum.GOODS,
                        }"
                        class="ellipsis"
                    >
                        {{ item | formatGoodsName }}
                    </span>
                    <span v-else class="ellipsis">
                        {{ item.productType === GoodsTypeEnum.REGISTRATION ? $t('registrationFeeName') : item.name }}
                    </span>
                    <compose-tag v-if="item.productType === GoodsTypeEnum.COMPOSE"></compose-tag>
                    <span v-if="item.parentName" class="compose-name" style=" font-size: 12px; color: #7a8794;">
                        【{{ item.parentName }}】
                    </span>
                </abc-table-cell>
            </template>

            <!--收费数量-->
            <template #chargeCount="{ trData: item }">
                <abc-table-cell>
                    <template v-if="item.checked || item.parentName">
                        {{ item.unitCount * (item.doseCount || 1) }}{{ item.unit }}
                    </template>
                    <template v-else>
                        未勾选
                    </template>
                </abc-table-cell>
            </template>

            <!--当前库存-->
            <template #stockCount="{ trData: item }">
                <abc-table-cell>
                    <span v-if="isStockGoods(item)" :style="isShortage(item).flag ? 'color: #FF9933' : ''">
                        {{ currentStockStr(item) }}
                    </span>
                    <span v-else> -- </span>
                </abc-table-cell>
            </template>

            <template #tips="{ trData: item }">
                <abc-table-cell>
                    <span
                        :style="item.checked || item.parentName ? 'color: #FF9933' : 'color: #FF3366'"
                        style="padding-left: 28px;"
                    >
                        {{ item.checked || item.parentName ? '库存不足' : '将退单不再收费' }}
                    </span>
                </abc-table-cell>
            </template>
        </abc-table>

        <div slot="footer" class="dialog-footer">
            <abc-button v-if="showConfirm" data-cy="charge-notice-dialog-confirm" @click="ok">
                {{ confirmText }}
            </abc-button>
            <abc-button variant="ghost" data-cy="charge-notice-dialog-cancel" @click="no">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { complexCount } from 'src/filters/goods';
    import { isShortage } from 'utils/validate';
    import { GoodsTypeEnum } from '@abc/constants';
    import ComposeTag from 'src/views/outpatient/common/compose-tag';

    export default {
        name: 'ChargeNoticeDialog',
        components: {
            ComposeTag,
        },
        props: {
            data: Array,
            confirm: Function,
        },
        data() {
            return {
                title: '收费提醒',
                GoodsTypeEnum,
                visible: false,
                showConfirm: true,
                confirmText: '继续收费',
                headTips: '',
                complexCount,
            };
        },
        created() {
            this._tableConfig = {
                list: [
                    {
                        label: '收费项目',
                        key: 'cadn',
                        ellipsisHide: true,
                        style: {
                            width: '130px',
                            display: 'flex',
                            lineHeight: 1,
                        },
                    },
                    {
                        label: '收费数量',
                        key: 'chargeCount',
                        style: {
                            width: '40px',
                            textAlign: 'right',
                        },
                    },
                    {
                        label: '当前库存',
                        key: 'stockCount',
                        style: {
                            textAlign: 'right',
                            width: '60px',
                        },

                    },
                    {
                        label: '',
                        key: 'tips',
                        style: {
                            width: '80px',
                        },
                    },
                ],
            };
        },
        methods: {
            isShortage,
            isStockGoods(item) {
                const type = item.productType || item.type;
                return (
                    type === GoodsTypeEnum.MEDICINE || type === GoodsTypeEnum.MATERIAL || type === GoodsTypeEnum.GOODS
                );
            },
            currentStockStr(goods) {
                let type = goods.productType;
                let subType = goods.productSubType;
                let {
                    packageUnit = '',
                    pieceUnit = '',
                } = goods;
                let packageCount = goods.stockPackageCount || 0;
                let pieceCount = goods.stockPieceCount || 0;

                const { productInfo } = goods;
                if (productInfo) {
                    type = productInfo.type;
                    subType = productInfo.subType;
                    packageCount = productInfo.stockPackageCount || packageCount;
                    pieceCount = productInfo.stockPieceCount || pieceCount;
                    packageUnit = productInfo.packageUnit || '';
                    pieceUnit = productInfo.pieceUnit || '';
                }

                return complexCount({
                    type,
                    subType,
                    packageCount,
                    pieceCount,
                    packageUnit,
                    pieceUnit,
                });
            },

            ok() {
                if (typeof this.confirm === 'function') {
                    this.confirm();
                }
                this.no();
            },

            no() {
                this.visible = false;
                this.destroyElement();
            },

            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss" rel="stylesheet/scss">
    @import 'src/styles/theme.scss';

    .charge-notice-dialog {
        .head-tips {
            height: 32px;
            color: $Y2;
        }
    }
</style>
