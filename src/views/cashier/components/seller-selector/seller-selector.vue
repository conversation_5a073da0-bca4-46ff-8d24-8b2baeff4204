<template>
    <abc-autocomplete
        v-model="sellerName"
        v-abc-focus-selected
        class="seller-selector"
        placeholder="开单人"
        :width="120"
        :focus-show="true"
        :fetch-suggestions="employeesFilter"
        :filter-suggestions="employeesFilter"
        @enter="
            (event) => {
                $emit('enter', event);
            }
        "
        @input="handleInput"
        @blur="handleBlur"
        @enterEvent="selectEmployee"
    >
        <template slot="suggestions" slot-scope="props">
            <dt
                class="suggestions-item"
                :class="{ selected: props.index == props.currentIndex }"
                @mousedown="selectEmployee(props.suggestion)"
            >
                {{ props.suggestion.name }}
            </dt>
        </template>

        <div slot="append" class="search-icon" @click="onClickClear">
            <i v-if="sellerName" class="iconfont cis-icon-clean"></i>
        </div>
    </abc-autocomplete>
</template>

<script>
    import ClinicAPI from 'api/clinic';

    export default {
        props: {
            value: [String, Number],
        },
        data() {
            return {
                employees: [],
                sellerName: '',
            };
        },
        watch: {
            value: {
                handler (val) {
                    this.findEmployee(val);
                },
            },
        },
        async created() {
            try {
                const { data } = await ClinicAPI.getClinicEmployee(true);
                this.employees = data.rows;
                this.findEmployee(this.value);
            } catch (e) {
                console.error(e);
            }
        },
        methods: {
            /**
             * @desc 当失焦时，如果不是被选择出来的，直接清空
             * <AUTHOR>
             * @date 2020/03/11 16:54:02
             */
            handleBlur() {
                if (!this.sellerId && this.sellerName) {
                    const seller = this.employees.find((item) => {
                        return item.name === this.sellerName;
                    });
                    if (seller) {
                        this.$emit('input', seller.id);
                    } else {
                        this.sellerName = '';
                        this.$emit('input', null);
                    }
                }
            },
            handleInput() {
                this.sellerId = null;
            },

            employeesFilter(queryString, cb) {
                if (!queryString) {
                    cb(this.employees);
                    return false;
                }

                cb((this.employees || []).filter(this.createFilter(queryString)));
            },
            createFilter(queryString) {
                return (item) => {
                    return (
                        item.name.indexOf(queryString) > -1 ||
                        item.namePy.indexOf(queryString.toLowerCase()) > -1 ||
                        item.namePyFirst.toLowerCase().indexOf(queryString.toLowerCase()) > -1
                    );
                };
            },
            selectEmployee(employee) {
                this.sellerId = employee.id;
                const seller = this.employees.find((item) => {
                    return item.id === employee.id;
                });
                this.sellerName = seller ? seller.name : '';
                this.$emit('change', {
                    id: this.sellerId,
                    name: this.sellerName,
                });
            },
            /**
             * desc [点击清除销售人员]
             */
            onClickClear() {
                this.sellerId = '';
                this.sellerName = '';
                this.$emit('change', {
                    id: this.sellerId,
                    name: this.sellerName,
                });
            },

            /**
             * @desc 根据传进来的sellerId在employee列表中找，找不到就清空（该用户被移除）
             * <AUTHOR>
             * @date 2019/11/15 14:44:28
             */
            findEmployee(val) {
                const seller = this.employees.find((item) => {
                    return item.id === val;
                });
                if (seller) {
                    this.sellerName = seller.name || '';
                } else {
                    this.sellerName = '';
                    val && this.$emit('input', null);
                }
            },
        },
    };
</script>
