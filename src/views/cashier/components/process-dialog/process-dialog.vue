<template>
    <abc-dialog
        v-show="showDialog"
        v-model="showDialog"
        title="设置加工信息"
        data-cy="process-setting-dialog"
        custom-class="process-dialog-wrapper"
        size="medium"
        content-styles="max-height: 574px;min-height: 350px"
    >
        <abc-form ref="processForm" v-abc-loading="loading" style="height: 100%;">
            <div
                v-for="(item, index) in currentProcessInfos"
                :key="index"
                class="process-item"
                :data-cy="`process-item-${index + 1}`"
            >
                <div class="process-switch" style="margin-bottom: 8px;">
                    <abc-checkbox
                        v-model="item.checked"
                        :disabled="disabled"
                        style="margin-bottom: 8px;"
                        data-cy="process-item-checkbox"
                    >
                        中药处方{{ translateH(index + 1) }}
                    </abc-checkbox>
                </div>

                <div class="process-detail">
                    <div class="detail-item">
                        <label>处方内容</label>
                        <p class="rule-tips">
                            {{ formatPrescriptionInfo(item) }}
                        </p>
                    </div>
                </div>

                <div class="process-detail">
                    <div class="detail-item">
                        <label>加工方式</label>
                        <process-select
                            v-if="usageOptions.length"
                            :required="item.checked"
                            :usage-type.sync="item.type"
                            :disabled="disabled || !item.checked"
                            :width="208"
                            :inner-width="140"
                            :usage-sub-type.sync="item.subType"
                            @change="_calcFee"
                        ></process-select>
                        <div v-else class="no-available-process-usages">
                            <p class="price-rule-tips price-error">
                                未设置加工费规则
                            </p>
                        </div>
                    </div>
                </div>

                <div v-if="item.type === 1" class="process-detail">
                    <div class="detail-item">
                        <label>加工袋数</label>
                        <span>1剂煎</span>
                        <abc-form-item :required="item.checked">
                            <abc-input
                                v-model="item.processBagUnitCount"
                                v-abc-focus-selected
                                :disabled="disabled || !item.checked"
                                data-cy="process-unit-count-input"
                                type="number"
                                :config="{
                                    max: 1000,
                                    formatLength: 1,
                                }"
                                :input-custom-style="{
                                    'text-align': 'center', margin: '0 10px'
                                }"
                                :width="84"
                                @change="val => handleCountChange(val, item, 'processBagUnitCount')"
                            ></abc-input>
                        </abc-form-item>
                        <span>袋，共煎</span>
                        <abc-form-item :required="item.checked">
                            <abc-input
                                v-model="item.totalProcessCount"
                                v-abc-focus-selected
                                :disabled="disabled || !item.checked"
                                data-cy="process-total-count-input"
                                type="number"
                                :config="{
                                    max: 1000,
                                }"
                                :input-custom-style="{
                                    'text-align': 'center', margin: '0 10px'
                                }"
                                :width="84"
                                @change="val => handleCountChange(val, item, 'totalProcessCount')"
                            ></abc-input>
                        </abc-form-item>
                        <span style="margin-left: 4px;">袋</span>
                    </div>
                </div>

                <div v-if="usageOptions.length" class="process-detail">
                    <div class="detail-item">
                        <label>加工费用</label>
                        <div>
                            <p class="price" data-cy="process-price" style="height: 32px;">
                                <abc-money :value="item.processFee" :is-show-space="true"></abc-money>
                            </p>
                            <p v-if="item.isMarkRule" class="price-rule-tips">
                                {{ item.ruleInfo }}
                            </p>
                            <p v-else class="price-rule-tips price-error">
                                未匹配到加工费规则
                            </p>
                        </div>
                    </div>
                </div>

                <div v-if="dispensingConfig.isTakeMedicationTime" class="process-detail">
                    <div class="detail-item">
                        <label>取药时间</label>
                        <div>
                            <abc-date-time-picker
                                v-model="item.takeMedicationTime"
                                is-compact
                                :hour-start="0"
                                :disabled="disabled || !item.checked"
                                data-cy="process-take-medication-date-time-picker"
                                auto-open-time-picker
                                adaptive-width
                                @change="handleChangeTakeMedicationTime"
                            >
                            </abc-date-time-picker>
                        </div>
                    </div>
                </div>

                <div class="process-detail">
                    <div class="detail-item">
                        <label>备注</label>
                        <abc-form-item>
                            <abc-input
                                v-model="item.processRemark"
                                adaptive-width
                                :disabled="disabled || !item.checked"
                                :max-length="100"
                                data-cy="process-remark"
                                placeholder="备注"
                            ></abc-input>
                        </abc-form-item>
                    </div>
                </div>
            </div>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <template v-if="disabled">
                <abc-button variant="ghost" @click="cancel">
                    关闭
                </abc-button>
            </template>
            <template v-else>
                <abc-button :loading="loading" data-cy="process-dialog-confirm" @click="confirm">
                    确定
                </abc-button>
                <abc-button variant="ghost" data-cy="process-dialog-cancel" @click="cancel">
                    取消
                </abc-button>
            </template>
        </div>
    </abc-dialog>
</template>
<script>
    import { numToChinese } from 'utils/num-to-chinese';
    import ProcessSelect from 'src/views/cashier/components/process-dialog/process-select';

    import ChargeAPI from 'api/charge';
    import { SourceFormTypeEnum } from '@/service/charge/constants.js';
    import { createGUID } from 'utils/index';
    import { debounce } from 'utils/lodash';
    import Storage from 'utils/localStorage-handler.js';

    import { mapGetters } from 'vuex';
    import { GoodsTypeEnum } from '@abc/constants';
    import { LAST_TAKE_MEDICINE_TIME } from 'utils/local-storage-key';
    import { parseTime } from '@/filters';

    export default {
        name: 'ProcessDialog',
        components: {
            ProcessSelect,
        },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            chargeForms: {
                type: Array,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            contactMobile: {
                validator: (prop) => typeof prop === 'string' || prop === null || prop === undefined,
                required: true,
            },
            isReplay: {
                type: Boolean,
                default: false,
            },
            status: [Number, String],
            // 计算加工袋数
            calcBagsNumber: {
                type: Function,
                default() {
                    () => function () {};
                },
            },
        },
        data() {
            return {
                currentProcessInfos: [],
                currentContactMobile: this.contactMobile,
                loading: false,
            };
        },
        computed: {
            ...mapGetters(['availableProcessUsages', 'currentClinic', 'dispensingConfig']),
            usageOptions() {
                return this.availableProcessUsages || [];
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            /**
             * @desc 中药forms
             * <AUTHOR>
             * @date 2020-05-20 15:48:15
             */
            chineseForms() {
                return this.chargeForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                });
            },
            decoctionForms() {
                return this.chargeForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.DECOCTION;
                });
            },
        },
        async created() {
            this._calcFee = debounce(this.calcProcessFee, 250, true);
            await this.initChineseForm();
            this.initProcessInfos();
            this.calcProcessFee();
        },
        methods: {
            handleCountChange(val, item, type) {
                if (!val) return;
                const chineseForm = this.findChineseForm(item) || {};
                const { doseCount } = chineseForm;
                if (!doseCount) return;
                this.$nextTick(() => {
                    if (type === 'processBagUnitCount') {
                        item.totalProcessCount = Math.ceil(val * doseCount);
                    } else {
                        item.processBagUnitCount = String(val / doseCount).length > 3 ?
                            (val / doseCount).toFixed(1) : val / doseCount;
                    }
                    this._calcFee();
                });
            },
            initChineseForm() {
                const promises = [];
                this.chineseForms.forEach((form) => {
                    const {
                        usageInfo,
                        specification,
                        pharmacyType,
                        doseCount,
                    } = form;
                    const promise = this.calcBagsNumber({
                        ...usageInfo,
                        doseCount,
                        specification,
                        pharmacyType,
                    }).then((res) => {
                        const {
                            processBagUnitCount,
                            totalProcessCount,
                        } = res || {};
                        this.$set(form, 'processBagUnitCount', processBagUnitCount);
                        this.$set(form, 'totalProcessCount', totalProcessCount);
                    });
                    promises.push(promise);
                });
                return Promise.all(promises);
            },
            initProcessInfos() {
                const lastTakeMedicineTimeObj = Storage.getObj(LAST_TAKE_MEDICINE_TIME, this.currentClinic.userId, true) || null;
                let lastTakeMedicineTime = '';
                if (lastTakeMedicineTimeObj) {
                    const {
                        takeMedicationTime, lastModifiedDate,
                    } = lastTakeMedicineTimeObj;
                    // 每个用户按天清空，只读取每天的配置，
                    // 增加 lastModifiedDate 上次修改时间，判断是否为当天的配置
                    const todayDate = parseTime(new Date(), 'y-m-d', true);
                    if (todayDate === lastModifiedDate) {
                        lastTakeMedicineTime = takeMedicationTime;
                    }
                }
                let lastTakeMedicineDate = '';
                let lastTakeMedicineDateTime = '';
                if (this.dispensingConfig.isTakeMedicationTime && lastTakeMedicineTime && !this.disabled) {
                    // 填充默认的取药时间
                    lastTakeMedicineDate = parseTime(new Date(), 'y-m-d');
                    lastTakeMedicineDateTime = `${lastTakeMedicineDate} ${lastTakeMedicineTime}`;
                }
                this.currentProcessInfos = [];
                this.chineseForms.forEach((form, cIndex) => {
                    const _processItem = {
                        keyId: createGUID(),
                        chargeFormId: form.id || form.keyId,
                        checked: this.decoctionForms.length === 0,
                        type: '',
                        subType: '',
                        processBagUnitCount: form.processBagUnitCount,
                        totalProcessCount: form.totalProcessCount,
                        processRemark: '',
                        processFee: null,
                        ruleInfo: null,
                        specification: form.specification,
                        takeMedicationTime: form.processInfo?.takeMedicationTime || lastTakeMedicineDateTime,
                    };
                    // 已有的加工费进行赋值
                    this.decoctionForms.forEach((dForm) => {
                        if (
                            (dForm.processInfo && dForm.processInfo.chargeFormId === form.id) ||
                            (dForm.processInfo && dForm.processInfo.chargeFormId === form.keyId) ||
                            (!dForm.processInfo.chargeFormId && cIndex === 0)
                        ) {
                            delete dForm.processInfo.keyId;
                            Object.assign(_processItem, dForm.processInfo, {
                                checked: true,
                                chargeFormId: form.id || form.keyId,
                            });
                        }
                    });
                    this.currentProcessInfos.push(_processItem);
                });
                // 没有处方补一个空的加工费值
                if (this.currentProcessInfos.length === 0) {
                    if (this.decoctionForms.length) {
                        this.decoctionForms.forEach((dForm) => {
                            if (dForm.processInfo) {
                                this.currentProcessInfos.push({
                                    ...dForm.processInfo,
                                    takeMedicationTime: dForm.processInfo?.takeMedicationTime || lastTakeMedicineDateTime,
                                });
                            }
                        });
                    } else {
                        this.currentProcessInfos.push({
                            keyId: createGUID(),
                            checked: true,
                            type: '',
                            subType: '',
                            processBagUnitCount: '',
                            totalProcessCount: '',
                            processRemark: '',
                            processFee: null,
                            ruleInfo: null,
                            takeMedicationTime: lastTakeMedicineDateTime,
                        });
                    }
                }
                if (!this.disabled) {
                    this.currentProcessInfos.forEach((item) => {
                        if (item.type) {
                            this.validateProcessInfo(item);
                        } else {
                            const _usageTypeInfo = this.usageOptions[0];
                            item.type = _usageTypeInfo ? _usageTypeInfo.type : '';
                            if (_usageTypeInfo && _usageTypeInfo.children && _usageTypeInfo.children.length) {
                                item.subType = _usageTypeInfo.children[0].subType;
                            }
                        }
                    });
                }
            },

            /**
             * @desc 管理中删除配置的加工费，需要清空相关type subtype
             * <AUTHOR> Yang
             * @date 2020-12-07 11:50:31
             */
            validateProcessInfo(item) {
                const usageTypeInfo = this.usageOptions.find((option) => {
                    return option.type === item.type;
                });
                if (usageTypeInfo) {
                    if (usageTypeInfo.children && usageTypeInfo.children.length) {
                        const _subType = usageTypeInfo.children.find((child) => {
                            return item.subType && child.subType === item.subType;
                        });
                        if (!_subType) {
                            item.type = '';
                            item.subType = '';
                        }
                    }
                } else {
                    item.type = '';
                    item.subType = '';
                }
            },

            translateH(index) {
                if (this.chineseForms && this.chineseForms.length <= 1) return '';
                return numToChinese(index);
            },
            formatPrescriptionInfo(item) {
                const chineseForm = this.findChineseForm(item);
                if (chineseForm) {
                    return `共${chineseForm.doseCount}剂，一剂${this.doseTotal(chineseForm.chargeFormItems).kind}味 / ${
                        this.doseTotal(chineseForm.chargeFormItems).count
                    }g`;
                }
                return '未添加药品';

            },
            /**
             * @desc 处方剂数 味数
             * <AUTHOR>
             * @date 2020-06-01 18:37:44
             */
            doseTotal(chargeFormItems) {
                let count = 0;
                const kinds = [];
                chargeFormItems.forEach((item) => {
                    const isCharged = item.status && item.status !== 3;
                    if (item.checked || isCharged) {
                        if (item.unit === 'g') {
                            count += item.unitCount * 10000 || 0;
                        }
                        if (kinds.indexOf(item.productId) === -1) {
                            kinds.push(item.productId);
                        }
                    }
                });
                return {
                    kind: kinds.length,
                    count: count / 10000,
                };
            },
            /**
             * @desc 门诊过来的处方都勾选了加工费，收费单勾选上加工费
             * 直接收费默认选中加工费，不能修改选中态
             * <AUTHOR>
             * @date 2020-05-15 17:47:15
             */
            confirm() {
                this.$refs.processForm.validate((val) => {
                    if (val) {
                        this.currentProcessInfos.forEach((item) => {
                            // 不是煎药，processBagUnitCount
                            if (item.type !== 1) {
                                item.processBagUnitCount = '';
                                item.totalProcessCount = '';
                            }
                        });
                        this.$emit('confirm', this.currentProcessInfos);
                        this.showDialog = false;
                    }
                });
            },

            cancel() {
                this.showDialog = false;
            },
            findChineseForm(processItem) {
                let chineseForm = this.chineseForms.find((form) => {
                    return form.id === processItem.chargeFormId ||
                        form.keyId === processItem.chargeFormId;
                });
                if (!chineseForm && this.chineseForms.length) {
                    chineseForm = this.chineseForms[0];
                }
                return chineseForm;
            },
            initProcessChargeForms() {
                const chargeForms = [];
                this.chargeForms.forEach((form) => {
                    const chargeFormItems = form.chargeFormItems.filter((item) => {
                        return item.checked;
                    });
                    // 过滤掉原本的加工费form
                    if (form.sourceFormType !== SourceFormTypeEnum.DECOCTION && chargeFormItems.length) {
                        const tempForm = {
                            keyId: form.keyId,
                            id: form.id,
                            registration: form.registration,
                            sourceFormType: form.sourceFormType,
                            chargeFormItems: chargeFormItems.map((item) => {
                                return {
                                    keyId: item.keyId,
                                    id: item.id,
                                    unit: item.unit,
                                    name: item.name,
                                    unitCount: item.unitCount,
                                    doseCount: item.doseCount || 1,
                                    unitPrice: item.unitPrice,
                                    productId: item.productId,
                                    productType: item.productType,
                                    productSubType: item.productSubType,
                                    sourceUnitPrice: item.sourceUnitPrice,
                                    sourceTotalPrice: item.sourceTotalPrice,
                                    expectedUnitPrice: item.expectedUnitPrice,
                                    expectedTotalPrice: item.expectedTotalPrice,
                                    useDismounting: item.useDismounting,
                                };
                            }),
                        };
                        chargeForms.push(tempForm);
                    }
                });

                // 构造 process 算费需要的chargeForm结构
                this.currentProcessInfos.forEach((processInfo) => {
                    const formObj = {
                        sourceFormType: SourceFormTypeEnum.DECOCTION,
                        keyId: createGUID(),
                        chargeFormItems: [
                            {
                                checked: true,
                                keyId: createGUID(),
                                name: '加工费',
                                productType: GoodsTypeEnum.DECOCTION,
                            },
                        ],
                        processInfo,
                    };
                    chargeForms.push(formObj);
                });

                return chargeForms;
            },
            /**
             * @desc 计算加工费
             * <AUTHOR>
             * @date 2020-06-01 20:31:01
             */
            async calcProcessFee() {
                if (this.disabled) return false;
                this.loading = true;
                const postData = {
                    chargeSheetId: this.status >= 0 ? this.$route.params.id : '',
                    isRenew: +this.isReplay,
                    chargeForms: this.initProcessChargeForms(),
                };
                // console.log(val);
                try {
                    const { data } = await ChargeAPI.calcProcessFee(postData);
                    if (data.processInfos) {
                        data.processInfos.forEach((processItem) => {
                            this.currentProcessInfos.forEach((item) => {
                                if (item.keyId === processItem.keyId) {
                                    item.processFee = processItem.processFee || 0;
                                    item.ruleInfo = processItem.ruleInfo;
                                    item.isMarkRule = processItem.isMarkRule;
                                    item.name = processItem.name;
                                }
                            });
                        });
                    }
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },
            /**
             * @description: 记忆用户上次设置的取药时间
             * @author: ff
             * @date: 2024/7/24
             */
            handleChangeTakeMedicationTime(val) {
                const lastTime = val ? parseTime(val, 'h:i', true) : '';
                if (this.currentClinic.userId && this.dispensingConfig.isTakeMedicationTime) {
                    const lastModifiedDate = parseTime(new Date(), 'y-m-d', true);
                    Storage.setObj(LAST_TAKE_MEDICINE_TIME, this.currentClinic.userId, {
                        takeMedicationTime: lastTime,
                        lastModifiedDate,
                    });
                }
            },
        },
    };
</script>
<style lang="scss" rel="stylesheet/scss">
    @import 'src/styles/theme.scss';

    .process-dialog-wrapper {
        .process-item {
            & + .process-item {
                margin-top: 24px;
            }

            .process-switch {
                border-bottom: 1px dashed var(--abc-color-P6);

                .abc-checkbox__label {
                    font-weight: 500;
                    color: var(--abc-color-T1);
                }

                .process-title {
                    margin-bottom: 8px;
                    font-weight: 500;
                }
            }

            .process-detail {
                display: flex;
                align-items: center;
                justify-content: start;

                & + .process-detail {
                    margin-top: 16px;
                }
            }

            .detail-item {
                display: flex;
                width: 100%;

                .abc-form-item {
                    flex: 1;
                    margin: 0;
                }

                > label {
                    display: inline-flex;
                    align-items: center;
                    align-self: flex-start;
                    width: 92px;
                    min-width: 92px;
                    height: 32px;
                    color: var(--abc-color-T2);
                }

                > span {
                    display: inline-flex;
                    align-items: center;
                    align-self: flex-start;
                    height: 32px;
                }

                .price {
                    display: flex;
                    align-items: center;
                    font-size: 16px;
                    font-weight: 500;
                }
            }

            .rule-tips {
                display: flex;
                align-items: center;
                line-height: 20px;
                color: var(--abc-color-T1);
            }

            .no-available-process-usages {
                display: flex;
                align-items: center;

                > p {
                    font-size: 14px;
                }
            }

            .price-rule-tips {
                font-size: 12px;
                color: var(--abc-color-T2);
            }

            .price-error {
                color: var(--abc-color-Y2);
            }
        }

        .split-line {
            height: 0;
            margin-bottom: 24px;
            border-top: 1px dashed var(--abc-color-P6);
        }
    }
</style>
