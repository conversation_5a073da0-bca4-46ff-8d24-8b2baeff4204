<template>
    <div
        class="points-discount-tr discount-tr"
        :class="{
            'is-disabled': disabledOperation,
            'is-simple': isSimple,
        }"
        @click="handleClick"
    >
        <abc-tag-v2
            variant="outline"
            theme="danger"
            shape="round"
            size="mini"
            style="margin-right: 10px;"
        >
            积分
        </abc-tag-v2>

        <div class="discount-info">
            <div class="discount-info-item">
                <div v-if="patientPointsInfo.checked" class="promotion-name">
                    <span>使用{{
                        patientPointsInfo.checkedDeductionPrice * patientPointsInfo.pointsDeductionRat
                    }}积分抵扣</span>
                </div>
                <template v-else>
                    <div v-if="disabledOperation" class="discount-info-item">
                        无
                    </div>
                    <abc-button v-else variant="text" size="small">
                        {{ patientPointsInfo.deductionDescription }}
                    </abc-button>
                </template>
            </div>
        </div>

        <div class="discount-total-price">
            <template v-if="patientPointsInfo.checked">
                -<abc-money :value="patientPointsInfo.checkedDeductionPrice"></abc-money>
            </template>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    export default {
        name: 'TrPointsDiscount',
        props: {
            patientPointsInfo: {
                type: Object,
            },
            disabledOperation: {
                type: Boolean,
                default: false,
            },
            isSimple: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {

            };
        },
        computed: {
        },

        methods: {
            handleClick() {
                if (this.disabledOperation) return;
                this.$emit('show-select-discount', 'point');
            },
        },
    };
</script>

