<template>
    <div v-abc-loading="loading" class="frequently-medicines-wrapper">
        <div v-if="suggestions.length" class="list-wrapper">
            <ul>
                <li v-for="m in suggestions" :key="m.goodsId">
                    <div class="add-medicine" @click="addMedicine(m)">
                        <i class="iconfont cis-icon-xinzeng"></i>
                    </div>

                    <div class="medicine-info">
                        <p>
                            <span class="name">
                                {{ m.medicineCadn }}
                            </span>
                            <span class="stock">
                                {{ stockInfo(m) }}
                            </span>
                        </p>
                        <p class="detail">
                            <span>{{ m.spec }}</span>
                            <span><abc-money :value="m.packagePrice"></abc-money></span>
                            <span>{{ m.manufacturer || '' }}</span>
                        </p>
                    </div>
                </li>
            </ul>
        </div>
        <div v-if="!suggestions.length" class="no-suggestion">
            暂无常用药品推荐
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import GoodsApi from 'api/goods/index';

    export default {
        name: 'FrequentlyMedicines',
        props: {
            loading: {
                type: Boolean,
                default: false,
            },
            suggestions: {
                type: Array,
            },
        },
        data() {
            return {
                success: false,
            };
        },
        methods: {
            stockInfo(m) {
                const {
                    packageCount,
                    pieceCount,
                    packageUnit,
                    pieceUnit,
                } = m;
                let str = '';
                if (packageCount && packageUnit) {
                    str += `${packageCount}${packageUnit}`;
                }
                if (pieceCount && pieceUnit) {
                    str += `${pieceCount}${pieceUnit}`;
                }
                return str;
            },

            /**
             * @desc 加药
             * <AUTHOR>
             * @date 2019/03/19 21:08:45
             */
            async addMedicine(medicine) {
                try {
                    const { data } = await GoodsApi.fetchGoods(medicine.goodsId);
                    this.$parent.$emit('addItem', Object.assign(data, {
                        packageCount: medicine.stockPackageCount,
                        pieceCount: medicine.stockPieceCount,
                    }));
                } catch (err) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: `查询 ${medicine.medicineCadn} 信息失败，请稍后再试`,
                    });
                }
            },
        },
    };
</script>


