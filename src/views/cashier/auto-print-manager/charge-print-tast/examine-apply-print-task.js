import PrintTask from 'views/pharmacy/pharmacy-auto/pharmacy-print-task/print-task.js';

import PrintAPI from 'api/print.js';

import AbcPrinter from '@/printer/index.js';
import { ABCPrintConfigKeyMap } from '@/printer/constants.js';
import { getViewDistributeConfig } from '@/views-distribute/utils.js';
import Logger from 'utils/logger';

export class ExamineApplyPrintTask extends PrintTask {
    async prepareData(params) {
        // api请求
        const {
            chargeSheetId,
        } = params;

        const { data } = await PrintAPI.printExamApplySheetByChargeId(chargeSheetId, {
            type: 2,
        });

        Logger.report({
            scene: 'auto_print_charge',
            data: {
                scene: 'examine_apply_data',
                uuid: this.uuid,
                info: '检查申请单数据',
                data: {
                    printData: data,
                },
            },
        });

        return {
            ...data,
            title: '检查申请单',
        };
    }

    printHandler(data) {
        if (!data) {
            Logger.report({
                scene: 'auto_print_charge',
                data: {
                    scene: 'examine_apply_data_is_empty',
                    uuid: this.uuid,
                    info: '检查申请单数据为空',
                    data: {
                        printData: data,
                    },
                },
            });
            return;
        }
        AbcPrinter.abcPrint({
            templateKey: window.AbcPackages.AbcTemplates.examinationApplySheet,
            printConfigKey: ABCPrintConfigKeyMap.examinationInspectApplySheet,
            data,
            isAutoPrint: true,
        }, this.uuid);
    }

    getTaskName() {
        return 'ExamineInspectApplyPrintTask';
    }

    static isEnable(config) {
        return config.includes('examination-inspect-apply-sheet');
    }

    static isPrintAble(printable) {
        return printable && printable.examinationInspection;
    }

    static getLabel() {
        return getViewDistributeConfig().Print.printOptions.EXAMINATION_APPLY_SHEET.label;
    }
}
