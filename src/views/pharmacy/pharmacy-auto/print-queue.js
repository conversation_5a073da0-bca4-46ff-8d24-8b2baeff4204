/**
 * @desc 打印队列
 * <AUTHOR>
 * @date 2021-07-14 11:37:17
 * @params
 * @return
 */
export default class PrintQueue {
    constructor() {
        this.printQueue = [];
    }

    get length() {
        return this.printQueue.length;
    }

    add(printTask) {
        this.printQueue.push(printTask);
    }

    shift() {
        return this.printQueue.shift();
    }

    clear() {
        this.printQueue = [];
    }
}
