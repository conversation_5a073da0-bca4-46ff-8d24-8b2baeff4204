<template>
    <span class="_abc-cascader-wrapper" @click="triggerOptionWrapper" v-abc-click-outside="outside">
        <input class="abc-input__inner" :style="inputStyles" ref="abcinput" :placeholder="placeholder" v-model="text" :readonly="true" />
        <span>
            <CascaderOption :childrens="options" v-show="showPopper" ref="cascaderOption" @change="changeEvent">
            </CascaderOption>
        </span>
        <i class="iconfont cis-icon-dropdown_triangle" v-if="!text"></i>
        <i class="iconfont cis-icon-clean" v-else @click.stop="$emit('text' , '');showPopper = false;"></i>
    </span>
</template>

<script>
    import CascaderOption from './cascader-option'
    import CascaderItem from './cascader-item'
    import Popper from 'utils/vue-popper';
    export default {
        name: 'AbcCacader',
        components: {
            CascaderOption,
            CascaderItem
        },
        mixins: [ Popper ],
        props: {
            direct: {
                type: Boolean,
                default: false
            },
            options: {
                type: Array,
                default: function () {
                    return  [{
                                label: '西药',
                                value: '西药'
                            },{
                                label: '中药',
                                value: '中药',
                                childrens: [{
                                    label: '全部中药',
                                    value: '全部中药'
                                }, {
                                    label: '中药饮片',
                                    value: '中药饮片',
                                }, {
                                    label: '中药颗粒',
                                    value: '中药颗粒',
                                }]
                            },{
                                label: '中成药',
                                value: '中成药'
                            }]
                }
            },
            width: {
                type: Number,
                default: 104
            },
            placeholder: {},
            text: {}
        },
        computed: {
            inputStyles() {
                return {
                    width: this.width + 'px'
                }
            }
        },
        methods: {
            triggerOptionWrapper() {
                this.showPopper = (this.showPopper ? false : true);
            },
            outside() {
                this.showPopper = false;
            },
            changeEvent(srcEvent) {
                if(!(this.direct || !srcEvent.childrens))return;

                this.showPopper = false;
                this.$emit('text' , srcEvent.value);
                this.$emit('change' , srcEvent);
            }
        },
        data() {
            return {
                showPopper: false
            }
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.cascaderOption.$el;
            this.referenceElm = this.$refs.abcinput;
        },
    }
</script>

<style lang="scss">
@import "src/styles/theme.scss";
    ._cascader-option-item {
        height: 40px;
        line-height: 40px;
        width: 104px;
        font-size: 14px;
        text-align: left;
        position: relative;
        list-style: none;
        color: #2E3439;
        background-color: #FFF;
        cursor: pointer;
        padding: 0 6px 0 10px;
        border-bottom: 1px solid $P3;
        &:hover {
            background-color: $theme4;
        }
        &:last-child {
            border-radius: 0 0 2px 2px;
            border-bottom: none;
        }
        i {
            float: right;
            font-size: 14px;
            vertical-align: top;
            line-height: 40px;
            color: $T3;
        }
        span {
            vertical-align: top;
            display: inline-block;
            line-height: 40px;
        }
        & > ._abc-cascader-option-wrapper {
            display: none;
        }
        &:hover > ._abc-cascader-option-wrapper {
            display: block;
        }
    }
    ._abc-cascader-option-wrapper{
        position: absolute;
        top: -1px;
        right: -104px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
        width: 104px;
        height: auto;
        border-radius: 0 0 2px 2px;
        z-index: 9999;
    }
    ._abc-cascader-wrapper {
        position: relative;
        display: inline-block;
        vertical-align: top;
        &:hover .cis-icon-guanbi{
            display: block;
            cursor: pointer;
        }
        .is-readonly .abc-input__inner {
            cursor: pointer;
        }
        .iconfont {
            position: absolute;
            top: 50%;
            margin-top: -7px;
            right: 6px;
            color: $T3;
            font-size: 14px;
            cursor: pointer;
        }
        input {
            font-size: 14px;
        }
    }
</style>