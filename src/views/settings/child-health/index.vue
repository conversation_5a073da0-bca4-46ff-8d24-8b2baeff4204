<template>
    <introduce :access-key="accessMap.CHILD_HEALTH">
        <biz-fill-remain-height class="child-health-setting">
            <template #header>
                <abc-manage-tabs :option="tabsOption" @change="handleTabsChange"></abc-manage-tabs>
            </template>
            <router-view></router-view>
        </biz-fill-remain-height>
    </introduce>
</template>

<script type="text/ecmascript-6">
    import Introduce from 'views/edition/introduce/index.vue';
    import AbcAccess from '@/access/utils.js';
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';

    const tabsOption = [
        {
            label: '服务配置', value: 'child-health-switch',
        },
        {
            label: '服务介绍', value: 'child-health-introduce',
        },
    ];
    export default {
        components: {
            BizFillRemainHeight,
            Introduce,
            AbcManageTabs,
        },
        data() {
            return {
                accessMap: AbcAccess.accessMap,
                tabsOption,
            };
        },
        methods: {
            handleTabsChange(index, item) {
                this.$router.push({
                    name: item.value,
                });
            },
        },
    };
</script>
