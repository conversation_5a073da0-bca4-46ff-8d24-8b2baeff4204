<template>
    <biz-fill-remain-height>
        <template #header>
            <abc-manage-tabs :option="tabsOption" disable-indicator @change="handleTabsChange"></abc-manage-tabs>
        </template>
        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script type="text/ecmascript-6">
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';

    const tabsOption = [
        {
            label: '企微管家', value: 'scrm-desc',
        },
    ];
    export default {
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
        data() {
            return {
                tabsOption,
            };
        },
        methods: {
            handleTabsChange(value) {
                this.$router.push({
                    name: value,
                });
            },
        },
    };
</script>
