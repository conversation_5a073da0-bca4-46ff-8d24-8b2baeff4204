@import "src/styles/theme.scss";
@import 'src/styles/mixin';

.registered-fee-setting {
    .prepend-input {
        z-index: 3;
    }

    .clinic-fee {
        display: flex;
        padding-bottom: 24px;
        border-bottom: 1px solid $P6;

        > div {
            span {
                color: $T2;
            }
        }
    }

    .registered-fee-table {
        .warn-text {
            color: var(--abc-color-Y1);
        }
    }

    .table-price {
        display: flex;
        align-items: center;

        &.warn-status {
            color: $Y2;
        }

        .warn-status {
            color: $Y2;
        }

        .match-code-status {
            @include ellipsis;
        }
    }

    .cutline {
        width: 312px;
        height: 1px;
        margin: 24px auto;
        background-color: $P3;
    }
}
