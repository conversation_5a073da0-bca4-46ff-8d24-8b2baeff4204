<template>
    <abc-layout preset="setting-table" class="examinations-table-wrapper">
        <abc-layout-header>
            <examination-second-tab style="margin-bottom: 16px;"></examination-second-tab>

            <abc-flex justify="space-between">
                <abc-space>
                    <abc-search
                        :width="200"
                        :search="fetchParams.keyword"
                        :placeholder="keywordPlaceholder"
                        @input="v => fetchParams.keyword = v"
                        @search="inputHandler"
                        @clear="clearInput"
                    ></abc-search>

                    <!--仪器筛选-->
                    <abc-select
                        v-model="fetchParams.bizRelevantId"
                        :width="120"
                        :fetch-suggestions="handleSearch"
                        with-search
                        placeholder="全部仪器"
                        clearable
                        @change="fetchGoodsList(true)"
                    >
                        <abc-option
                            v-for="item in deviceListOptions"
                            :key="item.id"
                            :label="item.deviceUuid"
                            :value="item.id"
                        ></abc-option>
                    </abc-select>

                    <abc-select
                        v-model="fetchParams.deviceType"
                        placeholder="检验分类"
                        :width="200"
                        adaptive-width
                        clearable
                        @change="fetchGoodsList(true)"
                    >
                        <abc-option
                            v-for="(category,idx) in itemCategoryList"
                            :key="idx"
                            v-bind="category"
                        ></abc-option>
                    </abc-select>

                    <abc-select
                        v-model="fetchParams.customTypeId"
                        placeholder="二级分类"
                        :width="120"
                        clearable
                        @change="fetchGoodsList(true)"
                    >
                        <abc-option
                            v-for="(it,idx) in customTypeList"
                            :key="idx"
                            :label="it.name"
                            :value="it.id"
                        ></abc-option>
                    </abc-select>

                    <!--是否对外开出-->
                    <abc-checkbox
                        v-if="!isCombineGoods"
                        v-model="fetchParamsIsSell"
                        type="number"
                        @change="fetchGoodsList(true)"
                    >
                        包含不可对外销售项目
                    </abc-checkbox>
                </abc-space>

                <abc-space>
                    <abc-button
                        v-if="isAdmin"
                        theme="success"
                        icon="s-b-add-line-medium"
                        @click="clickEvent({
                            id: null,addItem: 'isNewAdd'
                        })"
                    >
                        新建{{ projectName }}
                    </abc-button>
                    <abc-button
                        v-if="showGuanBiaoBtn"
                        variant="ghost"
                        @click="showGuanbiao = true"
                    >
                        贯标统计
                    </abc-button>

                    <abc-check-access>
                        <abc-button
                            variant="ghost"
                            icon="n-upload-line"
                            @click="exportExcel"
                        >
                            导出
                        </abc-button>
                    </abc-check-access>
                </abc-space>
            </abc-flex>
        </abc-layout-header>

        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                type="pro"
                class="diagnosis-treatment-table-view"
                :render-config="tableConfig"
                :data-list="panelData.rows"
                :loading="loading"
                empty-content="暂无检验项"
                :custom-tr-class="handleCustomTrClass"
                :custom-td-class="handleCustomTdClass"
                @handleClickTr="clickEvent"
                @sortChange="changeSortHandler"
            >
                <template #medicalCode="{ trData }">
                    <abc-table-cell>
                        <medical-code :goods="trData"></medical-code>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>

        <abc-layout-footer>
            <abc-pagination
                :count="totalCount"
                :pagination-params="paginationParams"
                :show-total-page="false"
                @current-change="changePageIndex"
            >
                <ul v-if="totalCount" slot="tipsContent" class="total-page">
                    <li>共 <span>{{ totalCount }}</span> 条数据</li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>

        <form-dialog
            v-if="showDialog"
            :id="currentId"
            v-model="showDialog"
            :is-template-clinic="isTemplateClinic"
            :delete-loading="deleteLoading"
            :default-goods="currentGoods"
            :combine-type="combineType"
            :device-list="dialogDeviceList"
            @delete="clickDeleteEvent"
            @refresh="
                (type) => {
                    fetchGoodsList(type === 'create');
                }
            "
            @changeCustomTypes="$emit('changeCustomTypes')"
        ></form-dialog>

        <guanbiao-dialog
            v-if="showGuanbiao"
            v-model="showGuanbiao"
            :is-stock-goods="false"
        ></guanbiao-dialog>
    </abc-layout>
</template>

<script>
    import SettingAPI from 'api/settings';
    import {
        mapGetters, mapState,
    } from 'vuex';
    import { debounce } from 'utils/lodash';
    import {
        formatMoney, parseTime,
    } from '@/filters';
    import { medicalFeeGrade2Str } from '@/filters';
    import MedicalCode from 'views/settings/diagnosis-treatment/components/medical-code.vue';
    import {
        checkBeforeDeleteGoods,
    } from 'src/views/layout/goods-pre-operate-check';
    import AbcSearch from 'components/abc-search/index.vue';
    import ExaminationSecondTab
        from 'views/settings/diagnosis-treatment/examinations/components/examination-second-tab/index.vue';
    import { useExaminationStore } from 'views/settings/diagnosis-treatment/examinations/hooks/useExaminationStore';

    export default {
        name: 'Examinations',
        components: {
            ExaminationSecondTab,
            AbcSearch,
            MedicalCode,
            FormDialog: () => import('@/views/settings/diagnosis-treatment/examinations/form-dialog.vue'),
            GuanbiaoDialog: () => import('views/inventory/components/guanbiao-dialog/index.vue'),
        },

        props: {
            combineType: {
                type: Number,
                required: true,
            },
            subType: {
                type: Number,
                required: true,
            },
            deviceList: {
                type: Array,
                default() {
                    return [];
                },
            },
            isTemplateClinic: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                shareState,
            } = useExaminationStore();

            return {
                shareState,
            };
        },

        data() {
            return {
                loading: false,
                showDialog: false,
                switchLoading: false,
                deleteLoading: false,
                totalCount: 0,
                sortConfig: {
                    orderBy: '',
                    orderType: '',
                },
                fetchParams: {
                    offset: 0,
                    limit: 10,
                    keyword: '',
                    type: 3,
                    subType: '', // 1: 检验  2: 检查
                    bizRelevantId: undefined, // 仪器设备型号Id
                    // 医保等级
                    medicalFeeGrade: [],// 1.甲类 2.乙类 3.丙类 100.其他
                    isSell: undefined,
                    combineType: '',
                    deviceModelId: '', // 仪器id
                    sbNationalMatched: 0, // 已对码
                    sbNationalNotMatched: 0, // 未对码
                    customTypeId: '', // 二级分类
                    deviceType: '', // 项目分类
                },

                panelData: {
                    rows: [],
                },

                currentGoods: {},
                currentId: null,

                showGuanbiao: false,
                clickType: '',
                isSellOptions: [{
                    label: '允许对外开出',
                    value: 1,
                },{
                    label: '所有项目',
                    value: undefined,
                }],
                searchKey: '', // 仪器列表下拉搜索关键字
            };
        },

        computed: {
            ...mapGetters([
                'currentClinic',
                'clinicConfig',
                'isAdmin',
                'treatmentConfig',
                'isChainAdmin',
            ]),

            ...mapState('socialPc', [
                'warningDictExpired',
                'warningPriceExceed',
                'warningSwitch',
                'warningShebaoNotMatched',
            ]),

            ...mapGetters('viewDistribute', ['viewDistributeConfig']),

            showMedicalCodeColum() {
                return this.viewDistributeConfig.Settings.showMedicalCodeColum;
            },

            projectName() {
                return this.viewDistributeConfig.Settings.projectSetting.name;
            },

            showGuanBiaoBtn() {
                return this.viewDistributeConfig.Settings.showGuanBiaoBtn;
            },
            customTypeList() {
                return this.shareState.customTypeList;
            },
            keywordPlaceholder() {
                return this.combineType ? `搜索${this.projectName}名称、编码` : `搜索${this.projectName}名称、代码`;
            },
            searchDeviceList() {
                return this.deviceList;
            },
            deviceListOptions() {
                if (!this.searchKey) {
                    return this.searchDeviceList;
                }
                return this.searchDeviceList.filter((option) => option.deviceUuid.includes(this.searchKey));
            },
            dialogDeviceList() {
                // 新建单项项目非模板门店只有未知仪器
                if (!this.isTemplateClinic && this.clickType === 'isNewAdd' && this.combineType === 0) {
                    return this.deviceList.filter((item) => item.innerFlag);
                }
                return this.deviceList;
            },
            itemCategoryList() {
                return this.treatmentConfig.itemCategory || [];
            },
            paginationParams() {
                const {
                    limit: pageSize, offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },
            tableConfig() {
                let arr = [
                    {
                        key: 'name',
                        label: `${this.projectName}名称`,
                        style: {
                            flex: 2,
                            width: '200px',
                            minWidth: '200px',
                            align: 'left',
                        },
                        sortable: true,
                        customRender: (h, row) => {
                            const disabled = !!row.disable;
                            return (
                                <abc-table-cell class="ellipsis" title={row.name}>
                                    {
                                        row.mutualRecognitionTag ? (
                                            <abc-icon
                                                icon={
                                                    row.mutualRecognitionTag === '1' ? 's-baotouinspection-line' : 's-headcalltest-fill'
                                                }
                                                style="margin-right: 2px"
                                                size={16}
                                            ></abc-icon>
                                        ) : null
                                    }

                                    <abc-text theme={ disabled ? 'gray-light' : 'primary'}>
                                        {row.name}
                                    </abc-text>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'enName',
                        label: '项目代码',
                        style: {
                            width: '120px',
                            minWidth: '120px',
                        },
                        hidden: this.combineType !== 0,
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    <span title={row.enName}>{row.enName}</span>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'bizRelevantId',
                        label: '检验分类',
                        style: {
                            width: '100px',
                            minWidth: '100px',
                        },
                        customRender: (h, row) => {
                            const itemCategory = row.bizExtensions?.itemCategory;
                            const categoryName = this.itemCategoryList.find((item) => item.value === +itemCategory)?.label;

                            return (
                                <abc-table-cell>
                                    <span title={categoryName}>{categoryName}</span>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'customTypeName',
                        label: '二级分类',
                        style: {
                            width: '100px',
                            minWidth: '100px',
                        },
                    },

                    {
                        key: 'deviceInfos',
                        label: '检验仪器',
                        style: {
                            width: '140px',
                            minWidth: '140px',
                        },
                        customRender: (h, row) => {
                            const deviceModelStr = (row.deviceInfos || []).filter((d) => !d.innerFlag).map((d) => d.deviceUuid).join(' ');
                            return (
                                <abc-table-cell>
                                    <span title={deviceModelStr}>{deviceModelStr}</span>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'bizExtensions',
                        label: '样本类型',
                        style: {
                            width: '90px',
                            minWidth: '90px',
                        },
                        customRender: (h, row) => {
                            const { bizExtensions } = row;
                            const { sampleType = '' } = bizExtensions || {};
                            return (
                                <abc-table-cell>
                                    <span className={{ 'is-disabled': row.disable }} title={sampleType}>
                                        {sampleType}
                                    </span>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'sampleName',
                        label: '采样组',
                        style: {
                            width: '100px',
                            minWidth: '100px',
                        },
                        customRender: (h, row) => {
                            const { bizExtensions } = row;
                            const {
                                name = '',
                            } = bizExtensions?.examinationSamplePipe || {};

                            return (
                                <abc-table-cell>
                                    <span title={name}>{name}</span>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'extend',
                        label: '检验机构',
                        style: {
                            width: '120px',
                            minWidth: '120px',
                        },
                        hidden: this.isChainAdmin,
                        customRender: (h, row) => {
                            const { examinationInstitution = {} } = row;

                            return (
                                <abc-table-cell>
                                    <span title={examinationInstitution?.name}>{examinationInstitution?.name}</span>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'executeDepartment',
                        label: '执行科室',
                        style: {
                            width: '120px',
                            minWidth: '120px',
                        },
                        hidden: this.isChainAdmin,
                        customRender: (h, row) => {
                            if (!row.isSell) {
                                return '';
                            }
                            const { executeDepartment } = row;
                            return (
                                <abc-table-cell>
                                    <span title={executeDepartment?.name}>{executeDepartment?.name}</span>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'packagePrice',
                        label: '销售价',
                        sortable: true,
                        style: {
                            width: '100px',
                            flex: 'none',
                            textAlign: 'right',
                        },
                        customRender: (h, row) => {
                            const price = typeof row.packagePrice === 'number' ? formatMoney(row.packagePrice) : '未定价';
                            return (
                                <abc-table-cell>
                                    <span title={price}>{price}</span>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'packageUnit',
                        label: '单位',
                        style: {
                            width: '100px',
                            minWidth: '100px',
                            textAlign: 'center',
                        },
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    <abc-text>
                                        { row.packageUnit }
                                    </abc-text>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        key: 'packageCostPrice',
                        label: '成本价',
                        style: {
                            width: '100px',
                            flex: 'none',
                            textAlign: 'right',
                            paddingRight: '16px',
                        },
                        sortable: true,
                        customRender: (h, row) => {
                            const price = formatMoney(row.packageCostPrice || 0);
                            return (
                                <abc-table-cell>
                                    <span title={price}>{price}</span>
                                </abc-table-cell>
                            );
                        },
                    },

                    {
                        label: '医保对码',
                        key: 'medicalCode',
                        style: {
                            width: '100px',
                            minWidth: '100px',
                        },
                        hidden: !this.showMedicalCodeColum,
                    },

                    {
                        key: 'status',
                        label: '状态',
                        style: {
                            width: '80px',
                            textAlign: 'center',
                            flex: 'none',
                        },
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                        {row.disable === 0 ? '已启用' : '已停用'}
                                </abc-table-cell>
                            );
                        },
                    },
                ];

                arr = arr.filter((item) => !item.hidden);
                return {
                    list: arr,
                };
            },

            fetchParamsIsSell: {
                get() {
                    return +!this.fetchParams.isSell;
                },
                set(v) {
                    this.fetchParams.isSell = +!v;
                },
            },

            isCombineGoods() {
                return this.combineType === 1;
            },
        },

        created() {
            this._debounceSearch = debounce(
                () => {
                    this.loading = false;
                    this.fetchGoodsList();
                },
                250,
                true,
            );
            this.fetchParams.subType = this.subType;
            this.fetchParams.combineType = this.combineType;
        },
        mounted() {
            const {
                goodsId = null,
            } = this.$route.query;
            goodsId && this.clickEvent({
                id: goodsId,addItem: null,
            });
        },
        methods: {
            formatMoney,
            parseTime,
            medicalFeeGrade2Str,
            handleMounted({ paginationLimit }) {
                this.fetchParams.limit = paginationLimit;
                this.fetchGoodsList();
            },
            clickEvent(item) {
                this.currentId = item.id;
                this.currentGoods = item;
                this.showDialog = true;
                this.clickType = item.addItem;
            },
            inputHandler() {
                this.fetchParams.offset = 0;
                this._debounceSearch();
            },
            handleSearch(key) {
                this.searchKey = key;
            },

            clearInput() {
                this.fetchParams.keyword = '';
                this.inputHandler();
            },
            /**
             * @desc  删除检验项
             * <AUTHOR>
             * @date 2019/04/11 16:50:59
             */
            async deleteExaminationGoods(id) {
                try {
                    await SettingAPI.examination.deleteExaminationGoods(id);
                    this.showDialog = false;
                    this.deleteLoading = false;

                    if (this.panelData.rows.length === 1 && this.fetchParams.offset > 0) {
                        this.fetchParams.offset -= this.fetchParams.limit;
                    }

                    this.fetchGoodsList();
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                } catch (e) {
                    console.error(e);
                    this.deleteLoading = false;
                }
            },
            async clickDeleteEvent(id, name) {
                const canDelete = await checkBeforeDeleteGoods(id, name, this);
                if (!canDelete) {
                    return;
                }
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不可恢复，但不影响已开出医嘱。是否删除 ？',
                    onConfirm: () => {
                        this.deleteExaminationGoods(id);
                    },
                });
            },


            async fetchGoodsList(initOffset = false) {
                if (initOffset) {
                    this.fetchParams.offset = 0;
                }
                const {
                    keyword, offset, isSell,
                } = this.fetchParams;
                try {
                    this.loading = true;
                    const { data } = await SettingAPI.goods.fetchGoodsList(
                        Object.assign(this.fetchParams, { isSell: isSell ? +isSell : undefined }),
                    );
                    if (keyword === this.fetchParams.keyword && offset === this.fetchParams.offset) {
                        this.panelData = data;
                        this.panelData.rows = this.panelData.rows || [];
                        this.totalCount = data.total;
                    }
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },

            async exportExcel() {
                await SettingAPI.goods.exportGoodsList(this.fetchParams);
            },

            async changePageIndex(index) {
                this.fetchParams.offset = (index - 1) * this.fetchParams.limit;
                await this.fetchGoodsList();
            },

            async sortChange({
                orderBy, orderType,
            }) {
                this.fetchParams.orderBy = orderBy;
                this.fetchParams.orderType = orderType;
                this.fetchParams.offset = 0;
                await this.fetchGoodsList();
            },

            changeSortHandler(val) {
                Object.assign(this.fetchParams, val);
                this.fetchGoodsList(true);
            },
            handleCustomTrClass(item) {
                const classArr = [];
                if (item.disable) {
                    classArr.push('is-disabled');
                }
                return classArr.join(' ');
            },
            handleCustomTdClass(config, item) {
                if (typeof item.packagePrice !== 'number' && config.key === 'packagePrice') {
                    return 'is-warn';
                }
                return '';
            },
        },
    };
</script>
