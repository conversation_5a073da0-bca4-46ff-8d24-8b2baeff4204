.targets-table-wrapper {
    padding: 0 24px;
}

.targets-form-dialog {
    .middle-line {
        display: inline-block;
        width: 18px;
        font-size: 14px;
        color: $T2;
        text-align: center;
    }

    .flex-wrapper {
        display: flex;
        align-items: center;
        width: 302px;

        .abc-form-item {
            margin-bottom: 0;
        }
    }

    .abc-radio {
        height: 24px;
    }

    .form-item-refs {
        align-items: flex-start;

        .abc-radio {
            height: 32px;
        }

        .label-name {
            line-height: 32px;
        }
    }

    .group-item {
        position: relative;
        width: 302px;
        padding: 12px 0;
        border-top: 1px dashed $P6;

        &:hover .delete-icon-wrapper {
            display: block;
        }

        &:first-child {
            margin-top: 4px;
        }

        .delete-icon-wrapper {
            position: absolute;
            top: 0;
            right: -22px;
            box-sizing: border-box;
            display: none;
            height: 100%;
            padding-top: 18px;
            padding-left: 2px;
        }

        .group-item-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 302px;

            .abc-form-item {
                margin-bottom: 0;
            }

            .number-group {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 166px;
            }
        }

        .group-item-bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 302px;
            margin-top: 8px;
        }
    }
}
