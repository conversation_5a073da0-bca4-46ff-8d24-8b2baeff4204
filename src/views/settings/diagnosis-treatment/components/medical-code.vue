<template>
    <abc-popover
        trigger="hover"
        theme="yellow"
        placement="bottom-start"
        width="330px"
        :open-delay="500"
        :disabled="lessOneCode"
        :popper-style="{
            maxHeight: '300px',
            overflow: 'auto',
        }"
        style="overflow: hidden;"
    >
        <abc-flex slot="reference" align="center" :gap="4">
            <abc-text 
                :theme="lessOneCode ? (renderCodeItem.codeInvalid === 1 ? 'warning-light' : 'black') : 'black'"
                style="flex: 1;" 
                class="ellipsis" 
                :title="lessOneCode ? renderCode : undefined"
            >
                {{ renderCode }}

                <span v-if="renderCodeItem.codeInvalid === 1">
                    （已失效）
                </span>
            </abc-text>

            <abc-icon v-if="multiHasInvalidCode" :size="14" icon="Attention"></abc-icon>
        </abc-flex>

        <abc-flex justify="space-between">
            <abc-text bold>
                已对医保码
            </abc-text>

            <abc-text bold>
                {{ list.length }}项
            </abc-text>
        </abc-flex>

        <abc-flex 
            v-for="(l, idx) in list" 
            :key="idx" 
            justify="space-between"
        >
            <abc-text
                style="width: 80px; margin-right: 8px;"
                class="ellipsis"
                :title="l.name"
            >
                {{ l.name }}
            </abc-text>

            <abc-text 
                :theme="l.codeInvalid === 1 ? 'warning-light' : 'black'"
                style="flex: 1; text-align: right;" 
                class="ellipsis" 
                :title="l.shebaoNationalCode"
            >
                {{ l.shebaoNationalCode }}
                <span v-if="l.codeInvalid === 1">
                    （已失效）
                </span>
            </abc-text>
        </abc-flex>
    </abc-popover>
</template>

<script>
    export default {
        name: 'MedicalCode',
        props: {
            goods: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                medicalCodeFilterOptions: [
                    {
                        label: '全部项目',
                        value: -1,
                    },
                    {
                        label: '未对码',
                        value: 0,
                    },
                    {
                        label: '已对码',
                        value: 1,
                    },
                ],
            };
        },
        computed: {
            renderCodeItem() {
                return {
                    shebaoNationalCode: this.goods.shebaoNationalView?.shebaoCode,
                    name: this.goods.shebaoNationalView?.shebaoGoodsName,
                    codeInvalid: this.goods.shebaoNationalView?.codeInvalid,
                };
            },
            list() {
                return this.goods.multiShebao?.list.map((l) => ({
                    shebaoNationalCode: l?.code,
                    name: l?.shebaoName || l?.goodsName,
                    codeInvalid: l?.codeInvalid,
                })) || [ this.renderCodeItem ];
            },
            lessOneCode() {
                return this.list.length <= 1;
            },
            renderCode() {
                return this.medicalCodeToText(this.renderCodeItem.shebaoNationalCode);
            },
            multiHasInvalidCode() {
                return this.list.some((item) => item.codeInvalid === 1);
            },
        },
        methods: {
            medicalCodeToText(code) {
                if (code === 'DISABLED') {
                    return '不允许医保支付';
                }
                return code || '未对码';
            },
        },
    };
</script>