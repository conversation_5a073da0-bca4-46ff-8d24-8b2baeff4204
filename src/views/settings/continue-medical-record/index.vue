<template>
    <we-clinic-no-permission v-if="!isShowOnlinePrescription"></we-clinic-no-permission>
    <introduce v-else :access-key="accessMap.CHARGE_CLONE_PRESCRIPTION">
        <div class="main-content call-number-setting" style="height: 100%; padding: 0;">
            <abc-manage-tabs :option="tabsOption" @change="handleTabsChange"></abc-manage-tabs>
            <router-view></router-view>
        </div>
    </introduce>
</template>

<script type="text/ecmascript-6">
    import Introduce from 'views/edition/introduce/index.vue';
    import AbcAccess from '@/access/utils.js';
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import WeClinicNoPermission from 'views/we-clinic/components/we-clinic-no-permission.vue';

    const tabsOption = [
        {
            label: '自助续方', value: 'continuemedicalrecord-home',
        },
        {
            label: '服务介绍', value: 'continuemedicalrecord-introduce',
        },
    ];
    export default {
        components: {
            WeClinicNoPermission,
            Introduce,
            AbcManageTabs,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                accessMap: AbcAccess.accessMap,
                tabsOption,
            };
        },
        computed: {
            weClinicModulePermission() {
                return this.$abcPage.$store.state.weClinicModulePermission;
            },
            isShowOnlinePrescription() {
                return this.weClinicModulePermission.onlinePrescription;
            },
        },
        methods: {
            handleTabsChange(index, item) {
                this.$router.push({
                    name: item.value,
                });
            },
        },
    };
</script>
