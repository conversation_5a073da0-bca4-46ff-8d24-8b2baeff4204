<template>
    <div class="setting-module__product-center__invoice">
        <navigation-bar :auto-back="false" title="诺诺发票" @back="goBack">
            <abc-button :disabled="!isChange" @click="handleSaveClick">
                保存
            </abc-button>
        </navigation-bar>
        <section class="setting-section">
            <div class="invoice-info">
                <img class="logo" :src="require('@/assets/images/nuonuo-logo.png')" alt="" />
                <div class="content">
                    <div class="title">
                        诺诺发票
                    </div>
                    <span class="tips">开通后即可在收费后开具电子发票。</span>
                </div>
            </div>
        </section>

        <section class="setting-section">
            <div class="section-title">
                销售方信息
                <span class="tips-info">将在发票中展示</span>
            </div>
            <div class="section-content">
                <abc-form
                    ref="form"
                    label-position="left"
                    item-block
                    :label-width="100"
                >
                    <abc-form-item label="纳税人识别号">
                        <abc-input
                            v-model="postData.taxNum"
                            :width="300"
                            readonly
                            disabled
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item required label="企业名称">
                        <abc-input v-model="postData.taxName" :width="300"></abc-input>
                    </abc-form-item>
                    <abc-form-item required label="地址">
                        <abc-input v-model="postData.address" :width="300"></abc-input>
                    </abc-form-item>
                    <abc-form-item required label="电话" :validate-event="validateMobile">
                        <abc-input v-model="postData.telephone" :width="300"></abc-input>
                    </abc-form-item>
                    <abc-form-item label="开户行名称">
                        <abc-input v-model="postData.bankAgent" :width="300"></abc-input>
                    </abc-form-item>
                    <abc-form-item label="开户行账号">
                        <abc-input v-model="postData.bankAccount" :width="300"></abc-input>
                    </abc-form-item>
                    <abc-form-item required label="复核人">
                        <abc-input v-model="postData.invoiceChecker" :width="300" max-length="10"></abc-input>
                    </abc-form-item>
                    <abc-form-item required label="零税率类型">
                        <abc-radio-group v-model="postData.zeroTaxType">
                            <abc-radio :label="0" style="height: 32px;">
                                普通零税率
                            </abc-radio>
                            <abc-radio :label="1" style="height: 32px;">
                                免税
                            </abc-radio>
                            <abc-radio :label="2" style="height: 32px;">
                                不征税
                            </abc-radio>
                        </abc-radio-group>
                    </abc-form-item>
                </abc-form>
            </div>
        </section>

        <section class="setting-section">
            <div class="section-title">
                备注信息
                <span class="tips-info">将在发票中展示</span>
            </div>
            <div class="section-content">
                <abc-form
                    ref="remarkForm"
                    label-position="left"
                    item-block
                    :label-width="100"
                >
                    <abc-form-item
                        label="备注"
                        :custom-label-style="{ paddingTop: '10px', alignSelf: 'flex-start' }"
                    >
                        <abc-radio-group v-model="postData.sendRemarkType">
                            <abc-radio :label="0" style="display: flex; height: 32px;">
                                不打印
                            </abc-radio>
                            <abc-radio :label="2" style="display: flex; height: 32px; margin-left: 0;">
                                打印医保结算信息（简洁版：130字内）
                                <abc-popover
                                    trigger="hover"
                                    placement="top"
                                    style="display: inline-flex; align-items: center; margin-left: 2px;"
                                    theme="yellow"
                                >
                                    <i slot="reference" style="color: #d9d8db;" class="icon iconfont cis-icon-info_bold"></i>
                                    <div style="width: 248px;">
                                        医保:0现金:0本年:0历年:0本年余
                                        额:0历年余额:0起付线:0就诊日
                                        期:xxxx-xx-xx
                                    </div>
                                </abc-popover>
                            </abc-radio>
                            <abc-radio :label="1" style="display: flex; height: 32px; margin-left: 0;">
                                打印医保结算信息（详细版：不限字数，适用于高级版）
                                <abc-popover
                                    trigger="hover"
                                    placement="top"
                                    style="display: inline-flex; align-items: center; margin-left: 2px;"
                                    theme="yellow"
                                >
                                    <i slot="reference" style="color: #d9d8db;" class="icon iconfont cis-icon-info_bold"></i>
                                    <div style="width: 258px;">
                                        现金支付:0,账户支付:0(当年账户:0,
                                        历账:0,共济:0),统筹基金:0,其他支
                                        付:0(大病基金:0,救助支付:0),自费
                                        0,自付:0(起付线:0,乙类先自付:0,医
                                        保超限价:0),当年余额:0,历年余额:0
                                    </div>
                                </abc-popover>
                            </abc-radio>
                        </abc-radio-group>
                    </abc-form-item>
                </abc-form>
            </div>
        </section>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { validateMobile } from 'utils/validate';
    import { isEqual } from 'utils/lodash';
    import store from 'store';
    import NavigationBar from 'views/layout/navigation-bar/navigation-bar';
    export default {
        name: 'InvoiceDetail',
        components: { NavigationBar },
        data() {
            return {
                fromRoute: null, // 上一个页面
                originPostData: {
                    taxNum: '',
                    taxName: '', // 公司名
                    address: '', // 公司地址
                    telephone: '', // 公司电话
                    bankAgent: '', // 开户行
                    bankAccount: '', // 银行账号
                    invoiceChecker: '', // 复核人员姓名
                    zeroTaxType: 0, // 零税率类型  0 普通零税率 1 免税 2 不征税
                    sendRemarkType: 0,
                },
                postData: {
                    taxNum: '',
                    taxName: '', // 公司名
                    address: '', // 公司地址
                    telephone: '', // 公司电话
                    bankAgent: '', // 开户行
                    bankAccount: '', // 银行账号
                    invoiceChecker: '', // 复核人员姓名
                    zeroTaxType: 0, // 零税率类型  0 普通零税率 1 免税 2 不征税
                    sendRemarkType: 0,
                },
                status: 0, // 0 未启用 1 待开通 2 已开通
                type: 0, // 0 未知，1 诺诺发票
            };
        },

        computed: {
            ...mapGetters('invoice', ['isOpenInvoice', 'invoiceInfo', 'isEnableInvoice']),

            isChange() {
                return !isEqual(this.originPostData, this.postData);
            },
        },

        watch: {
            invoiceInfo: {
                handler(val) {
                    Object.assign(this.postData, val);
                    Object.assign(this.originPostData, val);
                },
                deep: true,
                immediate: true,
            },
        },

        async beforeRouteEnter(to, from, next) {
            // 拉取发票配置
            await store.dispatch('invoice/fetchInvoiceConfig');
            next((vm) => {
                vm.fromRoute = from;
            });
        },

        created() {
            // 没有启用电子发票，返回产品中心页
            if (!this.isEnableInvoice) {
                this.goBack();

            }
        },

        methods: {
            validateMobile,
            /**
             * 返回
             * <AUTHOR>
             * @date 2020-09-17
             */
            goBack() {
                const { name } = this.fromRoute || {};
                if (name === 'product-center-invoice') {
                    // 产品中心过来的，直接go(-1)
                    this.$router.go(-1);
                } else {
                    // 其他页面过来的，替换成产品中心
                    this.$router.replace({ name: 'product-center' });
                }
            },

            /**
             * @desc
             * <AUTHOR>
             * @date 2020/12/09 15:34:35
             * @params
             * @return
             */
            async handleSaveClick() {
                this.$refs.form.validate(async (val) => {
                    if (val) {
                        try {
                            await this.$store.dispatch('invoice/updateInvoiceConfig', this.postData);
                            this.$Toast({
                                message: '保存成功',
                                type: 'success',
                            });
                        } catch (e) {
                            this.$Toast({
                                message: '保存失败',
                                type: 'error',
                            });
                        }
                    }
                });
            },
        },
    };
</script>

<style lang="scss">
    @import '~styles/abc-common.scss';

    .setting-module__product-center__invoice {
        padding-bottom: 24px;
        background-color: #ffffff;

        > section {
            padding: 0 24px;
        }

        section.setting-section {
            margin-top: 36px;

            .section-title {
                padding-bottom: 12px;
                font-weight: bold;
                border-bottom: 1px solid $P6;

                .tips-info {
                    margin-left: 16px;
                    font-size: 12px;
                    font-weight: normal;
                    color: $T2;
                }
            }

            .section-content {
                padding-top: 12px;
            }
        }

        .invoice-info {
            display: flex;
            height: 44px;
            margin-right: auto;

            .content {
                display: flex;
                flex-direction: column;
                flex-shrink: 0;
                justify-content: space-between;
                margin-left: 22px;

                .title {
                    font-weight: 500;
                }

                .tips {
                    font-size: 12px;
                    color: $T2;
                }
            }

            img.logo {
                height: 44px;
                object-fit: contain;
            }
        }
    }
</style>
