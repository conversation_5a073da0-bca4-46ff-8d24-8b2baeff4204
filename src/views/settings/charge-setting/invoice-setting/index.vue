<template>
    <biz-setting-layout class="abc-settings__invoice-setting-wrapper">
        <biz-setting-content>
            <biz-setting-form :label-width="120" :content-width="contentWidth">
                <biz-setting-form-group title="发票类型">
                    <abc-flex :gap="24" wrap="wrap">
                        <abc-card class="invoice-item-card digital-invoice-card">
                            <abc-flex vertical justify="space-between" style="height: 100%;">
                                <abc-flex vertical gap="small" class="invoice-item-card-header">
                                    <abc-flex justify="space-between" align="center">
                                        <abc-space>
                                            <abc-text size="large" bold>
                                                ABC数电发票
                                            </abc-text>
                                            <abc-tag-v2
                                                v-if="!digitalInvoiceOpenStatus"
                                                variant="dark"
                                                size="mini"
                                                theme="danger"
                                            >
                                                行业首发
                                            </abc-tag-v2>
                                            <abc-text v-else size="large" :theme="isOpenIsvDigitalInvoice ? 'success-light' : 'warning-light'">
                                                {{ isOpenIsvDigitalInvoice ? '已启用' : '未启用' }}
                                            </abc-text>
                                        </abc-space>
                                        <abc-text v-if="digitalInvoiceOpenStatus" theme="gray">
                                            {{ digitalInvoicePurchaseInfo.endDateStr }} 到期
                                        </abc-text>
                                    </abc-flex>
                                    <abc-text size="mini" theme="gray">
                                        {{ digitalInvoiceOpenStatus ? '@ABC数字医疗云' : '' }}
                                    </abc-text>
                                </abc-flex>
                                <abc-flex class="invoice-item-card-content digital-invoice-content">
                                    <abc-flex v-if="isOpenIsvDigitalInvoice" wrap="wrap" class="digital-invoice-control">
                                        <abc-flex
                                            v-for="(item, index) in digitalInvoiceControlList"
                                            :key="index"
                                            justify="space-between"
                                            align="center"
                                            class="digital-invoice-control-item"
                                            @click="item.click"
                                        >
                                            <abc-space>
                                                <abc-icon :icon="item.icon" :size="20" color="var(--abc-color-T3)"></abc-icon>
                                                <abc-text :theme="isOpenIsvDigitalInvoice ? 'black' : 'gray' ">
                                                    {{ item.name }}
                                                </abc-text>
                                            </abc-space>
                                            <abc-icon icon="n-right-line-medium" :size="16" color="var(--abc-color-T3)"></abc-icon>
                                        </abc-flex>
                                    </abc-flex>
                                    <div v-else class="digital-invoice-advantage">
                                        <abc-space
                                            v-for="(item, index) in digitalInvoiceAdvantage"
                                            :key="index"
                                            :size="12"
                                            style="width: 137px; margin-bottom: 10px;"
                                        >
                                            <div class="dot"></div>
                                            <abc-text theme="gray">
                                                {{ item }}
                                            </abc-text>
                                        </abc-space>
                                    </div>
                                </abc-flex>
                                <div class="invoice-item-card-control">
                                    <biz-marketing-button
                                        v-if="!digitalInvoiceOpenStatus"
                                        button-text="特价开通 ¥1199.20/年"
                                        discount-text="限时八折 直降300"
                                        @click="handleClickOpenDigitalInvoice"
                                    >
                                    </biz-marketing-button>
                                    <template v-else>
                                        <abc-flex gap="middle">
                                            <abc-button
                                                variant="ghost"
                                                size="large"
                                                style="width: 50%;"
                                                class="digital-invoice-instr-btn"
                                                @click="showIntroDialog"
                                            >
                                                产品介绍
                                            </abc-button>
                                            <abc-button
                                                v-if="isOpenIsvDigitalInvoice"
                                                variant="ghost"
                                                size="large"
                                                class="digital-invoice-status-btn"
                                                style="width: 50%;"
                                                @click="handleClickRenewBtn"
                                            >
                                                续费
                                            </abc-button>
                                            <abc-button
                                                v-else
                                                variant="ghost"
                                                size="large"
                                                class="digital-invoice-status-btn"
                                                style="width: 50%;"
                                                @click="openDigitalInvoiceService"
                                            >
                                                立即启用
                                            </abc-button>
                                        </abc-flex>
                                    </template>
                                </div>
                            </abc-flex>
                        </abc-card>
                        <abc-card v-for="(item, index) in invoiceManagerList" :key="index" class="invoice-item-card">
                            <abc-flex vertical justify="space-between" style="height: 100%;">
                                <abc-flex vertical gap="small" class="invoice-item-card-header">
                                    <abc-space>
                                        <abc-text size="large" bold>
                                            {{ item.title }}
                                        </abc-text>
                                        <abc-text v-if="item.isOpen" theme="success-light">
                                            已启用
                                        </abc-text>
                                    </abc-space>
                                    <abc-text size="mini" theme="gray">
                                        {{ item.subTitle || '' }}
                                    </abc-text>
                                </abc-flex>
                                <abc-flex vertical :gap="10" class="invoice-item-card-content">
                                    <abc-flex v-for="(desc, idx) in item.descList" :key="idx" :gap="12">
                                        <div style="height: 20px; padding: 8px 0; text-align: center;">
                                            <div class="dot"></div>
                                        </div>
                                        <abc-text theme="gray" style="flex: 1;">
                                            {{ desc }}
                                        </abc-text>
                                    </abc-flex>
                                </abc-flex>
                                <abc-flex style="width: 100%;" class="invoice-item-card-control">
                                    <abc-button
                                        v-for="(btn, idx) in item.controlList"
                                        :key="idx"
                                        variant="ghost"
                                        size="large"
                                        :style="`width: ${100 / item.controlList.length}%`"
                                        @click="btn.onClick"
                                    >
                                        {{ btn.name }}
                                    </abc-button>
                                </abc-flex>
                            </abc-flex>
                        </abc-card>
                    </abc-flex>
                </biz-setting-form-group>
                <biz-setting-form-group title="开票功能">
                    <biz-setting-form-item v-if="isSupportDefaultInvoice" label="默认开票方式" data-type="label-align">
                        <abc-radio-group v-model="postData.invoiceCategory">
                            <abc-radio :label="3" :disabled="!isOpenDigitalInvoice">
                                数电发票
                            </abc-radio>
                            <abc-radio v-if="isSupportFinanceInvoice" :label="2" :disabled="!isOpenMedicalInvoice">
                                财政电子票据
                            </abc-radio>
                            <abc-radio v-if="isOpenInvoice && !digitalInvoiceOpenStatus" :label="1">
                                增值税普通电子发票
                            </abc-radio>
                            <abc-radio v-if="isSupportPaperInvoice" :label="0">
                                纸质发票
                            </abc-radio>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item :label="`${isHospital ? '门诊' : ''}自动开票`" data-type="label-align">
                        <template #label-tip>
                            <abc-tooltip-info placement="top-start" content="内网开票方式无法自动开票"></abc-tooltip-info>
                        </template>
                        <biz-setting-form-item-tip>
                            <abc-tooltip
                                placement="right"
                                :disabled="!(postData.invoiceCategory === 0 || (postData.invoiceCategory === 2 && !isAllowAutoInvoice))"
                                :content="postData.invoiceCategory === 0 ?
                                    isSupportPaperInvoice ? '如需自动开具纸质发票，请前往【收费-打印设置】开启自动打印纸质发票' : '请先开通数电发票' :
                                    '财政电子票据为内网开票，无法自动开票'"
                            >
                                <abc-checkbox
                                    v-model="enableAutoBill"
                                    type="number"
                                    :disabled="postData.invoiceCategory === 0 || (postData.invoiceCategory === 2 && !isAllowAutoInvoice)"
                                >
                                    开启
                                </abc-checkbox>
                            </abc-tooltip>
                            <template #tip>
                                <abc-text size="mini" theme="gray">
                                    开启后，门诊收费完成将自动开具{{ isSupportPaperInvoice ? '发票（使用默认开票方式）' : '数电发票' }}
                                </abc-text>
                            </template>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                    <biz-setting-form-item v-if="isHospital" label="住院自动开票" data-type="label-align">
                        <template #label-tip>
                            <abc-tooltip-info placement="top-start" content="内网开票方式无法自动开票"></abc-tooltip-info>
                        </template>
                        <biz-setting-form-item-tip>
                            <abc-tooltip
                                placement="right"
                                :disabled="!(postData.invoiceCategory === 0 || (postData.invoiceCategory === 2 && medicalElectronicInfo?.extensions?.apiConfig?.networkMode === 1))"
                                :content="postData.invoiceCategory === 0 ?
                                    isSupportPaperInvoice ? '住院纸质发票暂未支持自动开具，如需开具纸质发票，请前往【住院收费】进行操作' : '请先开通数电发票' :
                                    '财政电子票据为内网开票，无法自动开票'"
                            >
                                <abc-checkbox
                                    v-model="enableAutoBillHospital"
                                    type="number"
                                    :disabled="postData.invoiceCategory === 0 || (postData.invoiceCategory === 2 && medicalElectronicInfo?.extensions?.apiConfig?.networkMode === 1)"
                                >
                                    开启
                                </abc-checkbox>
                            </abc-tooltip>
                            <template #tip>
                                <abc-text size="mini" theme="gray">
                                    开启后，住院收费完成将自动开具{{ isSupportPaperInvoice ? '发票（使用默认开票方式）' : '数电发票' }}
                                </abc-text>
                            </template>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                    <biz-setting-form-item v-if="isOpenPhysicalExamination" label="体检自动开票" data-type="label-align">
                        <template #label-tip>
                            <abc-tooltip-info placement="top-start" content="内网开票方式无法自动开票"></abc-tooltip-info>
                        </template>
                        <biz-setting-form-item-tip>
                            <abc-tooltip
                                placement="right"
                                :disabled="!(postData.invoiceCategory === 0 || (postData.invoiceCategory === 2 && medicalElectronicInfo?.extensions?.apiConfig?.networkMode === 1))"
                                :content="postData.invoiceCategory === 0 ?
                                    isSupportPaperInvoice ? '体检暂不支持开具纸质发票' : '请先开通数电发票' :
                                    '财政电子票据为内网开票，无法自动开票'"
                            >
                                <abc-checkbox
                                    v-model="enableAutoBillPe"
                                    type="number"
                                    :disabled="postData.invoiceCategory === 0 || (postData.invoiceCategory === 2 && medicalElectronicInfo?.extensions?.apiConfig?.networkMode === 1)"
                                >
                                    开启
                                </abc-checkbox>
                            </abc-tooltip>
                            <template #tip>
                                <abc-text size="mini" theme="gray">
                                    开启后，体检收费完成将自动开具{{ isSupportPaperInvoice ? '发票（使用默认开票方式）' : '数电发票' }}
                                </abc-text>
                            </template>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>

                    <biz-setting-form-item :label="invoiceWriteOffText" data-type="label-align">
                        <template #label-tip>
                            <abc-tooltip-info placement="top-start" content="内网开票方式无法自动冲红"></abc-tooltip-info>
                        </template>
                        <biz-setting-form-item-tip>
                            <abc-tooltip
                                placement="right"
                                :disabled="!(postData.invoiceCategory === 2 && !isAllowAutoInvoice)"
                                :content="'财政电子票据为内网开票，无法自动冲红'"
                            >
                                <abc-checkbox
                                    v-model="postData.refundAutoDestroy"
                                    type="number"
                                    :disabled="postData.invoiceCategory === 2 && !isAllowAutoInvoice"
                                >
                                    开启
                                </abc-checkbox>
                            </abc-tooltip>

                            <template #tip>
                                <abc-text size="mini" theme="gray">
                                    开启后，退费完成将{{ invoiceWriteOffText }}已开具的{{ isSupportPaperInvoice ? '发票（纸质发票自动作废，电子发票使用原开票方式自动冲红）' : '数电发票' }}
                                </abc-text>
                            </template>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                </biz-setting-form-group>

                <biz-setting-form-group v-if="isSupportTicketNumberManagement" title="纸质票号管理">
                    <biz-setting-form-item label="票号管理强度" data-type="label-align">
                        <abc-radio-group v-model="postData.normalInvoiceBillStrategy">
                            <biz-setting-form-item-tip>
                                <abc-radio :label="1">
                                    强票号管理
                                </abc-radio>
                                <template #tip>
                                    <abc-text size="mini" theme="gray">
                                        票号不足时无法开票
                                    </abc-text>
                                </template>
                            </biz-setting-form-item-tip>

                            <biz-setting-form-item-tip>
                                <abc-radio :label="0">
                                    弱票号管理
                                </abc-radio>
                                <template #tip>
                                    <abc-text size="mini" theme="gray">
                                        票号不足时直接开票，不做提示
                                    </abc-text>
                                </template>
                            </biz-setting-form-item-tip>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item label="票号管理维度" data-type="label-align">
                        <abc-radio-group v-model="cacheInvoiceScope" @change="handleChangeInvoiceScope">
                            <biz-setting-form-item-tip tip="同一设备不同账号共用票号">
                                <abc-radio :label="InvoiceNumberScope.MAC_ADDRESS">
                                    设备维度管理
                                </abc-radio>
                            </biz-setting-form-item-tip>
                            <biz-setting-form-item-tip>
                                <biz-setting-form-item-tip tip="同一账号不同设备共用票号">
                                    <abc-radio :label="InvoiceNumberScope.EMPLOYEE">
                                        账号维度管理
                                    </abc-radio>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item-tip>
                        </abc-radio-group>
                    </biz-setting-form-item>
                </biz-setting-form-group>
            </biz-setting-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button
                        :disabled="disabledBtn"
                        :loading="loading"
                        @click="saveSettings"
                    >
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <digital-invoice-dialog
            v-if="showDigitalInvoiceDialog"
            v-model="showDigitalInvoiceDialog"
        ></digital-invoice-dialog>

        <digital-invoice-pay
            v-if="showDigitalInvoicePay"
            v-model="showDigitalInvoicePay"
            :buy-method="currentDigitalBuyMethod"
            @pay-success="showPayInfoDialog"
        ></digital-invoice-pay>
        <digital-invoice-pay-success
            v-if="showPayInfo"
            v-model="showPayInfo"
            :pay-info="payInfo"
            :buy-method="currentDigitalBuyMethod"
            @open-service="openDigitalInvoiceService"
        ></digital-invoice-pay-success>

        <medical-electronic-invoice-guide-dialog
            v-if="showGuideDialog"
            v-model="showGuideDialog"
            @config-invoice="handleApplyMedicalInvoice"
        ></medical-electronic-invoice-guide-dialog>
        <invoice-settings-dialog
            v-if="showSettingsDialog"
            v-model="showSettingsDialog"
            :invoice-category="currentInvoiceCategory"
            :invoice-supplier-id="currentInvoiceSupplierId"
        ></invoice-settings-dialog>

        <!-- 功能介绍弹窗 -->
        <digital-invoice-intro-dialog
            v-if="introDialogVisible"
            v-model="introDialogVisible"
            @pay-success="showPayInfoDialog"
            @open-service="openDigitalInvoiceService"
        ></digital-invoice-intro-dialog>

        <!-- 开票账户弹窗 -->
        <digital-invoice-account-dialog
            v-if="accountDialogVisible"
            v-model="accountDialogVisible"
        ></digital-invoice-account-dialog>

        <invoice-rate-setting
            v-if="rateSettingDialogVisible"
            v-model="rateSettingDialogVisible"
            :invoice-supplier-id="currentInvoiceSupplierId"
        ></invoice-rate-setting>

        <digital-invoice-operator-dialog
            v-if="showOperatorDialog"
            v-model="showOperatorDialog"
        ></digital-invoice-operator-dialog>
    </biz-setting-layout>
</template>

<script>
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
    } from '@/components-composite/setting-form/index.js';
    import { mapGetters } from 'vuex';

    import {
        InvoiceCategory,
        InvoiceNumberScope,
        InvoiceSupplierId,
        InvoiceSupplierName,
    } from 'views/cashier/invoice/constants.js';
    import clone from 'utils/clone';
    import { isEqual } from 'utils/lodash';
    import Store from 'utils/localStorage-handler';
    import {
        AutoWriteInvoiceStorageKey,
        BosiInvoiceProvinceIds, DiagitalInvoiceBuyMethod,
    } from 'views/settings/charge-setting/invoice-setting/constant';
    import AbcSocket from 'views/common/single-socket';
    import AutoWriteMedicalElectronicInvoiceService
        from 'views/cashier/invoice/write-invoice-core-v2/auto-write-invoice/auto-write-medical-electronic-invoice-service';
    import FaceValidateDialog from 'views/cashier/invoice/tax-fully-digital-dialog/face-validate-dialog';
    import MedicalElectronicInvoiceGuideDialog
        from 'views/settings/charge-setting/invoice-setting/components/medical-electronic-invoice-guide-dialog.vue';
    import DigitalInvoiceDialog
        from 'views/settings/charge-setting/invoice-setting/components/digital-invoice-dialog.vue';
    import DigitalInvoiceAccountDialog
        from 'views/settings/charge-setting/invoice-setting/components/digital-invoice-account-dialog.vue';
    import InvoiceRateSetting from 'views/settings/charge-setting/invoice-setting/components/invoice-rate-setting.vue';
    import DigitalInvoiceIntroDialog
        from 'views/settings/charge-setting/invoice-setting/components/digital-invoice-intro-dialog.vue';
    import DigitalInvoiceOperatorDialog
        from 'views/settings/charge-setting/invoice-setting/components/digital-invoice-operator-dialog.vue';
    import InvoiceSettingsDialog from 'views/settings/charge-setting/invoice-setting/components/settings-dialog.vue';
    import DigitalInvoicePay from 'views/settings/charge-setting/invoice-setting/components/digital-invoice-pay.vue';
    import digitalInvoicePaySuccess
        from 'views/settings/charge-setting/invoice-setting/components/digital-invoice-pay-success.vue';
    import useDigitalInvoiceOpenStatus
        from 'views/settings/charge-setting/invoice-setting/hooks/use-digital-invoice-open-status';
    import BizMarketingButton from 'src/components-composite/biz-marketing-button';

    export default {
        name: 'InvoiceSetting',
        components: {
            BizMarketingButton,
            digitalInvoicePaySuccess,
            DigitalInvoicePay,
            InvoiceSettingsDialog,
            DigitalInvoiceOperatorDialog,
            DigitalInvoiceIntroDialog,
            InvoiceRateSetting,
            DigitalInvoiceAccountDialog,
            DigitalInvoiceDialog,
            MedicalElectronicInvoiceGuideDialog,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
        },
        beforeRouteLeave(to, from, next) {
            if (!this.disabledBtn) {
                this.$confirm({
                    type: 'warn',
                    title: '你的修改内容还未保存，确定离开？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        setup() {
            const {
                digitalInvoiceOpenStatus,
                digitalInvoicePurchaseInfo,
                fetchWaitingPayIndependentPurchaseItems,
            } = useDigitalInvoiceOpenStatus();

            return {
                digitalInvoiceOpenStatus,
                digitalInvoicePurchaseInfo,
                fetchWaitingPayIndependentPurchaseItems,
            };
        },
        data() {
            return {
                InvoiceNumberScope,
                InvoiceSupplierName,
                currentDigitalBuyMethod: DiagitalInvoiceBuyMethod.NEW,

                showDigitalInvoiceDialog: false,
                showDigitalInvoicePay: false,
                showGuideDialog: false,
                showSettingsDialog: false,
                currentInvoiceCategory: '',
                currentInvoiceSupplierId: '',
                introDialogVisible: false,
                showOperatorDialog: false,
                rateSettingDialogVisible: false,
                accountDialogVisible: false,
                showPayInfo: false,
                payInfo: {},

                loading: false,

                postData: {
                    invoiceCategory: 0, // 默认开票方式：0-纸质发票，3-数电发票，2-财政电子票据，1-增值税普通电子发票
                    enableAutoBill: 0, // 自动开票：0-不自动开票，1-自动开具
                    enableAutoBillHospital: 0, // 住院自动开票：0-不自动开票，1-自动开具
                    enableAutoBillPe: 0, // 体检自动开票：0-不自动开票，1-自动开具
                    refundAutoDestroy: 0, // 自动冲红
                    normalInvoiceBillStrategy: 0, // 票号管理强度：1-强票号管理，0-弱票号管理
                    invoiceScope: InvoiceNumberScope.MAC_ADDRESS, // 票号管理维度：1-设备纬度管理，2-账号纬度管理
                },
                cacheInvoiceScope: InvoiceNumberScope.MAC_ADDRESS,
                cachePostData: null,

                digitalInvoiceAdvantage: [
                    '一键开票',
                    '税局直连',
                    '全自动托管',
                    '灵活分类管理',
                ],
                digitalInvoiceControlList: [
                    {
                        name: '开票账户',
                        icon: 's-user-line',
                        click: this.showAccountInfoDialog,
                    },
                    {
                        name: '开票税率',
                        icon: 's-currency-3-line',
                        click: this.showRateSettingDialog,
                    },
                    {
                        name: '开票员管理',
                        icon: 's-onepersonlist-line',
                        click: this.showOperatorManageDialog,
                    },
                    {
                        name: '人脸验证',
                        icon: 's-b-scan-line',
                        click: this.showFaceValidateDialog,
                    },
                ],

                isLocalWriteInvoiceHost: false,
            };
        },
        computed: {
            ...mapGetters('invoice', [
                'isOpenIsvDigitalInvoice',
                'isOpenNuonuoDigitalInvoice',
                'isOpenMedicalInvoice',
                'isOpenInvoice',
                'medicalElectronicAPIConfig',
                'writeInvoiceConfig',
                'medicalElectronicInfo',
                'taxFullyDigitalInfo',
                'invoiceOperatorList',
            ]),
            ...mapGetters([
                'currentClinic',
                'clinicBasicConfig',
                'isHospital',
                'userInfo',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters('edition', ['allAccessObj']),
            isOpenPhysicalExamination() {
                const _accessObj = this.allAccessObj['physical-examination'];
                // 找不到默认true
                if (!_accessObj) return true;
                return !!_accessObj.isPurchased;
            },
            disabledBtn() {
                return isEqual(this.postData, this.cachePostData);
            },
            isBosiLocal() {
                return this.medicalElectronicInfo?.invoiceSupplierId === InvoiceSupplierId.FUJIAN_BOSI &&
                    this.medicalElectronicInfo?.extensions?.apiConfig?.networkMode;
            },
            // 呼和浩特
            isHuhehaote() {
                return ['150100'].includes(this.clinicBasicConfig.addressCityId);
            },
            isNanjin() {
                return ['320100'].includes(this.clinicBasicConfig.addressCityId);
            },
            // 贵州，内蒙，广西,重庆，安徽支持博思
            isSupportBosi() {
                return BosiInvoiceProvinceIds.includes(this.clinicBasicConfig.addressProvinceId) ||
                    this.isHuhehaote;
            },
            isSupportFinanceInvoice() {
                return this.viewDistributeConfig.Settings.isSupportFinanceInvoice;
            },
            isSupportPaperInvoice() {
                return this.viewDistributeConfig.Settings.isSupportPaperInvoice;
            },
            isSupportDefaultInvoice() {
                return this.viewDistributeConfig.Settings.isSupportDefaultInvoice;
            },
            invoiceWriteOffText() {
                return this.viewDistributeConfig.Settings.invoiceWriteOffText;
            },
            isSupportTicketNumberManagement() {
                return this.viewDistributeConfig.Settings.isSupportTicketNumberManagement;
            },
            invoiceManagerList() {
                return [
                    {
                        title: '数电发票',
                        subTitle: '@诺诺',
                        descList: [
                            '第三方提供服务，ABC仅做系统接入',
                            '如需使用请先联系开票平台开通',
                        ],
                        controlList: [
                            {
                                name: '开票账户',
                                onClick: () => this.configVatInvoiceService(InvoiceCategory.TAX_FULLY_DIGITAL),
                            },
                            {
                                name: '开票税率',
                                onClick: this.showRateSettingDialog,
                            },
                        ],
                        isOpen: this.isOpenNuonuoDigitalInvoice,
                        show: this.isOpenNuonuoDigitalInvoice && !this.digitalInvoiceOpenStatus,
                    },
                    {
                        title: '财政电子票据',
                        subTitle: this.isOpenMedicalInvoice ? `@${InvoiceSupplierName[this.medicalElectronicInfo.invoiceSupplierId]}` : '',
                        descList: [
                            '第三方提供服务，ABC仅做系统接入',
                            '如需使用请先联系财政开票平台开通',
                        ],
                        controlList: [
                            {
                                name: this.isOpenMedicalInvoice ? '开票账户' : '立即开通',
                                onClick: this.isOpenMedicalInvoice ? this.configFinanceInvoiceService : this.openFinanceInvoiceService,
                            },
                        ],
                        isOpen: this.isOpenMedicalInvoice,
                        show: this.isSupportFinanceInvoice,
                    },
                    {
                        title: '增值税普通电子发票',
                        subTitle: '@诺诺',
                        descList: ['纸质发票票面设置请前往【打印设置-票据-医疗票据】'],
                        controlList: [
                            {
                                name: '开票账户',
                                onClick: () => this.configVatInvoiceService(InvoiceCategory.ELECTRONIC),
                            },
                        ],
                        isOpen: this.isOpenInvoice,
                        show: this.isOpenInvoice && !this.digitalInvoiceOpenStatus,
                    },
                    {
                        title: '纸质发票',
                        descList: ['纸质发票票面设置请前往【打印设置-票据-医疗票据】'],
                        controlList: this.isHospital ? [
                            {
                                name: '设置门诊纸票',
                                onClick: this.handleToPrintSetting,
                            },
                            {
                                name: '设置住院纸票',
                                onClick: this.handleToHospitalPrintSetting,
                            },
                        ] : [
                            {
                                name: '前往票面设置',
                                onClick: this.handleToPrintSetting,
                            },
                        ],
                        show: this.isSupportPaperInvoice,
                    },
                ].filter((item) => item.show !== false);
            },
            contentWidth() {
                const invoiceTypeNum = this.invoiceManagerList.length + 1;
                const width = invoiceTypeNum * 328 + 24 * (invoiceTypeNum - 1) + 8;
                const windowWith = window.innerWidth;
                if (windowWith < 1760 && width > 1040) {
                    return '1040px';
                }
                return `${Math.max(width, 1040)}px`;
            },
            enableAutoBill: {
                get () {
                    if (this.postData.invoiceCategory === 0 ||
                        (this.postData.invoiceCategory === 2 && !this.isAllowAutoInvoice)
                    ) {
                        return 0;
                    }
                    return this.postData.enableAutoBill;
                },
                set (val) {
                    this.postData.enableAutoBill = val;
                },
            },
            enableAutoBillHospital: {
                get () {
                    if (this.postData.invoiceCategory === 0 || (this.postData.invoiceCategory === 2 && this.medicalElectronicInfo?.extensions?.apiConfig?.networkMode === 1)) {
                        return 0;
                    }
                    return this.postData.enableAutoBillHospital;
                },
                set (val) {
                    this.postData.enableAutoBillHospital = val;
                },
            },
            enableAutoBillPe: {
                get () {
                    if (this.postData.invoiceCategory === 0 || (this.postData.invoiceCategory === 2 && this.medicalElectronicInfo?.extensions?.apiConfig?.networkMode === 1)) {
                        return 0;
                    }
                    return this.postData.enableAutoBillPe;
                },
                set (val) {
                    this.postData.enableAutoBillPe = val;
                },
            },
            // 允许自动开医疗电子发票
            isAllowAutoInvoice() {
                return !this.isLocalInvoice || (this.isBosiLocal && this.isLocalWriteInvoiceHost);
            },
            isWuAiEInvoice() {
                return this.medicalElectronicInfo?.invoiceSupplierId === InvoiceSupplierId.NANJING_WUAI;
            },
            isLocalInvoice() {
                return !!this.medicalElectronicInfo?.extensions?.apiConfig?.networkMode || this.isWuAiEInvoice;
            },
            // 是否支持数电发票
            isOpenDigitalInvoice() {
                return this.isOpenIsvDigitalInvoice || this.isOpenNuonuoDigitalInvoice;
            },
        },
        watch: {
            'writeInvoiceConfig': {
                handler(val) {
                    if (val) {
                        this.postData.invoiceCategory = val.invoiceCategory;
                        this.postData.enableAutoBill = val.enableAutoBill;
                        this.postData.enableAutoBillHospital = val.enableAutoBillHospital;
                        this.postData.enableAutoBillPe = val.enableAutoBillPe;
                        this.postData.refundAutoDestroy = val.refundAutoDestroy;
                        this.postData.normalInvoiceBillStrategy = val.normalInvoiceBillStrategy;
                        this.postData.invoiceScope = val.invoiceScope;
                    } else {
                        this.postData = {
                            invoiceCategory: 1, // 是否开启自动开票
                            enableAutoBill: 0, // 自动开票
                            enableAutoBillHospital: 0, // 自动开票
                            enableAutoBillPe: 0, // 自动开票
                            refundAutoDestroy: 0, // 自动冲红
                            normalInvoiceBillStrategy: 0, // 弱校验票号
                            invoiceScope: InvoiceNumberScope.MAC_ADDRESS,
                        };
                    }
                    this.cacheInvoiceScope = this.postData.invoiceScope;
                    this.cachePostData = clone(this.postData);
                },
                immediate: true,
                deep: true,
            },
            'medicalElectronicAPIConfig': {
                handler() {
                    this.checkNetwork();
                },
                deep: true,
            },
        },
        async created() {
            await this.$store.dispatch('invoice/initInvoiceConfig');
            this.fetchWaitingPayIndependentPurchaseItems();
            this.checkNetwork();
        },
        methods: {
            // 数电发票相关方法
            openDigitalInvoiceService() {
                this.showDigitalInvoiceDialog = true;
            },
            showPayInfoDialog(data) {
                this.payInfo = data;
                this.showPayInfo = true;
            },
            openFinanceInvoiceService() {
                if (!this.isNanjin && !this.isSupportBosi) {
                    this.$alert({
                        type: 'warn',
                        title: '本地区暂未接入医疗电子发票',
                        content: '本地区还未接入医疗电子发票开票平台，请先联系ABC客服进行对接后才可开票',
                    });
                } else {
                    this.showGuideDialog = true;
                }
            },
            handleApplyMedicalInvoice(invoiceData) {
                this.showGuideDialog = false;
                const {
                    invoiceCategory,invoiceSupplierId,
                } = invoiceData;
                this.currentInvoiceSupplierId = invoiceSupplierId;
                this.currentInvoiceCategory = invoiceCategory;
                this.showSettingsDialog = true;
            },
            configFinanceInvoiceService() {
                this.currentInvoiceCategory = InvoiceCategory.MEDICAL_ELECTRONIC;
                this.currentInvoiceSupplierId = this.medicalElectronicInfo?.invoiceSupplierId;
                this.showSettingsDialog = true;
            },
            configVatInvoiceService(invoiceCategory) {
                this.currentInvoiceCategory = invoiceCategory;
                this.currentInvoiceSupplierId = InvoiceSupplierId.NUONUO;
                this.showSettingsDialog = true;
            },
            handleClickOpenDigitalInvoice() {
                this.currentDigitalBuyMethod = DiagitalInvoiceBuyMethod.NEW;
                this.introDialogVisible = true;
            },
            handleToPrintSetting() {
                this.$router.push({
                    name: 'printConfigMedicalBills',
                });
            },
            handleToHospitalPrintSetting() {
                this.$router.push({
                    name: 'medical-bill',
                });
            },

            showIntroDialog() {
                this.introDialogVisible = true;
            },
            handleClickRenewBtn() {
                this.currentDigitalBuyMethod = DiagitalInvoiceBuyMethod.RENEW;
                this.showDigitalInvoicePay = true;
            },
            showAccountInfoDialog() {
                this.accountDialogVisible = true;
            },
            showRateSettingDialog() {
                this.currentInvoiceSupplierId = this.taxFullyDigitalInfo?.invoiceSupplierId;
                this.rateSettingDialogVisible = true;
            },
            showOperatorManageDialog() {
                this.showOperatorDialog = true;
            },
            showFaceValidateDialog() {
                new FaceValidateDialog({
                    value: true,
                    showTips: false,
                    employeeId: this.userInfo.id,
                    onFinish: () => {
                        // do nothing
                    },
                }).generateDialogAsync({ parent: this });
            },

            async checkNetwork() {
                const {
                    ip, port,
                } = this.medicalElectronicAPIConfig;
                this.isLocalWriteInvoiceHost = false;
                if (window.electron?.network?.checkConnection && this.isLocalInvoice) {
                    this.isLocalWriteInvoiceHost = await window.electron.network.checkConnection(ip, Number(port));
                    console.log('检查网络response', this.isLocalWriteInvoiceHost);
                }
            },
            handleChangeInvoiceScope() {
                if (this.cacheInvoiceScope !== this.postData.invoiceScope) {
                    this.$confirm({
                        title: '提示',
                        type: 'warn',
                        content: '修改票号管理方式后保存设置，店内所有已开发票票号不可修改、已添加未使用的票号将立即失效，需要重新管理录入，建议非营业时间修改设置。',
                        onConfirm: async () => {
                            this.postData.invoiceScope = this.cacheInvoiceScope;
                        },
                        onCancel: () => {
                            this.cacheInvoiceScope = this.postData.invoiceScope;
                        },
                    });
                }
            },
            // 保存设置
            async saveSettings() {
                this.loading = true;
                try {
                    const activeAutoInvoiceDevice = Number(this.isLocalWriteInvoiceHost && this.isBosiLocal && this.postData.invoiceCategory === 2);
                    const lastAutoInvoiceDeviceId = Store.get(`${AutoWriteInvoiceStorageKey}_${this.currentClinic.clinicId}`, true)?.autoInvoiceDeviceId;

                    const postData = {
                        invoiceCategory: this.postData.invoiceCategory,
                        enableAutoBill: this.enableAutoBill,
                        enableAutoBillHospital: this.enableAutoBillHospital,
                        enableAutoBillPe: this.enableAutoBillPe,
                        refundAutoDestroy: this.postData.refundAutoDestroy,
                        normalInvoiceBillStrategy: this.postData.normalInvoiceBillStrategy,
                        activeAutoInvoiceDevice,
                        autoInvoiceDeviceId: lastAutoInvoiceDeviceId,
                        invoiceScope: this.postData.invoiceScope,
                    };
                    const res = await this.$store.dispatch('invoice/updateWriteInvoiceConfig', postData);
                    const { autoInvoiceDeviceId } = res;
                    Store.set(`${AutoWriteInvoiceStorageKey}_${this.currentClinic.clinicId}`, {
                        autoInvoiceDeviceId,
                    });

                    // 重新订阅消息
                    if (lastAutoInvoiceDeviceId !== autoInvoiceDeviceId) {
                        const { socket } = AbcSocket.getSocket();
                        this.autoWriteInvoiceService = AutoWriteMedicalElectronicInvoiceService.getInstance(socket, this.currentClinic.clinicId, this.medicalElectronicAPIConfig);
                        this.autoWriteInvoiceService.update();
                        console.log('更新消息订阅');
                    }
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                } catch (error) {
                    console.warn('保存配置失败', error);
                } finally {
                    this.loading = false;
                }
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/mixin.scss";

.abc-settings__invoice-setting-wrapper {
    .invoice-item-card {
        width: 330px;
        height: 256px;
        margin-top: 0;
        background: linear-gradient(180deg, rgba(228, 240, 255, 0.8) 0%, rgba(241, 247, 255, 0) 26.96%);

        &.digital-invoice-card {
            background: linear-gradient(180deg, rgba(255, 251, 241, 0.8) 0%, rgba(255, 242, 211, 0) 28.7%);

            .digital-invoice-status-btn {
                color: var(--abc-color-T1);
                background: linear-gradient(90.29deg, #efc584 0.29%, #f1d0a1 99.81%);
                border: none;

                &:hover {
                    background: linear-gradient(90.29deg, #f3d19d 0.29%, #f4dab4 99.81%);
                    border: none;
                }
            }

            .digital-invoice-instr-btn {
                color: #6e5a46;

                &:hover {
                    background: var(--abc-color-Y4);
                    border: 1px solid var(--abc-color-Y6, #6e5a46);
                }
            }
        }

        .dot {
            width: 4px;
            height: 4px;
            background: var(--abc-color-T3);
            border-radius: 50%;
        }

        .invoice-item-card-header {
            height: 84px;
            padding: 20px 20px 0;
        }

        .invoice-item-card-content {
            height: 92px;
            padding: 0 20px;

            &.digital-invoice-content {
                padding: 0;

                .digital-invoice-control {
                    width: 100%;

                    .digital-invoice-control-item {
                        width: 50%;
                        padding: 12px 16px;
                        cursor: pointer;
                        border-top: 1px solid var(--abc-color-P6);

                        &:nth-child(2n+1) {
                            border-right: 1px solid var(--abc-color-P6);
                        }

                        &:hover {
                            background: var(--abc-color-cp-grey4);
                        }
                    }
                }

                .digital-invoice-advantage {
                    padding: 0 20px;
                }
            }
        }

        .invoice-item-card-control {
            padding: 20px;
            border-top: 1px solid var(--abc-color-P6);
        }
    }
}
</style>
