<template>
    <abc-dialog
        v-model="dialogVisible"
        custom-class="infectious-diseases-dialog"
        content-styles=" padding: 24px 14px 24px 24px;"
        size="xlarge"
        title="三类传染病"
    >
        <div v-for="(val, key) in showInfectiousDisease" :key="key" class="infectious-content">
            <div class="grade-title">
                {{ key | filterGrade }}({{ Object.keys(val).length }}种)
            </div>
            <div class="grade-infectious-content">
                <abc-space
                    v-for="(diseaseVal, diseaseKey) in val"
                    :key="diseaseKey"
                    class="disease"
                    :size="4"
                >
                    {{ diseaseKey }}
                    <template v-if="diseaseVal.length">
                        <abc-tooltip-info>
                            <div class="infectious-diseases-popover-container">
                                <div v-for="disease in diseaseVal" :key="disease" class="popover-item">
                                    {{ disease }}
                                </div>
                            </div>
                        </abc-tooltip-info>
                    </template>
                </abc-space>
            </div>
        </div>
    </abc-dialog>
</template>

<script>
    import infectiousDiseasesEnum, { showInfectiousDisease } from 'src/assets/configure/infectious-diseases';

    export default {
        name: 'InfectiousDiseasesDialog',
        filters: {
            filterGrade(val) {
                if (val === 'classA') return '甲类';
                if (val === 'classB') return '乙类';
                return '丙类';
            },
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                infectiousDiseases: infectiousDiseasesEnum,
                showInfectiousDisease,
            };
        },
        computed: {
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.infectious-diseases-dialog {
    .abc-dialog-body {
        display: flex;
        flex-direction: column;
        overflow-y: auto;

        @include scrollBar(true);

        .infectious-content {
            display: flex;
            flex-direction: column;
            width: 100%;

            &:not(:nth-of-type(1)) {
                margin-top: 20px;
            }

            .grade-title {
                font-size: 16px;
                color: $T2;
            }
        }

        .grade-infectious-content {
            display: flex;
            flex-wrap: wrap;
            gap: 5px 0;
            width: 100%;
            margin-top: 10px;

            .disease {
                display: flex;
                align-items: center;
                width: 25%;

                @include ellipsis;
            }
        }
    }
}

.infectious-diseases-popover-container {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 0;
    width: 500px;

    .popover-item {
        width: 33%;

        @include ellipsis;
    }
}
</style>
