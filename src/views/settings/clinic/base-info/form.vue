<template>
    <biz-setting-layout>
        <biz-setting-content>
            <abc-form
                ref="updateClinicInfo"
                v-abc-loading="loading"
                class="clinic-info-form"
                item-block
                item-no-margin
                label-position="left"
            >
                <biz-setting-form :label-width="84">
                    <biz-setting-form-group>
                        <biz-setting-form-header>
                            <abc-tips-card-v2 theme="primary">
                                请确保以下信息填写真实有效，开通微{{ $app.institutionTypeWording }}后，{{ roleLabel }}可在微{{ $app.institutionTypeWording }}中查看医疗机构信息
                            </abc-tips-card-v2>
                        </biz-setting-form-header>

                        <biz-setting-form-item v-if="isChainAdmin" label="机构名称" label-line-height-size="medium">
                            <abc-form-item required>
                                <abc-space>
                                    <abc-input
                                        v-model="postData.name"
                                        :width="300"
                                        max-length="30"
                                        :disabled="!canModified"
                                    ></abc-input>

                                    <abc-tooltip-info placement="top">
                                        <div style="width: 330px;">
                                            <p>{{ organNameTip }}</p>
                                        </div>
                                    </abc-tooltip-info>
                                </abc-space>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-else label="机构名称" label-line-height-size="medium">
                            <abc-form-item
                                required
                                :custom-label-style="{ paddingTop: '10px' }"
                            >
                                <abc-space>
                                    <abc-input
                                        v-model="postData.name"
                                        style="width: 300px;"
                                        :maxlength="30"
                                        placeholder="显示在处方抬头"
                                        :disabled="!canModified"
                                    >
                                    </abc-input>

                                    <abc-tooltip-info placement="top" :max-width="330">
                                        <p>{{ organNameTip }}</p>
                                    </abc-tooltip-info>
                                </abc-space>
                            </abc-form-item>
                        </biz-setting-form-item>
                        <biz-setting-form-item label="内部简称" label-line-height-size="medium">
                            <abc-form-item>
                                <abc-space>
                                    <abc-input
                                        v-model="postData.shortName"
                                        :width="300"
                                        max-length="30"
                                        :disabled="!canModified"
                                    ></abc-input>

                                    <abc-tooltip-info placement="top" :max-width="330">
                                        主要用于内部展示，如系统上各个模块店名、微{{ $app.institutionTypeWording }}店名的展示。当连锁门店过多时，简称可以方便内部人员和{{ roleLabel }}快速区分不同门店
                                    </abc-tooltip-info>
                                </abc-space>
                            </abc-form-item>
                        </biz-setting-form-item>


                        <template v-if="isSupportNationalCode">
                            <biz-setting-form-item label="国家医保代码" label-line-height-size="medium">
                                <abc-form-item>
                                    <abc-input
                                        v-model="postData.nationalCode"
                                        :disabled="isChainAdmin"
                                        :width="300"
                                        max-length="30"
                                        type="number-en-char"
                                    ></abc-input>
                                </abc-form-item>
                            </biz-setting-form-item>
                        </template>

                        <template v-if="isSupportBusMode && isChainSubStore">
                            <biz-setting-form-item label="类型" label-line-height-size="medium">
                                <abc-form-item>
                                    <abc-input
                                        :value="busModeType[postData.busMode]"
                                        :width="300"
                                        :disabled="true"
                                    ></abc-input>
                                </abc-form-item>
                            </biz-setting-form-item>
                        </template>

                        <template v-if="isSupportTrustCode">
                            <biz-setting-form-item label="信用代码" label-line-height-size="medium">
                                <abc-form-item
                                    v-if="!isChainAdmin"
                                >
                                    <abc-input
                                        v-model="postData.creditCode"
                                        :width="300"
                                        max-length="30"
                                        type="number-en-char"
                                    ></abc-input>
                                </abc-form-item>
                            </biz-setting-form-item>
                        </template>

                        <template v-if="isSupportBusinessScope">
                            <biz-setting-form-item label="经营范围" label-line-height-size="medium">
                                <abc-form-item required>
                                    <div style=" width: 610px; font-size: 14px; line-height: 20px;">
                                        {{ businessScopeName }}
                                        <abc-button variant="text" size="small" @click="selectBusinessScope">
                                            修改
                                        </abc-button>

                                        <abc-text
                                            v-if="isShowBusinessScopeTip"
                                            theme="warning-light"
                                            class="business-scope-error-tip"
                                        >
                                            经营范围不能为空
                                        </abc-text>
                                    </div>
                                </abc-form-item>
                            </biz-setting-form-item>
                        </template>


                        <biz-setting-form-item label="机构地址" label-line-height-size="medium">
                            <abc-form-item :validate-event="handleAddressValidate">
                                <abc-space>
                                    <abc-address-selector
                                        ref="addressSelector"
                                        v-model="postData"
                                        :width="300"
                                        @change="onAddressChange"
                                    ></abc-address-selector>
                                </abc-space>
                            </abc-form-item>
                            <abc-form-item label=" ">
                                <abc-space>
                                    <abc-space is-compact compact-block>
                                        <abc-input
                                            v-model="postData.addressDetail"
                                            :title="postData.addressDetail"
                                            :width="218"
                                            placeholder="详细地址"
                                            disabled
                                        >
                                        </abc-input>
                                        <abc-button
                                            theme="primary"
                                            variant="ghost"
                                            @click="openTmapModal"
                                        >
                                            {{ postData.addressDetail ? '点击修改' : '点击录入' }}
                                        </abc-button>
                                    </abc-space>
                                    <abc-tooltip-info placement="top">
                                        地址主要应用于打印单据、微诊所等，方便患者了解机构位置
                                    </abc-tooltip-info>
                                </abc-space>
                            </abc-form-item>
                        </biz-setting-form-item>



                        <template v-if="isSupportOrganType">
                            <biz-setting-form-item label="机构类型" label-line-height-size="medium">
                                <abc-form-item>
                                    <abc-cascader
                                        v-model="medicalOrganizationType"
                                        :props="{
                                            children: 'childs',
                                            label: 'name',
                                            value: 'id'
                                        }"
                                        placeholder="请选择"
                                        :width="300"
                                        :only-show-leaf-label="true"
                                        :options="OrganizationTypes"
                                        @change="changeCategory"
                                    >
                                    </abc-cascader>
                                </abc-form-item>
                            </biz-setting-form-item>
                        </template>

                        <template v-if="isSupportOrganGrade">
                            <biz-setting-form-item label="机构等级" label-line-height-size="medium">
                                <abc-form-item>
                                    <abc-select
                                        v-model="agencyGradeId"
                                        :disabled="isDisableAgencyGradeId"
                                        :width="300"
                                        placeholder="请选择"
                                        @change="changeAgencyGradeId"
                                    >
                                        <abc-option
                                            v-for="item in AgencyGrades"
                                            :key="item.id"
                                            :value="item.id"
                                            :label="item.name"
                                        >
                                        </abc-option>
                                    </abc-select>
                                    <abc-tooltip-info
                                        v-if="isDisableAgencyGradeId"
                                        placement="top"
                                        style="display: inline-flex; align-items: center; margin-left: 8px;"
                                    >
                                        <div>
                                            <p>机构等级用于医保确认限价范围等业务，如需修改请联系ABC客户，电话************</p>
                                        </div>
                                    </abc-tooltip-info>
                                </abc-form-item>
                            </biz-setting-form-item>
                        </template>

                        <biz-setting-form-item label="联系电话1" label-line-height-size="medium">
                            <abc-form-item :validate-event="validateMobile">
                                <abc-input
                                    v-model="postData.contactPhone"
                                    :width="300"
                                    :max-length="20"
                                    type="phone"
                                ></abc-input>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="联系电话2" label-line-height-size="medium" has-divider>
                            <abc-form-item :validate-event="validateMobile">
                                <abc-input
                                    v-model="postData.backupContactPhone"
                                    :width="300"
                                    :max-length="20"
                                    type="phone"
                                ></abc-input>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <template v-if="isSupportBusinessSubject">
                            <biz-setting-form-item label="执业许可科目" label-line-height-size="small" has-divider>
                                <abc-space>
                                    <abc-text
                                        size="normal"
                                        tag="div"
                                        class="ellipsis"
                                        style="max-width: 570px;"
                                        :title="practiceSubject"
                                    >
                                        {{ practiceSubject }}
                                    </abc-text>
                                    <abc-button variant="text" size="small" @click="selectType">
                                        修改
                                    </abc-button>
                                </abc-space>
                            </biz-setting-form-item>
                        </template>

                        <biz-setting-form-item :label="`${$app.institutionTypeWording}Logo`" has-divider label-line-height-size="medium">
                            <abc-form-item>
                                <biz-setting-form-item-tip tip="Logo图片可用于处方打印、叫号屏显示，建议白色或透明背景">
                                    <setting-set-logo
                                        v-model="postData.logo"
                                        file-path="basic"
                                    ></setting-set-logo>
                                </biz-setting-form-item-tip>
                            </abc-form-item>
                        </biz-setting-form-item>


                        <template v-if="isSupportCompanyWeChat && !isIntranetUser">
                            <biz-setting-form-item label="企业微信" label-line-height-size="tiny">
                                <abc-space :size="4">
                                    <template v-if="postData.qwCorpInfo">
                                        <abc-icon icon="chosen" size="14" color="#0EBA52"></abc-icon>
                                        <abc-text size="normal">
                                            已绑定
                                        </abc-text>
                                    </template>

                                    <template v-else>
                                        <abc-icon icon="chosen" size="14" color="#D9DBE3"></abc-icon>
                                        <abc-text size="normal" theme="gray">
                                            未绑定
                                        </abc-text>
                                    </template>
                                    <template v-if="postData.qwCorpInfo">
                                        <abc-text size="normal" style="margin-left: 12px;">
                                            企业简称：{{ postData.qwCorpInfo.name }}
                                        </abc-text>

                                        <abc-tooltip-info>
                                            如需换绑，请联系ABC客服，客服电话：************
                                        </abc-tooltip-info>
                                    </template>
                                </abc-space>
                            </biz-setting-form-item>
                        </template>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button :disabled="!isUpdate" @click="save">
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <abc-dialog
            v-if="selectTypeDialog"
            v-model="selectTypeDialog"
            title="选择执业许可范围"
            content-styles="padding: 0 0 0 24px;height: 494px"
        >
            <abc-transfer v-model="selectedPracticeSubject" :data="medicalLevel">
                <template #selected="{ item }">
                    {{ item.name }}
                </template>
            </abc-transfer>
            <div slot="footer" class="dialog-footer">
                <abc-button @click="confirmSelect">
                    确定
                </abc-button>
                <abc-button type="blank" @click="selectTypeDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <!--经营范围选择-->
        <business-scope-transfer-dialog
            v-model="selectBusinessScopeDialog"
            :scene="CatalogueEnum.BUSINESS_SCOPE_ORGAN"
            :default-checked-keys="defaultCheckedKeys"
            @confirm="confirmSelectBusinessScope"
        ></business-scope-transfer-dialog>
        <!-- 机构地图选址 -->
        <tmap-address-select
            v-if="tMapDialog"
            v-model="tMapDialog"
            :init-data="postData"
            @confirm="confirmTMapSelect"
        ></tmap-address-select>
    </biz-setting-layout>
</template>

<script type="text/ecmascript-6">
    import CdssAPI from 'src/api/cdss/index';
    import Api from 'api/settings';

    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import {
        OrganizationTypes, AgencyGrades,
    } from 'views/settings/clinic/base-info/constant';
    import { validateMobile } from 'utils/validate';
    import {
        mapGetters, mapActions,
    } from 'vuex';
    import { busModeType } from '@/views/settings/clinic/chain-sub-info/constant';
    import BusinessScopeTransferDialog
        from '@/views-pharmacy/inventory/frames/supplier/components/businessScopeTransfer.vue';
    import { CatalogueEnum } from '@/hooks/business/use-dictionary';
    import useBusinessScope from 'views/inventory/goods/archives/hook/useBusinessScope';
    import TmapAddressSelect from './components/tmap-address-select.vue';
    import ClinicAPI from 'api/clinic';

    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormHeader,
        BizSettingFormItemTip,
    } from '@/components-composite/setting-form/index.js' ;

    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import SettingSetLogo from 'views/settings/components/setting-set-logo/index.vue';


    export default {
        components: {
            SettingSetLogo,
            BusinessScopeTransferDialog,
            TmapAddressSelect,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormHeader,
            BizSettingFormItemTip,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
        },
        setup() {
            const {
                init,
                getBusinessScopeName,
            } = useBusinessScope(CatalogueEnum.BUSINESS_SCOPE_ORGAN);

            return {
                init,
                getBusinessScopeName,
            };
        },
        data() {
            return {
                selectTypeDialog: false,
                isUpdate: false,

                selectedPracticeSubject: [],
                medicalLevel: [], // 一级诊疗科目

                postData: {
                    addressProvinceName: '',
                    addressCityName: '',
                    addressDistrictName: '',
                    addressProvinceId: '',
                    addressCityId: '',
                    addressDistrictId: '',

                    addressDetail: '',
                    contactPhone: '',
                    backupContactPhone: '',
                    id: '',
                    name: '',
                    shortName: '',
                    // type: '',  //@废弃
                    // typeName: '', //@废弃
                    practiceSubject: [],
                    category: '', // 医疗机构类型
                    logo: '',

                    nationalCode: '', // 国家机构编码
                    classCode: '', // 级码
                    degreeCode: '', // 等码

                    businessScope: [], // 经营范围
                    creditCode: '', // 信用代码
                    busMode: '', // 门店类型
                },
                canModified: 1, // 机构名称、内部简称是否限制修改
                agencyGradeId: '', // 机构等级id

                medicalOrganizationType: [],
                showTips: false,
                loading: false,
                selectBusinessScopeDialog: false,
                isShowBusinessScopeTip: false,
                businessScopeName: '',
                defaultCheckedKeys: [],

                AgencyGrades,
                OrganizationTypes,
                busModeType,
                tMapDialog: false, // 机构选址弹窗显隐
            };
        },
        computed: {
            CatalogueEnum() {
                return CatalogueEnum;
            },
            ...mapGetters([
                'currentClinic',
                'clinics',
                'userInfo',
                'isChainSubStore',
                'isChainAdmin',
                'isHospital',
                'isIntranetUser',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            organNameTip() {
                return this.viewDistributeConfig.Settings.clinic.baseInfo.organNameTip;
            },
            // 是否支持信用代码
            isSupportTrustCode() {
                return this.viewDistributeConfig.Settings.clinicInfo.isSupportTrustCode;
            },
            // 是否支持经营范围
            isSupportBusinessScope() {
                return this.viewDistributeConfig.Settings.clinicInfo.isSupportBusinessScope;
            },
            // 是否支持机构类型
            isSupportOrganType() {
                return this.viewDistributeConfig.Settings.clinicInfo.isSupportOrganType;
            },
            // 是否支持机构登记
            isSupportOrganGrade() {
                return this.viewDistributeConfig.Settings.clinicInfo.isSupportOrganGrade;
            },
            // 是否支持执业许可科目
            isSupportBusinessSubject() {
                return this.viewDistributeConfig.Settings.clinicInfo.isSupportBusinessSubject;
            },
            // 是否支持国家医保代码
            isSupportNationalCode() {
                return this.viewDistributeConfig.Settings.clinicInfo.isSupportNationalCode;
            },
            // 是否支持企业微信
            isSupportCompanyWeChat() {
                return this.viewDistributeConfig.Settings.clinicInfo.isSupportCompanyWeChat;
            },
            // 是否支持门店类型设置
            isSupportBusMode() {
                return this.viewDistributeConfig.Settings.clinicInfo.isSupportBusMode;
            },
            practiceSubject() {
                return this.postData.practiceSubject.map((item) => {
                    return item.name.replace(/.*\|/, '');
                }).join(' / ');
            },
            /**
             * @desc 是否禁止修改机构等级
             */
            isDisableAgencyGradeId() {
                return this.isHospital && this.$abcSocialSecurity.isOpenSocial;
            },
            roleLabel() {
                return this.viewDistributeConfig.roleLabel;
            },
        },

        watch: {
            postData: {
                handler (val) {
                    this.isUpdate = !isEqual(this._postDataCache, val);
                    this.isShowBusinessScopeTip = this.postData.businessScope.length === 0;
                },
                deep: true,
            },
        },

        async created() {
            await this.init();
            // 设置诊所id
            // dispatch诊所信息同时需要修改clinics
            const { clinicId } = this.currentClinic;
            this.clinicId = clinicId;
            this.fetchClinicInfoById();

            this.fetchSuggest();

        },
        methods: {
            ...mapActions([
                'acFetchUserInfo',
                'acFetchCurrentClinicInfo',
                'acGetClinicJoined',
            ]),
            validateMobile,

            /**
             * @desc 机构等级由 【医院等级（级）分类：1 一级， 2 二级 3 三级 9 未评级】和 【医院等级（等）分类：1 特等， 2 甲等 3 乙等 4 丙等 9 未评等】构成
             * @agencyGradeId 机构等级id, 由医院等级（级）的码classCode 和 医院等级（等）的码degreeCode 拼接而成；改变时拆分并获取 级 和 等 对应的code。
             */
            changeAgencyGradeId(val) {
                if (val && val.length === 2) {
                    this.postData.classCode = val[0];
                    this.postData.degreeCode = val[1];
                } else {
                    this.postData.classCode = null;
                    this.postData.degreeCode = null;
                }
            },

            selectType() {
                this.selectedPracticeSubject = this.postData.practiceSubject.slice();

                this.postData.practiceSubject.forEach((item) => {
                    this.medicalLevel.forEach((ml) => {
                        if (item.id === ml.id) {
                            ml.checked = true;
                        }
                    });
                });

                this.selectTypeDialog = true;
            },

            async fetchSuggest() {
                // level = level || 0;
                // parentId = parentId || null;
                this.loading = true;
                try {
                    const { data } = await CdssAPI.doSearch({
                        client: 'department',
                        level: 0,
                    });
                    if (data && data.hits && Array.isArray(data.hits)) {
                        this.medicalLevel = data.hits.map((item) => {
                            item.id = item.departmentId;
                            item.name = item.name.replace(/.*\|/, '');
                            return item;
                        });
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },

            async initClinicInfo(data) {
                // 诊所类型
                // 处理诊所类型下拉列表
                Object.assign(this.postData, data);

                const {
                    practiceSubject = [],
                    category = '',
                    classCode = '',
                    degreeCode = '',
                } = this.postData;

                this.postData.practiceSubject = practiceSubject || [];
                this.postData.category = category || '';
                this.medicalOrganizationType = category ? this.findCategory(category) : [];
                this.canModified = data.canModified;
                this.agencyGradeId = classCode && degreeCode ? `${classCode}${degreeCode}` : '';
                this.postData.businessScope = data.businessScope || [];

                this.businessScopeName = this.getBusinessScopeName(this.postData.businessScope);

                this._postDataCache = Clone(this.postData);

                await this.acFetchCurrentClinicInfo();
            },
            /**
             * @desc 找到医疗机构类型对应的值
             */
            findCategory(category) {
                const res = [];
                return this.findNode(OrganizationTypes, category, res);
            },
            findNode(array, target, res) {
                for (let i = 0;i < array.length; i++) {
                    res.push({
                        value: array[i].id,
                        label: array[i].name,
                    });
                    if (array[i].name === target) {
                        return res;
                    }
                    const children = array[i].childs;
                    if (children && children.length) {
                        const result = this.findNode(children, target, res);
                        if (result) return result;
                    }
                    res.pop();
                }
                return false;
            },

            changeCategory(data) {
                if (data && data.length) {
                    this.postData.category = data.length > 1 ? data[1].label : data[0].label;
                } else {
                    this.postData.category = '';
                }
            },

            async fetchClinicInfoById() {
                this.loading = true;
                try {
                    const res = await Api.clinic.fetchClinicInfoById();
                    const { data } = res.data;
                    this.initClinicInfo(data);
                } catch (e) {
                    console.log(e);
                }
                this.loading = false;
            },

            confirmSelect() {
                this.postData.practiceSubject = this.selectedPracticeSubject;
                this.selectTypeDialog = false;
            },

            save() {
                this.$refs.updateClinicInfo.validate(async (value) => {
                    if (this.isSupportBusinessScope && this.postData.businessScope.length === 0) {
                        return ;
                    }
                    if (value) {
                        this.submit();
                    }
                });
            },

            async submit() {
                // 诊所类型
                // 处理诊所类型下拉列表
                try {
                    await Api.clinic.update({
                        ...this.postData,
                        addressProvinceId: this.postData.addressProvinceId,
                        addressCityId: this.postData.addressCityId,
                        addressDistrictId: this.postData.addressDistrictId,
                    });

                    this._postDataCache = Clone(this.postData);
                    this.isUpdate = false;

                    await Promise.all([
                        this.acFetchUserInfo(),
                        this.acGetClinicJoined(),
                        this.acFetchCurrentClinicInfo(),
                    ]);
                    this.$store.dispatch('fetchClinicBasicConfig');

                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (err) {
                    this.$Toast({
                        message: '保存失败',
                        type: 'error',
                    });
                    this.postData = Clone(this._postDataCache);
                }
            },
            // 选择经营范围
            selectBusinessScope() {
                this.defaultCheckedKeys = this.postData.businessScope.map((item) => item.id);
                this.selectBusinessScopeDialog = true;
            },

            // 确认选择经营范围
            confirmSelectBusinessScope(payload, name) {
                this.businessScopeName = name;
                this.postData.businessScope = payload;
                this.selectBusinessScopeDialog = false;
            },

            cancel() {
                this.selectBusinessScopeDialog = false;
            },

            // 地址省市区校验
            handleAddressValidate(_, callback) {
                const {
                    addressProvinceId,
                    addressCityId,
                    addressDistrictId,
                } = this.postData;
                if (!addressProvinceId || !addressCityId || !addressDistrictId) {
                    callback({
                        validate: false,
                        message: '请完善省市区信息',
                    });
                }
            },

            // 省市区切换，区级有变化则清空详细地址信息
            onAddressChange(value) {
                if (this._postDataCache.addressCityId !== value.addressCityId) {
                    this.postData.addressTitle = '';
                    this.postData.addressSource = '';
                    this.postData.addressDetail = '';
                    this.postData.addressLatitude = '';
                    this.postData.addressLongitude = '';
                    this.postData.addressGeo = '';
                    if (this.postData.config) {
                        this.postData.config.addressTitle = '';
                    }
                }
            },

            // 打开腾讯地图选址
            openTmapModal() {
                const {
                    addressProvinceId,
                    addressCityId,
                    addressDistrictId,
                } = this.postData;
                if (!addressProvinceId || !addressCityId || !addressDistrictId) {
                    this.$Toast({
                        message: '请先选择省/市/区后再录入详细地址',
                        type: 'warning',
                    });
                    return;
                }
                this.tMapDialog = true;
            },

            // 根据地图选址更新省市区选择
            async updateTMapSelectRegion(data) {
                if (!data.addressData) {
                    return;
                }
                const {
                    provice, city, district,
                } = data.addressData;
                const params = {
                    regionName: district || city || provice,
                };
                if (!params.regionName || params.regionName === this.postData.addressDistrictName) return;
                const result = await ClinicAPI.searchAddressRegion(params);
                if (result && result.district) {
                    this.postData.addressDistrictId = result.district.id;
                    this.postData.addressDistrictName = result.district.name;
                    this.$refs?.addressSelector?.selectedDistrict(result.district.name, result.district.id);
                }
            },

            // 机构地图选址
            confirmTMapSelect(data) {
                if (data.isInitMarker) { // 未变更地址
                    this.tMapDialog = false;
                    return;
                }
                if (!this.postData.config) {
                    this.postData.config = {};
                }
                this.postData.config.addressTitle = data.addressTitle; // config.addressTitle， 用于接口取
                this.postData.addressTitle = data.addressTitle; // 用于接口存
                this.postData.addressSource = data.addressSource; // 地址来源 0-手动 1-地图
                this.postData.addressDetail = data.addressDetail || data.addressTitle;
                this.postData.addressLatitude = data.addressLatitude;
                this.postData.addressLongitude = data.addressLongitude;
                this.postData.addressGeo = (data.addressLongitude && data.addressLatitude) ? `${data.addressLongitude},${data.addressLatitude}` : '';
                this.tMapDialog = false;
                this.updateTMapSelectRegion(data);
            },
        },
    };
</script>
