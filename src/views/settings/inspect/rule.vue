<template>
    <biz-setting-layout
        v-abc-loading="loading"
        style="position: relative;"
    >
        <biz-setting-content>
            <biz-setting-form :label-width="existProcessFlowSetting ? 142 : 98">
                <abc-form ref="form">
                    <biz-setting-form-group v-if="existProcessFlowSetting" title="RIS流程设置">
                        <biz-setting-form-item
                            label="检查（RIS）流程设置"
                        >
                            <abc-radio-group v-model="postData.pacsSettings.processMode">
                                <biz-setting-form-item-tip tip="适用于有设立预约登记机制的机构">
                                    <abc-radio :label="0">
                                        <abc-flex align="center" :gap="2">
                                            全流程：预约登记 <abc-icon icon="Arrow_Rgiht_" :color="themeStyle.T3"></abc-icon> 检查诊断
                                        </abc-flex>
                                    </abc-radio>
                                </biz-setting-form-item-tip>

                                <biz-setting-form-item-tip tip="适用于没有设计预约登记机制的机构，开单后直接上机检查">
                                    <abc-radio :label="1">
                                        简易流程：检查诊断
                                    </abc-radio>
                                </biz-setting-form-item-tip>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="检查开单设置">
                        <biz-setting-form-item
                            label="执行检查时机"
                        >
                            <abc-radio-group v-model="postData.onlyExecuteAfterPaid">
                                <biz-setting-form-item-tip tip="开单后可执行检查，减少患者付费次数">
                                    <abc-radio :label="0">
                                        开单后即可执行检查
                                    </abc-radio>
                                </biz-setting-form-item-tip>

                                <biz-setting-form-item-tip tip="避免漏费，未收费无法执行检查">
                                    <abc-radio :label="1">
                                        收费后才可以执行检查
                                    </abc-radio>
                                </biz-setting-form-item-tip>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            :label="medicalOrderRevokeRule.label"
                        >
                            <abc-radio-group v-model="postData.undoSettings.rule">
                                <biz-setting-form-item-tip v-for="(o, key) in medicalOrderRevokeRule.options" :key="key" :tip="o.tip">
                                    <abc-radio :label="o.value">
                                        {{ `检查${ o.label}` }}
                                    </abc-radio>
                                </biz-setting-form-item-tip>
                            </abc-radio-group>
                        </biz-setting-form-item>


                        <biz-setting-form-item
                            label="项目合并规则"
                        >
                            <abc-radio-group v-model="postData.mergeSettings.rule">
                                <abc-radio :label="0">
                                    一个项目对应一条检查单
                                </abc-radio>

                                <abc-radio :label="1" @click="handleMergeProjectSettingChange">
                                    同类型检查项目合并成一条检查单
                                </abc-radio>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="检查操作设置">
                        <biz-setting-form-item
                            label="报告审核模式"
                        >
                            <biz-setting-form-item-tip tip="开启后，指定审核人完成审核后，才能出报告">
                                <abc-checkbox 
                                    v-model="postData.reportCheckSettings.needCheck" 
                                    type="number" 
                                    @change="postData.reportCheckSettings.checkerUpdateReport = 0"
                                >
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            v-if="postData.reportCheckSettings.needCheck"
                            label="审核人修改报告"
                        >
                            <biz-setting-form-item-tip tip="开启后，审核人可直接修改报告">
                                <abc-checkbox v-model="postData.reportCheckSettings.checkerUpdateReport" type="number">
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </abc-form>
            </biz-setting-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button
                        :loading="btnLoading"
                        :disabled="!isUpdate"
                        @click="onClickSave"
                    >
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <abc-dialog
            v-if="modalVisible"
            v-model="modalVisible"
            title="设置机房自助登记预约分配规则"
            content-styles="width: 460px; height:230px; padding: 24px 12px 24px 24px;"
        >
            <div class="room-reg-set-wrapper">
                <abc-radio-group v-model="postData.onlyExecuteAfterPaid">
                    <div class="room-reg-set-item">
                        <abc-radio :label="0">
                            按设备空闲资源优先分配
                        </abc-radio>

                        <div class="room-reg-set-item__tip">
                            减少患者等待时间
                        </div>
                    </div>

                    <div class="room-reg-set-item">
                        <abc-radio :label="1">
                            按机房顺序依次排满整天
                        </abc-radio>

                        <div class="room-reg-set-item__tip">
                            适合设备机房之间距离较远
                        </div>

                        <div class="room-order-wrapper">
                            <abc-select
                                v-for="(o, key) in rooms"
                                :key="key"
                                v-model="roomValues[key]"
                                class="order-select"
                                width="124"
                                clearable
                            >
                                <abc-option
                                    v-for="(m, n) in rooms"
                                    :key="n"
                                    :label="m.name"
                                    :value="m.id"
                                    :index="n"
                                    :disabled="roomValues.includes(m.id) && roomValues[key] !== m.id"
                                ></abc-option>
                            </abc-select>
                        </div>
                    </div>
                </abc-radio-group>
            </div>


            <div slot="footer" style="text-align: right;">
                <abc-button
                    type="primary"
                    @click="save"
                >
                    确定
                </abc-button>

                <abc-button type="blank" @click="modalVisible = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </biz-setting-layout>
</template>

<script>
    import {
        mapGetters, mapMutations,
    } from 'vuex';
    import propertyAPI from 'api/property';

    import InspectAPI from 'api/hospital/inspect';
    import themeStyle from 'src/styles/theme.module.scss';
    import { isHospital } from 'views/common/clinic';
    import AbcAccess from '@/access/utils';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';

    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
    } from '@/components-composite/setting-form/index.js';

    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';


    export default {
        components: {
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
        },
        beforeRouteLeave(to, from, next) {
            if (this.isUpdate) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '当前设置尚未保存，是否离开?',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                themeStyle,
                loading: false,

                btnLoading: false,

                modalVisible: false,

                rooms: [],
                roomValues: [],

                postData: {
                    pacsSettings: {
                        deviceList: null,
                        interfaceIp: '',
                        interfacePort: '',
                        processMode: undefined,
                    },
                    mergeSettings: {
                        rule: undefined,
                    },
                    onlyExecuteAfterPaid: undefined,
                    reportCheckSettings: {
                        needCheck: undefined,
                        checkerUpdateReport: undefined,
                    },
                    undoSettings: {
                        rule: 0,
                    },
                },
                cachePostData: null,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            existProcessFlowSetting() {
                return this._isHospital || AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION);
            },
            _isHospital() {
                return isHospital(this.currentClinic);
            },
            isUpdate() {
                if (!this.cachePostData) return false;
                return !isEqual(this.postData, this.cachePostData);
            },

            medicalOrderRevokeRule() {
                return this.viewDistributeConfig.Settings.medicalOrderRevokeRule;
            },
        },
        created() {
            this.propertyKey = 'examination.settings.inspect';
            this.propertyScope = 'clinic';
            this.initExaminationSettings();

            this.loadRooms();
        },
      
        methods: {
            ...mapMutations('inspect', ['updateInspectRuleSettings']),

            save() {
                //
            },

            async loadRooms() {
                const { data } = await InspectAPI.loadDeviceRoom();
                this.rooms = data.rows || [];

                if (!this.rooms.length) {
                    this.roomValues = [];
                    return;
                }


                this.roomValues = [this.rooms[0].id, ...new Array(this.rooms.length - 1).fill('')];
            },

            handleClick() {
                this.modalVisible = true;
            },

            async initExaminationSettings() {
                this.loading = true;

                try {
                    const { data } = await propertyAPI.getV3(this.propertyKey,this.propertyScope);
                    this.postData = data;
                    this.cachePostData = Clone(this.postData);
                    // 更新 vuex 中检查的规则设置
                    this.updateInspectRuleSettings(data);
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },

            async onClickSave() {
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        this.submitHandler();
                    }
                });
            },

            async submitHandler() {
                try {
                    this.btnLoading = true;
                    const { data } = await propertyAPI.updateV3(this.propertyKey, this.propertyScope,{
                        ...this.postData,
                    });
                    this.postData = data;
                    this.cachePostData = Clone(this.postData);
                    // 更新 vuex 中检查的规则设置
                    this.updateInspectRuleSettings(data);
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (e) {
                    this.$Toast({
                        message: '保存失败',
                        type: 'error',
                    });
                } finally {
                    this.btnLoading = false;
                }
            },

            handleMergeProjectSettingChange() {
                const h = this.$createElement;

                this.$confirm({
                    type: 'warn',
                    title: '切换合并规则提示：',
                    content: () => h(
                        'div',
                        [
                            '1、切换后历史报告也会同步切换',
                            '2、切换成合并模式，检查人、审核人只取其中1份报告的检查人、审核人',
                            '3、因合并导致报告的检查人、审核人发生改变，检查业绩也会一起改变',
                        ].map((item) => (
                            h(
                                'div',
                                item,
                                null,
                            )
                        )),
                        null,
                    ),
                    onConfirm: () => {
                        this.mergeValue = 1;
                    },
                    onCancel: () => {
                        this.mergeValue = 0;
                    },
                    contentStyles: {
                        width: '480px',
                    },
                });
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';

.room-reg-set-wrapper {
    .room-reg-set-item {
        margin-bottom: 16px;

        .room-reg-set-item__tip {
            margin-left: 24px;
            font-size: 12px;
            color: $T2;
        }

        .room-order-wrapper {
            display: flex;
            flex-wrap: wrap;
            padding-left: 24px;
            margin-top: 8px;

            .order-select {
                margin: 0 8px 8px 0;

                &:nth-child(3n) {
                    margin-right: 0;
                }
            }
        }
    }
}
</style>
