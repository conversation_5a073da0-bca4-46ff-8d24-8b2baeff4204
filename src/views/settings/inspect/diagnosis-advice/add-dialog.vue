<template>
    <abc-dialog
        v-model="dialogVisible"
        class="add-diagnosis-advice-dialog"
        content-styles="width: 400px;height: 200px"
        :title="`${id ? '编辑' : '新建'}诊断意见`"
        @close="$emit('close')"
    >
        <abc-form
            ref="form"
            v-abc-loading="loading.page"
            label-position="left"
            item-block
            :label-width="84"
        >
            <abc-form-item
                label="诊断意见"
                required
                hidden-red-dot
            >
                <abc-input
                    v-model="postData.name"
                ></abc-input>
            </abc-form-item>

            <abc-form-item
                label="检查类型"
                required
                hidden-red-dot
            >
                <abc-select
                    v-model="postData.deviceType"
                    adaptive-width
                >
                    <abc-option
                        v-for="(o,i) in inspectTypeOptions"
                        :key="i"
                        v-bind="o"
                    ></abc-option>
                </abc-select>
            </abc-form-item>

            <abc-form-item
                label="异常关注"
                required
                hidden-red-dot
            >
                <abc-select
                    v-model="postData.abnormalFlag"
                    adaptive-width
                >
                    <abc-option
                        v-for="(o,i) in abnormalOptions"
                        :key="i"
                        v-bind="o"
                    ></abc-option>
                </abc-select>
            </abc-form-item>
        </abc-form>

        <template slot="footer">
            <div class="dialog-footer">
                <abc-button
                    :loading="loading.btn"
                    @click="handleSubmit"
                >
                    确定
                </abc-button>

                <abc-button type="blank" @click="dialogVisible = false">
                    取消
                </abc-button>
            </div>
        </template>
    </abc-dialog>
</template>

<script>
    import {
        ABNORMAL_ENUM,
    } from './constant';
    import {
        GoodsTypeEnum,
        GoodsSubTypeEnum,
    } from '@abc/constants';
    import ExaminationAPI from '@/api/examination';

    export default {
        name: 'AddDiagnosisAdviceDialog',

        props: {
            value: Boolean,

            id: {
                type: String,
                default: '',
            },

            inspectTypeOptions: {
                type: Array,
                default: () => ([]),
            },

            defaultData: {
                type: Object,
                default: null,
            },
        },

        data () {
            return {
                postData: {
                    abnormalFlag: '',
                    deviceType: '',
                    name: '',
                    type: GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test,
                },

                loading: {
                    page: false,
                    btn: false,
                },
            };
        },

        computed: {
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            abnormalOptions() {
                return [
                    {
                        label: ABNORMAL_ENUM[ABNORMAL_ENUM.normal],
                        value: ABNORMAL_ENUM.normal,
                    },
                    {
                        label: ABNORMAL_ENUM[ABNORMAL_ENUM.abnormal],
                        value: ABNORMAL_ENUM.abnormal,
                    },
                ];
            },
        },

        created() {
            this.fetchDetail();
            this.defaultData && this.setDefaultData();
        },

        methods: {
            async fetchDetail() {
                if (!this.id) return;

                this.loading.page = true;

                try {
                    const { data } = await ExaminationAPI.getExaminationDiagnosisEntryById(this.id);

                    this.postData = data;
                } catch (error) {
                    console.error(error);
                }

                this.loading.page = false;
            },

            setDefaultData() {
                this.postData = {
                    ...this.postData,
                    ...(this.defaultData || {}),
                };
            },

            handleSubmit() {
                this.$refs.form.validate(async (v) => {
                    if (!v) return;

                    this.loading.btn = true;

                    try {
                        let res;
                        if (this.id) {
                            res = await ExaminationAPI.updateExaminationDiagnosisEntryById(this.id, this.postData);
                        } else {
                            res = await ExaminationAPI.saveExaminationDiagnosisEntry(this.postData);
                        }

                        this.$Toast({
                            type: 'success',
                            message: '已保存',
                        });

                        this.dialogVisible = false;

                        this.$emit('refresh', res.data);
                    } catch (error) {
                        console.error(error);
                    }

                    this.loading.page = false;
                });
            },
        },
    };
</script>

<style lang="scss">
.add-diagnosis-advice-dialog {
    .abc-input-wrapper {
        width: 100%;
    }

    .abc-input__inner {
        width: 100%;
    }

    .abc-form-item:last-child {
        margin-bottom: 0;
    }
}
</style>
