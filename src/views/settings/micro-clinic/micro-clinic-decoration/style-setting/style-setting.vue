<template>
    <div class="micro-clinic-search-wrapper">
        <div class="micro-clinic-decorate-preview-layout">
            <decoration-preview
                ref="decorationPreview"
                :preview-url="previewUrl"
                :arrow-default-top="500"
            >
                <div class="decorate-preview-slot-wrapper">
                    <div class="decorate-preview-slot-header">
                        风格设置
                    </div>
                    <div class="decorate-preview-slot-content">
                        <div class="decorate-preview-slot-content-title">
                            皮肤颜色
                        </div>
                        <div class="decorate-preview__theme-wrapper">
                            <div
                                v-for="theme in themeList"
                                :key="theme.name"
                                class="decorate-preview__theme-item"
                                :class="{ 'active': currentTheme === theme.name }"
                                :style="{ backgroundColor: theme.value }"
                                @click="currentTheme = theme.name"
                            >
                            </div>
                        </div>
                    </div>
                    <div class="decorate-preview-slot-footer">
                        <abc-button
                            :loading="saveLoading"
                            :disabled=" !hasChangeTheme || saveLoading"
                            style="width: 80px;"
                            @click="handleSave"
                        >
                            保存
                        </abc-button>
                        <abc-button
                            type="blank"
                            style="width: 80px;"
                            :disabled="!hasChangeTheme || saveLoading"
                            @click="handleCancel"
                        >
                            取消
                        </abc-button>
                    </div>
                </div>
            </decoration-preview>
        </div>
    </div>
</template>

<script>
    import DecorationPreview from '../../decoration/index';
    import { CHANGE_THEME } from 'views/settings/micro-clinic/decoration/constant';
    import { getPreviewBaseUrl } from 'views/settings/micro-clinic/decoration/config';
    import { isEqual } from '@/utils/lodash';
    import {
        mapState, mapGetters,
    } from 'vuex';
    export default {
        name: 'StyleSetting',
        components: {
            DecorationPreview,
        },
        data() {
            return {
                isLoading: false,
                saveLoading: false,
                themeList: [
                    {
                        name: 'theme1',
                        value: '#D66A44',
                    },
                    {
                        name: 'theme2',
                        value: '#377BD8',
                    },
                    {
                        name: 'theme3',
                        value: '#EB8F3B',
                    },
                    {
                        name: 'theme4',
                        value: '#1D8CA2',
                    },
                ],
                currentTheme: '',
                originTheme: '',
            };
        },
        computed: {
            ...mapState('weClinic', ['config']),
            ...mapGetters('weClinic', ['style']),
            previewUrl() {
                return `${getPreviewBaseUrl()}/home`;
            },
            hasChangeTheme() {
                return !isEqual(this.currentTheme, this.originTheme);
            },
        },
        async created() {
            await this.$store.dispatch('weClinic/fetchConfig');
            this.currentTheme = this.config.style;
            this.originTheme = this.currentTheme;
        },
        updated () {
            this.$emit('updateHomepageConfig', this.isChangeList);
        },
        methods: {
            handleCancel() {
                this.currentTheme = this.originTheme;
            },
            async handleSave() {
                try {
                    this.saveLoading = true;
                    await this.$store.dispatch('weClinic/updateStyle', this.currentTheme);
                    this.$refs.decorationPreview.getInstance().emit(CHANGE_THEME, {
                        command: 'changeTheme',
                        data: this.currentTheme,
                    });
                    this.originTheme = this.currentTheme;
                } catch (err) {
                    console.error(err);
                } finally {
                    this.saveLoading = false;
                }
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.micro-clinic-search-wrapper {
    overflow: hidden;

    .decorate-preview-slot-content-title {
        margin-bottom: 10px;
        color: $T2;
    }

    .decorate-preview__theme-wrapper {
        display: flex;

        .decorate-preview__theme-item {
            width: 36px;
            height: 36px;
            margin-right: 24px;
            cursor: pointer;
            border-radius: var(--abc-border-radius-small);

            &.active {
                width: 36px;
                height: 36px;
                border: 4px solid $S2;
                outline-style: solid;
                outline-color: #3495ff;
            }
        }
    }
}
</style>
