import { Mc<PERSON>omponent<PERSON><PERSON> } from 'views/settings/micro-clinic/core/mc-component-api.js';
import AbcSocket from 'views/common/single-socket';
import {
    MP_WEAPP_AUTH_SOCKET_EVENT_NAME, MpWeappAuthSocketMessage,
} from '@/views/we-clinic/data';

export class MpAuthPresenter {
    _socket = null;
    _socketListener = null;

    isDestroyed = false;

    view = null;
    appInfoList = [];
    weappQualify = {
        businessLicenseUrl: '',
        medicalLicenseUrl: '',
    };

    attachView(view) {
        this.view = view;
        this.initSocket();
    }

    async created() {
        await this.loadData();
    }
    async loadData() {
        const [appInfoList, weappQualify] = await Promise.all([this.fetchMpWeappInfo(), this.fetchWeappQualify()]);
        if (this.isDestroyed) {
            return;
        }

        this.appInfoList = appInfoList;
        this.weappQualify = weappQualify;


        this.view.initData({
            appInfoList: this.appInfoList,
            weappQualify: this.weappQualify,
        });
    }

    initSocket() {
        const { socket } = AbcSocket.getSocket();
        this._socket = socket;
        this._socketListener = this.onAuthEvent.bind(this);
        // 增加监听
        this._socket.on(MP_WEAPP_AUTH_SOCKET_EVENT_NAME, this._socketListener);
    }

    async fetchMpWeappInfo() {
        let res = [];
        try {
            const { mpWeappViewList = [] } = await McComponentAPI.getAuthedMpWeappUsingGET();
            res = mpWeappViewList;
        } catch (e) {
            console.error('fetchMpWeappInfo.error', e);
        }
        return res;
    }

    async fetchMpAuthUrl(from) {
        const { innerAuthUrl } = await McComponentAPI.getInnerAuthUrlUsingGET(0, from);
        return innerAuthUrl;
    }

    async fetchWeappQualify() {
        // const weappQualify = {
        //     businessLicenseUrl: '',
        //     medicalLicenseUrl: '',
        // };
        try {
            const data = await McComponentAPI.getWeappQualifyUsingGET();

            if (!data.additionalQualifies) {
                data.additionalQualifies = '';
            } else {
                data.additionalQualifies = data.additionalQualifies.join('');
            }

            // pick(weappQualify, data);
            return data;
        } catch (e) {
            console.error('fetchWeappQualify.error', e);
        }
    }

    async uploadWeappQualify(data) {
        // 上传
        await McComponentAPI.modifyWeappQualifyUsingPOST(data);
    }
    async openCreateWeappPage(data, needAuth = true) {
        try {
            // 上传
            await McComponentAPI.modifyWeappQualifyUsingPOST(data);
            if (!needAuth) return;
            // 获取微信授权页面 URL
            const { authUrl } = await McComponentAPI.getFastRegisterAuthUrlUsingGET();
            this.view.openCreateWeappAuthPage(authUrl);
        } catch (e) {
            console.error('openCreateWeappPage.error', e);
        }
    }

    /**
     * 请求公众号授权
     * @returns {Promise<void>}
     */
    async requestMpAuth(from) {
        // 显示授权弹框
        this.view.showMpAuthDialog();
        // 获取授权 URL
        const url = await this.fetchMpAuthUrl(from);
        // 界面展示该 URL 生成的二维码
        this.view.refreshMpAuthQrCode(url);
    }

    async requestWeappAuth() {
        // 显示授权弹框
        // 获取授权 URL
        try {
            const { innerAuthUrl } = await McComponentAPI.getInnerAuthUrlUsingGET(10, 'weapp');
            this.view.showWeappAuthDialog(innerAuthUrl);
        } catch (e) {
            console.log(e);
        }
    }

    // 监听 socket 的事件
    onAuthEvent(msg) {
        if (this.isDestroyed) {
            return;
        }
        const { type } = msg;
        switch (type) {
            case MpWeappAuthSocketMessage.MP_AUTH_SCAN_SUCCESS:
            // 公众号授权扫码成功
                this.onAuthMpScanSuccess();
                break;
            case MpWeappAuthSocketMessage.MP_AUTH_SUCCESS:
            // 公众号授权成功
                this.onAuthMpSuccess();
                break;
            case MpWeappAuthSocketMessage.MP_AUTH_FAIL:
            // 公众号授权失败
                this.onAuthMpFail();
                break;

            case MpWeappAuthSocketMessage.WEAPP_AUTH_SCAN_SUCCESS:
            // 小程序授权扫码成功
                this.onAuthWeappScanSuccess();
                break;
            case MpWeappAuthSocketMessage.WEAPP_AUTH_SUCCESS:
            // 小程序授权成功
                this.onAuthWeappSuccess();
                break;
            case MpWeappAuthSocketMessage.WEAPP_AUTH_FAIL:
                this.onAuthWeappFail();
                // 小程序授权失败
                break;
            case MpWeappAuthSocketMessage.WEAPP_SET_AUDIT_INFO_SUCCESS:
            // 小程序设置信息成功
                break;
            case MpWeappAuthSocketMessage.WEAPP_SET_AUDIT_INFO_FAIL:
            // 小程序设置信息失败
                break;
            default:
                break;
        }
        console.log('onAuthEvent', msg);
    }

    // 授权公众号扫描成功
    onAuthMpScanSuccess() {
        this.view.showMpAuthScaned();
    }

    // 授权公众号授权成功
    onAuthMpSuccess() {
        this.view.showMpAuthSuccess();
    }

    // 授权公众哈卡失败
    onAuthMpFail() {
        this.view.showMpAuthFailed();
    }

    onAuthWeappFail() {
        this.view.showWeappAuthFailed();
    }

    // 授权小程序扫码成功
    onAuthWeappScanSuccess() {
        this.view.showWeappAuthScaned();
    }

    // 授权小程序成功
    onAuthWeappSuccess() {
        this.view.showWeappAuthSuccess();
    }

    async destroy() {
        this.isDestroyed = true;
        this._socket.off(MP_WEAPP_AUTH_SOCKET_EVENT_NAME, this._socketListener);
        this.view = null;
    }
}
