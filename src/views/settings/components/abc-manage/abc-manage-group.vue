<template>
    <div class="abc-manage-group">
        <template v-if="title">
            <div class="abc-manage-group-title">
                <span class="title-content" :style="{ width: labelWidth }">
                    <slot name="title">
                        {{ title }}
                    </slot>
                </span>
                <span class="title-tip">
                    <slot name="tip">
                        {{ tip }}
                    </slot>
                </span>

                <div class="close-btn">
                    <slot name="close"></slot>
                </div>
            </div>
        </template>

        <slot></slot>
    </div>
</template>

<script>
    export default {
        name: 'AbcManageGroup',
        inject: {
            $abcManagePage: {
                default: {},
            },
        },
        props: {
            title: {
                type: String,
                default: '',
            },
            tip: {
                type: String,
                default: '',
            },
        },
        computed: {
            labelWidth() {
                return this.$abcManagePage.realLabelWidth;
            },
        },
    };
</script>

<style lang='scss'>
@import '@/styles/mixin.scss';

.abc-manage-group {
    & + .abc-manage-group {
        margin-top: 40px;
    }

    .abc-manage-group-title {
        display: flex;
        align-items: flex-end;
        padding-bottom: 8px;
        margin-bottom: 12px;
        border-bottom: 1px dashed $P6;

        .close-btn {
            margin-left: auto;
        }

        .title-content {
            font-size: 14px;
            font-weight: bold;
            color: $T1;

            @include ellipsis;
        }

        .title-tip {
            flex: 1;
            font-size: 12px;
            font-weight: 400;
            color: $T3;

            @include ellipsis;
        }
    }
}
</style>
