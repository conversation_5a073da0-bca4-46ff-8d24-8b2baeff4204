<template>
    <abc-dialog
        v-if="modalVisible"
        ref="abcDialog"
        v-model="modalVisible"
        :content-styles="contentStyle"
        :title="title"
        :auto-focus="false"
        append-to-body
    >
        <abc-form
            ref="projectForm"
            v-abc-loading="loading"
            label-position="left"
            :label-width="68"
            class="add-eye-project-from-wrapper"
            item-block
        >
            <h3 class="add-project-form-title">
                基础信息
            </h3>

            <div class="add-project-form-item-wrapper">
                <abc-form-item
                    ref="nameFormItemRef"
                    label="项目名称"
                    required
                    hidden-red-dot
                >
                    <abc-input
                        v-model="goods.name"
                        :disabled="isChainSubStore || goodsDisabled"
                    >
                        <span v-if="goodsDisabled" slot="append" class="disable-tag">
                            已停用
                        </span>
                    </abc-input>
                </abc-form-item>

                <template v-if="!isChainAdmin">
                    <abc-form-item label="执行科室">
                        <abc-select
                            v-model="goods.executeDepartmentId"
                            adaptive-width
                            :disabled="goodsDisabled"
                        >
                            <abc-option
                                v-for="(d,i) in departmentList"
                                :key="i"
                                v-bind="d"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </template>

                <abc-form-item label="检查类型" required hidden-red-dot>
                    <abc-select
                        :value="itemCategory"
                        placeholder="请选择检查类型"
                        disabled
                        adaptive-width
                    >
                        <abc-option
                            v-for="(o, key) in filterDeviceList"
                            :key="key"
                            :index="key"
                            :value="o.value"
                            :label="o.label"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item label="二级分类">
                    <secondary-classification-select
                        v-model="goods.customTypeId"
                        :disabled="isChainSubStore || goodsDisabled"
                        :type-id="primaryClassification.id"
                        clearable
                        adaptive-width
                    ></secondary-classification-select>
                </abc-form-item>

                <abc-form-item label="单位" class="unit-form-item">
                    <select-usage
                        v-model="goods.packageUnit"
                        :li-width="75.5"
                        :options="unitArray"
                        type="customUnit"
                        :disabled="!isAdmin || goodsDisabled"
                        @modify-options="unitEditVisible = true"
                    >
                    </select-usage>
                </abc-form-item>

                <abc-form-item label="性别限制">
                    <abc-select
                        v-model="goods.gender"
                        :disabled="isChainSubStore || goodsDisabled"
                        adaptive-width
                    >
                        <abc-option label="男" :value="1"></abc-option>
                        <abc-option label="女" :value="2"></abc-option>
                        <abc-option label="不限" :value="0"></abc-option>
                    </abc-select>
                </abc-form-item>

                <!--如果是编辑就需要强校验，因为新增的时候可以系统生成-->
                <abc-form-item
                    label="项目编码"
                    :required="editMode"
                    :error="projectCodeError"
                    hidden-red-dot
                >
                    <abc-input
                        v-model="goods.shortId"
                        class="code-input"
                        placeholder="系统生成或自定义"
                        :max-length="20"
                        type="number-en-char"
                        :disabled="!isAdmin || goodsDisabled"
                        :config="{ supportZero: true }"
                    >
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    ref="costPriceFormItemRef"
                    label="成本价"
                    :validate-event="validateCostPackagePrice"
                >
                    <abc-input
                        v-model="goods.packageCostPrice"
                        v-abc-focus-selected
                        type="money"
                        :config="{
                            formatLength: 2, supportZero: true, max: 9999999
                        }"
                        :disabled="isChainSubStore || goodsDisabled"
                    >
                        <span slot="prepend">
                            <abc-currency-symbol-icon color="#7a8794" :size="14"></abc-currency-symbol-icon>
                        </span>
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    label="售价"
                    :validate-event="validatePackagePrice"
                >
                    <abc-input
                        v-model="goods.packagePrice"
                        v-abc-focus-selected
                        type="money"
                        :config="{
                            formatLength: 2, supportZero: true, max: 9999999
                        }"
                        :disabled="isChainSubStore || goodsDisabled"
                    >
                        <span slot="prepend">
                            <abc-currency-symbol-icon color="#7a8794" :size="14"></abc-currency-symbol-icon>
                        </span>
                    </abc-input>
                </abc-form-item>
            </div>

            <h3 class="add-project-form-title">
                检查项目
            </h3>

            <single-item-table
                :data-source="currentSelectedSystemIndicators"
                @delete-item="handleDeleteItem"
            ></single-item-table>

            <div ref="addSingleBarRef" class="tfoot">
                <div class="left">
                    <abc-button
                        type="blank"
                        class="addSubBtn"
                        @click="toAddClinicalTarget"
                    >
                        添加指标
                    </abc-button>
                </div>
            </div>
        </abc-form>

        <div slot="footer" class="add-project-dialog-footer">
            <abc-button
                type="primary"
                :disabled="editMode && !contentChanged"
                :loading="btnLoading"
                @click="save"
            >
                确定
            </abc-button>

            <abc-button type="blank" @click="modalVisible = false">
                取消
            </abc-button>

            <template v-if="editMode">
                <abc-popover
                    v-if="!goodsDisabled && isAdmin"
                    class="disable-goods-select"
                    trigger="click"
                    placement="bottom-start"
                    theme="white"
                    :visible-arrow="false"
                    :popper-style="{ padding: 0 }"
                >
                    <abc-button slot="reference" type="danger">
                        停用
                    </abc-button>

                    <ul class="disable-goods-options">
                        <li @click="stop">
                            停用但保留项目资料
                        </li>

                        <li @click="del">
                            停用并删除项目资料
                        </li>
                    </ul>
                </abc-popover>

                <abc-button
                    v-if="!goodsDisabled && !isAdmin"
                    type="danger"
                    @click="stop"
                >
                    停用
                </abc-button>

                <abc-button
                    v-if="startVisible"
                    type="blank"
                    @click="start"
                >
                    启用
                </abc-button>
            </template>
        </div>

        <unit-editor
            v-if="unitEditVisible"
            v-model="unitEditVisible"
            :type="3"
            @update-unit="handleUnitEdited"
        ></unit-editor>

        <add-system-indicators-dialog
            v-if="isShowClinicalTargetDialog"
            v-model="isShowClinicalTargetDialog"
            :data-source="currentSystemIndicators"
            :item-category="Number(goods.bizExtensions.itemCategory)"
            @setSelectedSystemIndicators="handleSelectedSystemIndicators"
        >
        </add-system-indicators-dialog>
    </abc-dialog>
</template>

<script>
    import UnitEditor from 'views/settings/diagnosis-treatment/components/unit-editor.vue';
    import SelectUsage from 'views/layout/select-group/index';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';

    import { mapGetters } from 'vuex';
    import TreatmentApi from 'api/treatment';
    import { getErrorMessage } from 'views/inventory/goods/utils';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    import { DISABLED } from 'views/inventory/components/social-code-autocomplete/constant.js';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import SettingAPI from 'api/settings';
    import GoodsAPI from 'api/goods';
    import {
        INSPECT_EXTEND_ENUM,
        INSPECT_POSITION,
        INSPECT_TYPE,
        INSPECT_TYPE_ENUM,
    } from '@/views-hospital/inspect-setting/utils/constant';

    import SingleItemTable from '@/views-hospital/inspect-setting/components/single-item-table.vue';
    import AddSystemIndicatorsDialog
        from '@/views-hospital/inspect-setting/components/add-system-indicators-dialog.vue';
    import ExaminationAPI from 'api/examination';
    import { checkBeforeDisableGoods } from 'views/layout/goods-pre-operate-check';

    const SecondaryClassificationSelect = () => import('@/views/inventory/goods/components/secondary-classification/secondary-select.vue');

    const DEFAULT_PROJECT_DATA = {
        name: '', // 项目名称
        type: GoodsTypeEnum.PHYSICAL_EXAMINATION,
        subType: GoodsSubTypeEnum[GoodsTypeEnum.PHYSICAL_EXAMINATION].INSPECT,
        bizExtensions: {
            itemCategory: INSPECT_TYPE.EYE, // 检查类型
        },
        bizRelevantId: '', // 默认设备
        packagePrice: '', // 销售价格
        packageCostPrice: '', // 成本价格
        packageUnit: '', // 单位

        shortId: '', // 项目编码
        isSell: 1,
        extendSpec: INSPECT_EXTEND_ENUM.ris,
        feeComposeList: [],
        inspectionSite: undefined, // 检查部位
        disable: 0,
        // 性别限制
        gender: '',
        // 执行科室
        executeDepartmentId: '',
        deviceType: '',
        customTypeId: INSPECT_TYPE.EYE,
    };
    export default {
        name: 'AddEyeProjectDialog',

        components: {
            SingleItemTable,
            UnitEditor,
            SelectUsage,
            AddSystemIndicatorsDialog,
            SecondaryClassificationSelect,
            AbcCurrencySymbolIcon,
        },

        model: {
            prop: 'visible',
            event: 'change',
        },

        props: [
            'visible',
            'mode',
            'id',
            'deviceType',
            'departmentList',
        ],

        data() {
            return {
                goods: DEFAULT_PROJECT_DATA,
                cacheGoods: DEFAULT_PROJECT_DATA,

                DISABLED,

                customUnitList: [],
                sysUnitList: [],
                unitEditVisible: false,

                projectCodeError: { // 项目编码重复提示
                    error: false,
                    message: '',
                },

                btnLoading: false,

                inspectPositionOptions: Object.keys(INSPECT_POSITION).map((key) => {
                    return {
                        label: INSPECT_POSITION[key],
                        value: key,
                    };
                }).filter((item) => +item.value !== 0),
                loading: false,
                isShowClinicalTargetDialog: false, //临床检查 添加指标弹窗
                systemIndicators: [], //指标
                currentSelectedSystemIndicators: [],
                itemCategory: INSPECT_TYPE.EYE,
            };
        },

        computed: {
            ...mapGetters([
                'isAdmin',
                'goodsConfig',
                'currentClinic',
                'isChainSubStore',
                'isChainAdmin',
                'goodsPrimaryClassification',
            ]),

            modalVisible: {
                get() {
                    return this.visible;
                },

                set(val) {
                    this.$emit('change', val);
                },
            },

            editMode() {
                return this.mode === 'edit';
            },


            contentStyle() {
                return 'max-height: calc(100vh - 198px); padding:0;';
            },

            title() {
                if (!this.editMode) return '新增眼科检查项目';
                return '编辑眼科检查项目';
            },

            unitArray() {
                return this.sysUnitList.concat(this.customUnitList);
            },

            goodsDisabled() {
                return !!this.goods.disable;
            },

            contentChanged() {
                return !isEqual(this.goods, this.cacheGoods) || !isEqual(this.currentSelectedSystemIndicators,this.initCurrentSelectedSystemIndicators);
            },

            startVisible() {
                if (this.isChainSubStore) {
                    return this.goodsDisabled && !this.goods?.chainV2DisableStatus;
                }

                return this.goodsDisabled;
            },

            currentSystemIndicators() {
                const { itemCategory } = this.goods.bizExtensions;
                return this.systemIndicators.filter((item) => {
                    return item.deviceType === Number(itemCategory);
                }).map((item) => ({
                    ...item, checked: this.currentSelectedSystemIndicators.some((bItem) => (bItem.sourceId === item.id || bItem.id === item.id)),
                }));
            },
            /**
             * @desc 一级分类
             */
            primaryClassification() {
                return (
                    this.goodsPrimaryClassification.find((item) => {
                        return item.goodsType === GoodsTypeEnum.EXAMINATION && item.goodsSubType === this.goods.subType;
                    }) || { id: '' }
                );
            },

            filterDeviceList() {
                return [
                    {
                        value: INSPECT_TYPE.EYE,
                        label: INSPECT_TYPE_ENUM[INSPECT_TYPE.EYE],
                    },
                ];
            },
        },

        async created() {
            this.setSystemIndicators();
            this.getCustomUnit();

            if (this.mode === 'add') {
                this.goods = Clone(DEFAULT_PROJECT_DATA);
                this.cacheGoods = Clone(DEFAULT_PROJECT_DATA);
                return;
            }
            this.loading = true;
            try {
                const { data } = await SettingAPI.examination.fetchExaminationById(this.id);
                const { examGoodsInfo } = data;
                const { items } = data;

                this.currentSelectedSystemIndicators = Clone(items);
                this.initCurrentSelectedSystemIndicators = Clone(items);

                examGoodsInfo.feeComposeList = examGoodsInfo.feeComposeList || [];
                examGoodsInfo.gender = examGoodsInfo.gender ?? '';
                examGoodsInfo.executeDepartmentId = examGoodsInfo?.extendInfo?.executeDepartments?.[0]?.id || '';

                this.goods = Clone(examGoodsInfo);
                this.itemCategory = this.goods.bizExtensions.itemCategory;
                this.cacheGoods = Clone(examGoodsInfo);
            } catch (error) {
                console.log(error);
            }
            this.loading = false;
        },

        beforeDestroy() {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
        },

        methods: {
            async getCustomUnit() {
                const res = await TreatmentApi.fetchCustomUnit(GoodsTypeEnum.EXAMINATION);

                this.updateUnit(res);
            },

            updateUnit(unit) {
                const {
                    data: {
                        sysUnitList, customUnitList,
                    },
                } = unit;

                this.sysUnitList = sysUnitList || [];
                this.customUnitList = customUnitList || [];
            },

            handleUnitEdited(data) {
                this.updateUnit(data);

                if (!this.goods.packageUnit) {
                    return;
                }

                const selected = this.unitArray.find(
                    (it) => it.name === this.goods.packageUnit,
                );

                if (!selected) {
                    return;
                }

                const {
                    data: {
                        sysUnitList = [], customUnitList = [],
                    },
                } = data;
                const _unitArray = sysUnitList.concat(customUnitList);
                const updated = _unitArray.find(
                    (it) => it.id === selected.id,
                );
                if (
                    updated &&
                    this.goods.packageUnit !== updated.name
                ) {
                    this.goods.packageUnit = updated.name;
                }
                if (!updated) {
                    this.goods.packageUnit = '';
                }
            },

            save() {
                this.$refs.projectForm.validate(async (val) => {
                    if (!val) return;

                    const data = {
                        examGoodsInfo: {
                            ...this.goods,
                            extendInfo: {
                                ...(this.goods.extendInfo || {}),
                                executeDepartmentIds: [ this.goods.executeDepartmentId ],
                            },
                        },
                        items: [{}],
                    };
                    data.items = this.currentSelectedSystemIndicators;

                    if (!this.editMode) await this.post(data);
                    else await this.put(this.id, data);
                });
            },

            async stop() {
                const flag = await checkBeforeDisableGoods(this.id, this.goods.name, this);
                if (!flag) return;

                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '停用后，医嘱中不可再开出该项目，是否停用 ？',
                    onConfirm: async () => {
                        try {
                            await GoodsAPI.switchGoodsDisabled(this.id, { disable: 1 });
                            this.$Toast({
                                message: '停用成功',
                                type: 'success',
                            });

                            this.$emit('refresh-project-list');
                            this.goods.disable = 1;
                            this.cacheGoods.disable = 1;
                        } catch (error) {
                            console.error('disable error:',error);
                        }
                    },
                });
            },

            async start() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '启用后，医嘱中即可开出该项目，是否启用 ？',
                    onConfirm: async () => {
                        try {
                            await GoodsAPI.switchGoodsDisabled(this.id, { disable: 0 });
                            this.$Toast({
                                message: '启用成功',
                                type: 'success',
                            });

                            this.$emit('refresh-project-list');
                            this.goods.disable = 0;
                            this.cacheGoods.disable = 0;
                        } catch (error) {
                            console.error('disable error:',error);
                        }
                    },
                });
            },

            del() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不可恢复，但不影响已开出医嘱。是否删除 ？',
                    onConfirm: async () => {
                        await SettingAPI.examination.deleteExaminationGoods(this.id);

                        this.$Toast({
                            message: '删除成功',
                            type: 'success',
                        });

                        this.$emit('refresh-project-list');
                        this.modalVisible = false;
                    },
                });
            },

            async put(id, data) {
                try {
                    this.btnLoading = true;

                    await SettingAPI.examination.updateExaminationGoods(
                        id,
                        data,
                    );

                    this.timer = setTimeout(() => {
                        this.$Toast({
                            type: 'success',
                            message: '修改成功',
                        });

                        this.modalVisible = false;

                        this.$emit('refresh-project-list');
                    }, 2500);
                } catch (e) {
                    this.projectCodeError = getErrorMessage(e, '项目');
                    this.btnLoading = false;
                }
            },

            async post(data) {
                try {
                    this.btnLoading = true;

                    await SettingAPI.examination.createExaminationGoods(data);

                    this.timer = setTimeout(() => {
                        this.$Toast({
                            type: 'success',
                            message: '新建成功',
                        });

                        this.modalVisible = false;

                        this.$emit('refresh-project-list');
                    }, 2500);
                } catch (e) {
                    this.projectCodeError = getErrorMessage(e, '项目');
                    this.btnLoading = false;
                }
            },

            toAddClinicalTarget() {
                this.isShowClinicalTargetDialog = true;
            },

            async setSystemIndicators() {
                const examinationGoodsId = this.mode === 'add' ? null : this.id;
                try {
                    const res = await ExaminationAPI.getSystemIndicators(
                        examinationGoodsId,null,GoodsTypeEnum.EXAMINATION,GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test,
                    );
                    this.systemIndicators = res.data.rows;
                } catch (e) {
                    console.log(e);
                }
            },
            handleSelectedSystemIndicators(data) {
                this.currentSelectedSystemIndicators = data;
            },
            handleDeleteItem(idx) {
                this.currentSelectedSystemIndicators.splice(idx,1);
            },

            // 校验成本价
            validateCostPackagePrice(val, callback) {
                const isValid = (_v) => ![undefined, null, ''].includes(_v);
                const v = +val;

                if (isValid(this.salePrice)) {
                    if (+v > +this.salePrice) {
                        return callback({
                            validate: false,
                            message: '成本价不能大于销售价',
                        });
                    }
                }
                callback({
                    validate: true,
                });
            },

            // 校验销售价
            validatePackagePrice(val, callback) {
                if (val === undefined || val === '') {
                    return callback({
                        validate: false,
                        message: '售价不能为空',
                    });
                }

                this.$refs.costPriceFormItemRef.validate();

                return callback({
                    validate: true,
                });
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.add-eye-project-from-wrapper {
    width: 666px;
    padding: 24px;

    .add-project-form-item-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 16px 24px;
        justify-content: space-between;
        margin-bottom: 40px;

        .abc-form-item {
            width: calc(calc(100% - 24px) / 2);
            margin: 0;

            .abc-input-wrapper {
                width: 100%;
            }

            .abc-input__inner {
                width: 100%;
                text-align: left !important;
            }
        }
    }

    .add-project-form-title {
        margin-bottom: 16px;
        font-weight: bold;
        color: $T1;
    }

    .disable-tag {
        color: $R2;
    }

    .unit-form-item {
        .abc-input__inner {
            height: 32px;
        }
    }

    .abc-form-item {
        margin-bottom: 16px;
    }
}

.add-project-dialog-footer {
    display: flex;
    justify-content: flex-end;

    .disable-goods-select {
        color: #ff3333;

        .cis-icon-dropdown_triangle {
            right: -14px;
            color: #ff3333;
        }
    }

    .disable-goods-options {
        padding: 2px 0;

        li {
            display: flex;
            align-items: center;
            height: 34px;
            padding: 8px;
            font-size: 14px;
            color: #000000;
            text-align: center;
            cursor: pointer;

            &:hover {
                background-color: $P4;
            }
        }
    }
}

.add-project-set-footer {
    justify-content: space-between !important;
}

.tfoot {
    margin-top: 12px;
}
</style>
