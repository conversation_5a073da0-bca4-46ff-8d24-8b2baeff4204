<template>
    <biz-fill-remain-height class="physical-examination-setting-wrapper">
        <template #header>
            <abc-manage-tabs :option="tabsOption" :value="selectedTab" @change="handleTabsChange"></abc-manage-tabs>
        </template>
        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script type="text/ecmascript-6">
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import AbcAccess from '@/access/utils';
    import { mapGetters } from 'vuex';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';

    export default {
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
        data() {
            return {
                selectedTab: '',
            };
        },
        computed: {
            ...mapGetters([
                'isChainSubStore',
                'isSingleStore',
            ]),
            tabsOption () {
                return [
                    {
                        label: '检验',
                        value: '@physical-project-examination',
                        visible: AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION),
                    },
                    {
                        label: '检查',
                        value: '@physical-project-inspect',
                        visible: AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION),
                    },
                    {
                        label: '其他费用',
                        value: '@physical-project-other',
                        visible: AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION),
                    },
                    {
                        label: '套餐',
                        value: '@physical-project-compose',
                        visible: AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION),
                        separation: true,
                    },
                    {
                        label: '总评建议设置',
                        value: '@physical-examination-assessment-opinion',
                        visible: AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION) &&
                            (this.isChainSubStore || this.isSingleStore),
                    },
                    {
                        label: '总评报告模板设置',
                        value: '@physical-examination-assessment-template',
                        extend: [
                            '@physical-examination-assessment-template-individual',
                            '@physical-examination-assessment-template-public-healthy',
                        ],
                        visible: AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION) &&
                            (this.isChainSubStore || this.isSingleStore),
                        separation: true,
                    },
                    {
                        label: '体检流程设置',
                        value: '@physical-examination-set-public-health',
                        visible: AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION) &&
                            (this.isChainSubStore || this.isSingleStore),
                        separation: true,
                    },
                    {
                        label: '体检系统介绍', value: '@physicalExaminationProductIntroduce',visible: true,
                    },
                ].filter((item) => item.visible);
            },
        },
        watch: {
            '$route': {
                handler() {
                    this.findSelectedTab();
                },
                immediate: true,
            },
        },
        mounted() {
            this.findSelectedTab();
        },
        methods: {
            handleTabsChange(value) {
                this.selectedTab = value;
                this.$router.push({
                    name: value,
                });
            },
            findSelectedTab() {
                this.tabsOption.forEach((item) => {
                    if (item.value === this.$route.name || (item.extend && item.extend.includes(this.$route.name))) {
                        this.selectedTab = item.value;
                    }
                });
            },
        },
    };
</script>

<style lang="scss">
.physical-examination-setting-wrapper {
    height: 100%;
}
</style>
