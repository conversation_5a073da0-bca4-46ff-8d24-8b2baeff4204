<template>
    <biz-setting-layout v-abc-loading="loading" class="print-config-wrapper">
        <biz-setting-content>
            <abc-form ref="printForm" item-no-margin>
                <biz-setting-form :label-width="70">
                    <biz-setting-form-header>
                        <assessment-template-tabs></assessment-template-tabs>
                    </biz-setting-form-header>

                    <biz-setting-form-group title="体检报告封面设置">
                        <biz-setting-form-item label="机构名称" label-line-height-size="medium">
                            <abc-form-item required class="print-form-item" :validate-event="validateName">
                                <title-setting v-model="postData.cover.institutionName" :max-length="titleMaxLength * 2"></title-setting>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="机构Logo" label-line-height-size="medium">
                            <biz-setting-form-item-tip tip="Logo图片可用于导检单、体检报告打印，建议白色或透明背景">
                                <setting-set-logo
                                    v-model="postData.cover.institutionLogoUrl"
                                    file-path="physical-examination-print-logo"
                                    width="100px"
                                    height="100px"
                                    :cut-picture-option="{
                                        fixed: false,
                                        fixedNumber: [1,1],
                                        autoCropWidth: 500,
                                        autoCropHeight: 500,
                                    }"
                                ></setting-set-logo>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="报告标题" label-line-height-size="medium" has-divider>
                            <abc-flex :gap="12" vertical>
                                <abc-form-item required :validate-event="validateName">
                                    <title-setting v-model="postData.cover.mainTitle" :max-length="30"></title-setting>
                                </abc-form-item>

                                <abc-form-item required class="print-form-item" :validate-event="validateName">
                                    <title-setting v-model="postData.cover.subTitle" :max-length="30"></title-setting>
                                </abc-form-item>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="体检人信息" has-divider>
                            <abc-flex wrap="wrap" style="row-gap: 12px;">
                                <abc-checkbox
                                    v-model="postData.cover.personInfo.basicInfo"
                                    disabled
                                    type="number"
                                    style="width: 25%;"
                                >
                                    受检者
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.cover.personInfo.peDate" style="width: 25%;" type="number">
                                    体检日期
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.cover.personInfo.peOrganName" style="width: 25%;" type="number">
                                    体检单位
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.cover.personInfo.peOrderNo" style="width: 25%;" type="number">
                                    体检单号
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.cover.personInfo.reportDate" style="width: 25%;" type="number">
                                    报告日期
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.cover.personInfo.mobile" style="width: 25%;" type="number">
                                    联系手机
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.cover.personInfo.company" style="width: 25%;" type="number">
                                    所属单位
                                </abc-checkbox>

                                <abc-checkbox v-model="postData.cover.personInfo.address" style="width: 25%;" type="number">
                                    联系地址
                                </abc-checkbox>
                            </abc-flex>

                            <abc-space size="middle">
                                <abc-space>
                                    <label>横向位置</label>
                                    <abc-input-number
                                        v-model="postData.cover.personInfo.xAxis"
                                        size="tiny"
                                        button-placement="top"
                                        :width="72"
                                        :config="{
                                            min: 0,
                                            max: 46,
                                            supportZero: true,
                                        }"
                                        @change="v => postData.cover.personInfo.xAxis = v"
                                    >
                                        <abc-icon slot="icon-minus" icon="dropdown_triangle"></abc-icon>
                                        <abc-icon slot="icon-plus" icon="dropdown_triangle_up"></abc-icon>
                                    </abc-input-number>
                                </abc-space>

                                <abc-space>
                                    <label>纵向位置</label>
                                    <abc-input-number
                                        v-model="postData.cover.personInfo.yAxis"
                                        size="tiny"
                                        button-placement="top"
                                        :width="72"
                                        :config="{
                                            min: 0,
                                            max: 40,
                                            supportZero: true,
                                        }"
                                        @change="v => postData.cover.personInfo.yAxis = v"
                                    >
                                        <abc-icon slot="icon-minus" icon="dropdown_triangle"></abc-icon>
                                        <abc-icon slot="icon-plus" icon="dropdown_triangle_up"></abc-icon>
                                    </abc-input-number>
                                </abc-space>
                            </abc-space>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="联系方式" label-line-height-size="medium">
                            <abc-form-item required class="print-form-item">
                                <title-setting v-model="postData.cover.institutionContact" :max-length="30"></title-setting>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="地址" label-line-height-size="medium">
                            <abc-form-item required class="print-form-item">
                                <title-setting v-model="postData.cover.institutionAddress" :max-length="titleMaxLength * 2"></title-setting>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="二维码">
                            <abc-checkbox
                                v-model="postData.cover.qrCode"
                                :disabled="!isOpenMp"
                                type="number"
                                class="checkbox-no-margin"
                            >
                                微医院二维码
                            </abc-checkbox>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="首页风格" label-line-height-size="medium">
                            <select-cover v-model="postData.cover.background"></select-cover>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="导读">
                        <biz-setting-form-item label="导读内容" label-line-height-size="medium">
                            <abc-textarea
                                v-model="postData.introduction.content"
                                :height="200"
                                :width="614"
                                placeholder="输入导读内容"
                                @focus="handleChangeTab(1)"
                            >
                            </abc-textarea>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="异常图示">
                            <abc-checkbox v-model="postData.introduction.abnormalImage" type="number">
                                异常图示
                            </abc-checkbox>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="正文">
                        <biz-setting-form-item label="总检总评">
                            <abc-radio-group v-model="postData.content.mainComments">
                                <abc-radio :label="0">
                                    不填写时，体检报告不展示总检总评标题
                                </abc-radio>
                                <abc-radio :label="1">
                                    不填写时，体检报告展示总检总评标题
                                </abc-radio>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button :loading="btnLoading" :disabled="!isUpdate" @click="handleSave">
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar>
            <preview-layout
                class="is-a4-preview-layout is-a4-preview-layout-more-padding-15"
                :page-style="coverStyle"
            >
                <template slot="previewTab">
                    <div
                        class="preview-tab-item"
                        :class="{ 'preview-tab-item__active': tabIndex === 0 }"
                        @click="handleChangeTab(0)"
                    >
                        体检报告
                    </div>
                    <div
                        class="preview-tab-item"
                        :class="{ 'preview-tab-item__active': tabIndex === 1 }"
                        @click="handleChangeTab(1)"
                    >
                        导读
                    </div>
                </template>

                <div
                    slot="previewHtml"
                    ref="previewMountPoint"
                ></div>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script type="text/ecmascript-6">
    import TitleSetting from 'views/settings/print-config/components/title-setting.vue';
    import PreviewLayout from 'views/settings/print-config/components/preview-layout.vue';
    import { validateName } from '../../utils';
    import AbcPrinter from '@/printer';
    import SelectCover from '../components/select-cover.vue';
    import PrintAPI from 'api/print';
    import { clone } from '@abc/utils';
    import { mapGetters } from 'vuex';
    import {
        PE_REPORT_EXAMPLE_DATA,
    } from './constant';
    import { isEqual } from 'utils/lodash';
    import PreAbcPrint from '@/printer/index-v2/pre-print-handler';

    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
        BizSettingFormHeader,
    } from '@/components-composite/setting-form/index.js';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
        BizSettingSidebar,
    } from '@/components-composite/setting-form-layout/index.js';
    import AssessmentTemplateTabs
        from 'views/settings/physical-examination/assessment-set/assessment-template/components/assessment-template-tabs.vue';
    import SettingSetLogo from 'views/settings/components/setting-set-logo/index.vue';

    const ResetData = {
        cover: {
            mainTitle: '健康体检报告',//报告封面主标题
            subTitle: 'HEALTH EXAMINATION REPORT',//报告封面副标题
            institutionLogoUrl: '',
            institutionName: '',
            personInfo: {
                basicInfo: 1,//受检者  可选值：0 1 默认值 1
                peDate: 1,//体检日期 可选值：0 1 默认值 1
                peOrganName: 1,//体检单位 可选值：0 1 默认值 1
                peOrderNo: 1,//体检单号 可选值：0 1 默认值 1
                reportDate: 1,//报告日期 可选值：0 1 默认值 1
                mobile: 1,//联系手机 可选值：0 1 默认值 1
                company: 1,//所属单位 可选值：0 1 默认值 1
                address: 1,//联系地址 可选值：0 1 默认值 1
                xAxis: 23,//横向位置 默认值 23
                yAxis: 20,//纵向位置 默认值 20
            },
            institutionContact: '', //联系方式
            institutionAddress: '', //地址
            qrCode: 1,//二维码 可选值：0 1 默认值 1
        },
        introduction: {
            content: '',//报告导读
            abnormalImage: 1, //异常图示 可选值：0 1 默认值 1
        },
        content: {
            mainComments: 0,
        },
    };

    export default {
        name: 'AssessmentTemplate',

        components: {
            SettingSetLogo,
            PreviewLayout,
            TitleSetting,
            SelectCover,

            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingSidebar,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
            BizSettingFormHeader,
            AssessmentTemplateTabs,
        },

        data() {
            return {
                postData: clone(ResetData),
                postDataCache: clone(ResetData),
                titleMaxLength: 50, // 标题最大长度
                loading: false,
                btnLoading: false,

                printInstance: null,

                tabIndex: 0,
            };
        },

        computed: {
            ...mapGetters(['printGlobalConfig', 'isOpenMp']),

            coverStyle() {
                if (this.tabIndex === 0) {
                    return {
                        background: `url(${this.postData.cover.background}) no-repeat center`,
                        backgroundSize: 'cover',
                    };
                }
                return {};

            },

            isUpdate() {
                return !isEqual(this.postData, this.postDataCache);
            },
        },

        watch: {
            postData: {
                handler(newPostData) {
                    this.$nextTick(() => {
                        this.updatePreview(newPostData);
                    });
                },
                deep: true,
            },
        },

        async created() {
            AbcPrinter.setGlobalConfig();
            PreAbcPrint.setGlobalConfig();
            await this.fetchData();
        },

        beforeDestroy() {
            this.destroyPrintInstanceStyle();
            this.$destroyed = true;
        },

        methods: {
            validateName,

            async fetchData() {
                try {
                    this.loading = false;
                    const data = await this.$store.dispatch('fetchPrintPEPublicHealthyReportConfig');
                    data.content = data.content || { mainComments: 0 };
                    this.postData = clone(data);
                    this.postDataCache = clone(this.postData);
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },

            handleSave() {
                this.$refs.printForm.validate(async (val) => {
                    if (val) {
                        await this.updatePrintConfig();
                    }
                });
            },

            async updatePrintConfig() {
                try {
                    this.btnLoading = true;
                    this.postData = this.convertPostData(this.postData);
                    const { data } = await PrintAPI.updatePrintConfig('clinic', 'pe.publicHealthReport', {
                        publicHealthReport: this.postData,
                    });
                    this.$store.commit('SET_PRINT_PUBLIC_HEALTH_PE_REPORT_CONFIG', data);
                    this.btnLoading = false;
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.postDataCache = clone(this.postData);
                } catch (e) {
                    this.btnLoading = false;
                }
            },

            getCurrentTemplate() {
                return window.AbcPackages.AbcTemplates.peIndividualReport;
            },

            // 更新打印预览界面
            async updatePreview(newPostData) {
                await this.mountPrintInstance();
                // 更新预览界面
                await this.updateInstanceGlobalConfig(newPostData);
            },

            async mountPrintInstance() {
                if (this.printInstance) return;

                try {
                    const { peIndividualReport } = window.AbcPackages.AbcTemplates;

                    // 实例化打印实例
                    const printInstance = new window.AbcPackages.AbcPrint({
                        template: peIndividualReport,
                        page: {
                            size: 'A4',
                            orientation: window.AbcPackages.AbcPrint.Orientation.portrait,
                            pageSizeReduce: {
                                top: 15,
                                bottom: 15,
                                right: 15,
                                left: 15,
                            },
                        },
                        originData: Object.assign(PE_REPORT_EXAMPLE_DATA, {
                            qrCodeUrl: this.isOpenMp ? PE_REPORT_EXAMPLE_DATA.qrCodeUrl : '',
                        }),
                        extra: {
                            isPreview: true,
                            isPreviewCover: this.tabIndex === 0,
                            isPreviewIntroDuction: this.tabIndex === 1,
                        },
                    });

                    // 初始化打印实例
                    await printInstance.init();

                    this.destroyPrintInstanceStyle();
                    if (!this.$destroyed) {
                        this._destroyStyle = printInstance.loadInstanceStyle();
                    }

                    // 把打印实例中解析的html挂载到当前页面
                    this.$refs.previewMountPoint.innerHTML = '';
                    this.$refs.previewMountPoint.appendChild(printInstance.instance.$el);
                    this.printInstance = printInstance;
                } catch (e) {
                    console.error('AbcPrint挂载失败', e);
                }
            },

            async updateInstanceGlobalConfig(newPostData) {
                const newInstanceGlobalConfig = clone(this.printGlobalConfig);
                newInstanceGlobalConfig.publicHealthReport = newPostData;
                if (this.printInstance) {
                    await this.printInstance.updateInstanceGlobalConfig(newInstanceGlobalConfig);
                    await this.printInstance.updateDatahandlerAndInstance();
                }
            },

            destroyPrintInstanceStyle() {
                this._destroyStyle?.();
                this._destroyStyle = null;
            },

            handleChangeTab(tabIndex) {
                if (this.tabIndex === tabIndex) return;
                this.tabIndex = tabIndex;
                this.printInstance = null;
                this.updatePreview(this.postData);
            },

            convertPostData(data) {
                data.cover.personInfo.xAxis = Number(data.cover.personInfo.xAxis);
                data.cover.personInfo.yAxis = Number(data.cover.personInfo.yAxis);

                return data;
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "~styles/theme.scss";

.print-config-wrapper {
    flex: 1;

    .pc-print-config-content-wrapper {
        height: 100%;
        padding: 0 0 24px 24px;

        .assessment-print-config-wrapper {
            main {
                .print-config-content {
                    margin-top: 0;

                    .abc-form {
                        width: 606px;
                        padding-top: 14px;

                        .print-config-items {
                            .patient-group-item {
                                .abc-checkbox-wrapper {
                                    min-width: 80px;
                                    margin-right: 16px;
                                }
                            }
                        }

                        .desc {
                            margin-top: 8px;
                            font-size: 12px;
                            color: $T3;
                        }
                    }
                }
            }
        }
    }
}
</style>
