<template>
    <div v-abc-loading.small="chargeSheetItem && chargeSheetItem.loading" class="outpatient__consulting-online__message-list__item-confirm consulting-online-message-card">
        <div class="card-body">
            <div class="title">
                <h5>治疗方案</h5>
            </div>
            <div class="desc">
                <section class="content-item">
                    <div class="label">
                        患者：
                    </div>
                    <div class="content">
                        {{ patientInfo.name }}  {{ patientInfo.sex || '' }}  {{ patientInfo.age | formatAge }}
                    </div>
                </section>

                <section class="content-item">
                    <div class="label">
                        诊断：
                    </div>
                    <div class="content">
                        {{ diagnosis }}
                    </div>
                </section>

                <section class="content-item">
                    <div class="label">
                        治疗：
                    </div>
                    <div class="content">
                        {{ planDetail }}
                    </div>
                </section>
            </div>
        </div>

        <div class="cut-line"></div>
        <div class="card-footer">
            <abc-icon
                v-if="showStatus.iconName"
                :icon="showStatus.iconName"
                :color="showStatus.iconColor"
                size="12"
            ></abc-icon>
            <span class="text">{{ showStatus.text }}</span>
        </div>
    </div>
</template>

<script>
    import { mapActions, mapState } from 'vuex';
    import { getChargeSheetDesc, getCountDesc } from 'views/outpatient/consulting-online/utils.js';
    export default {
        name: 'ItemConfirm',
        props: {
            info: {
                type: Object,
                required: true,
            },
        },
        created () {
            const { chargeSheetId } = this.info;
            if (chargeSheetId) {
                this.acPushFetchChargeSheetTask(chargeSheetId);
            }
        },
        computed: {
            ...mapState('im', [
                'chargeSheetList',
            ]),
            chargeSheetItem() {
                return this.chargeSheetList.find((item) => item.chargeSheetId === this.info.chargeSheetId);
            },
            // 患者信息
            patientInfo() {
                if (this.chargeSheetItem && this.chargeSheetItem.detail) {
                    const { patient } = this.chargeSheetItem.detail;
                    return patient;
                }
                return {};

            },
            diagnosis() {
                if (this.chargeSheetItem && this.chargeSheetItem.detail && this.chargeSheetItem.detail.medicalRecord) {
                    return this.chargeSheetItem.detail.medicalRecord.diagnosis;
                }
                return '';
            },
            planDetail() {
                if (this.chargeSheetItem && this.chargeSheetItem.detail && this.chargeSheetItem.detail.chargeForms) {
                    const obj = getChargeSheetDesc(this.chargeSheetItem.detail.chargeForms);
                    return getCountDesc(obj);
                }
                return '';
            },
            checkStatus() {
                return (this.chargeSheetItem && this.chargeSheetItem.detail) ?
                    this.chargeSheetItem.detail.checkStatus :
                    0;
            },
            //checkStatus => 0-等待用户支付，1-需要用户补充信息，如快递费，2-诊所审核快递信息，也叫等待划价
            showStatus() {
                switch (this.checkStatus) {
                    case 1:
                        return {
                            text: '等待患者确认',
                            active: false,
                        };
                    default:
                        return {
                            text: '订单已确认',
                            iconName: 'chosen',
                            iconColor: '#1EC761',
                            active: true,
                        };
                }
            },
        },
        methods: {
            ...mapActions('im', [
                'acPushFetchChargeSheetTask',
            ]),
        },
    };
</script>
