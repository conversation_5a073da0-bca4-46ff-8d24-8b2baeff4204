<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="调用模板"
        content-styles="width: 360px; height: 140px; padding: 24px;"
        custom-class="template-use-method-dialog"
        append-to-body
        @click.stop.prevent=""
    >
        <div
            class="dialog-content"
            @click.stop.prevent=""
        >
            <div>
                已填写 {{ diffNameArrStr }}
            </div>
            <p style="color: #000000;">
                插入到已填写内容中，还是覆盖已填写内容？
            </p>
        </div>
        <div slot="footer" class="dialog-footer">
            <abc-button type="primary" style="width: 80px; min-width: 80px;" @click.stop.prevent="handleClickAppend">
                插入
            </abc-button>
            <abc-button type="primary" style="width: 80px; min-width: 80px;" @click.stop.prevent="handleClickOverwrite">
                覆盖
            </abc-button>
            <abc-button type="blank" style="width: 80px; min-width: 80px;" @click.stop.prevent="handleClose">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    export default {
        props: {
            diffNameArr: {
                type: Array,
                required: true,
            },
            onAppend: {
                type: Function,
                required: true,
            },
            onOverwrite: {
                type: Function,
                required: true,
            },
        },

        data() {
            return {
                visible: false,
            };
        },
        computed: {
            diffNameArrStr() {
                return this.diffNameArr.map((item) => `"${item}"`).join(' ');
            },
        },
        methods: {
            // 直接添加
            handleClickAppend() {
                this.onAppend && this.onAppend();
                this.handleClose();
            },
            handleClickOverwrite() {
                this.onOverwrite && this.onOverwrite();
                this.handleClose();
            },
            handleClose() {
                this.visible = false;
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common';

    .template-use-method-dialog {
        z-index: 10001;

        .dialog-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;

            div {
                display: flex;
                align-items: flex-start;
                margin-bottom: 8px;

                span {
                    @include ellipsis(1);

                    display: inline-block;
                    max-width: 240px;
                    margin: 0 3px;
                    font-size: 16px;
                }
            }
        }
    }
</style>
