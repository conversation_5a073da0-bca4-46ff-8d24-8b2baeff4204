import { mapGetters } from 'vuex';
import infectiousDiseases from 'src/assets/configure/infectious-diseases';
import clone from '@/utils/clone';

export default {
    watch: {
        // 监听传染病监控开关
        clinicBasic: {
            handler(val) {
                this.handleInfectiousDiseaseSwitch(val);
            },
            immediate: true,
            deep: true,
        },
    },
    computed: {
        ...mapGetters(['clinicBasic']), // 获取配置项
    },
    methods: {
        /**
         * 监听诊断结果,筛选出疾病列表
         */
        handleDiagnosisList(val) {
            this.diagnosisList =
                Array.isArray(val) && val.length && Array.isArray(val[0].value) && val[0].value.length ?
                    val[0].value.map((item) => item.name) :
                    [];
        },
        /**
         * 监听多条诊断结果,筛选出疾病列表
         */
        handleMultiDiagnosisList(val) {
            const tempList = [];
            if (Array.isArray(val) && val.length) {
                val.forEach((item, index) => {
                    if (Array.isArray(item.value)) {
                        tempList[index] = [];
                        item.value.forEach((ele) => {
                            tempList[index].push(ele.name);
                        });
                    }
                });
            }
            this.diagnosisList = clone(tempList);
        },
        /**
         * 监听诊断结果,筛选出传染病
         */
        handleInfectiousDiseaseList(val) {
            let flag = false; // 诊断中是否有传染病
            const result = {
                classA: [], classB: [], classC: [],
            }; // 诊断中的传染病列表

            const infectedDiseaseGrade = Object.keys(infectiousDiseases);
            infectedDiseaseGrade.forEach((item) => {
                infectiousDiseases[item].forEach((ele) => {
                    const resDiagnosis = val.find((diagnosis) => diagnosis === ele);
                    if (resDiagnosis) {
                        flag = true;
                        result[item].push(resDiagnosis);
                    }
                });
            });
            this.isShowInfectedDiseaseWarn = flag;
            this.infectedDiseaseDiagnosis = clone(result);
        },
        /**
         * 监听多条诊断结果,筛选出传染病
         */
        handleMultiInfectiousDiseaseList(val) {
            let flag = false; // 诊断中是否有传染病
            let result = {
                classA: [], classB: [], classC: [],
            }; // 诊断中的传染病列表

            const infectedDiseaseGrade = Object.keys(infectiousDiseases);
            if (Array.isArray(val) && val.length) {
                val.forEach((it, index) => {
                    if (it) {
                        infectedDiseaseGrade.forEach((item) => {
                            infectiousDiseases[item].forEach((ele) => {
                                const resDiagnosis = it.find((diagnosis) => diagnosis === ele);
                                if (resDiagnosis) {
                                    flag = true;
                                    result[item].push(resDiagnosis);
                                }
                            });
                        });
                    }
                    this.isShowInfectedDiseaseWarn[index] = flag;
                    this.infectedDiseaseDiagnosisList[index] = clone(result);
                    flag = false;
                    result = {
                        classA: [], classB: [], classC: [],
                    };
                });
            }
        },
        /**
         * 获取传染病监控开关
         */
        handleInfectiousDiseaseSwitch(val) {
            this.isOnInfectiousDiseaseWarn = Boolean(val.outpatient?.settings?.infectiousDiseases);
        },
    },
};
