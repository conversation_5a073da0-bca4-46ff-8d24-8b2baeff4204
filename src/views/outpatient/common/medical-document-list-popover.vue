<template>
    <abc-popover
        width="auto"
        placement="bottom-start"
        trigger="click"
    >
        <span slot="reference">
            <slot></slot>
        </span>
        <div class="medical-document-list-popover">
            <div v-for="group in medicalDocumentTree" :key="group.id">
                <div class="medical-document-list-popover_title">
                    {{ group.name }}
                </div>
                <div class="medical-document-list-popover_list">
                    <div
                        v-for="item in group.medicalViews"
                        :key="item.id"
                        class="medical-document-list-popover_list-item ellipsis"
                        :class="{ 'medical-document-list-popover_list-item--disabled': item.hasEdited }"
                        @click="handleClickMedicalDocument(item)"
                    >
                        <img
                            v-if="!item.hasEdited"
                            class="medical-document-file-icon"
                            src="~assets/images/hospital/file-empty.png"
                            alt="文书图标"
                        />
                        <img
                            v-else
                            class="medical-document-file-icon"
                            src="~assets/images/hospital/file-empty-disable.png"
                            alt="文书图标禁用"
                        />
                        {{ item.name }}
                    </div>
                </div>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    export default {
        name: 'MedicalDocumentListPopover',
        props: {
            medicalDocumentTree: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        methods: {
            handleClickMedicalDocument(item) {
                if (item.hasEdited) {
                    return;
                }
                this.$emit('select', item);
            },
        },
    };
</script>

<style lang="scss">
@import "~styles/theme.scss";

.medical-document-list-popover {
    width: 438px;
    padding: 16px 16px 12px;
    background: $S2;
    border: 1px solid #d9dbe3;
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 2px 8px 0 #00000026;

    .medical-document-list-popover_title {
        height: 20px;
        margin-bottom: 10px;
        font-size: 14px;
        line-height: 20px;
        color: $T2;
    }

    .medical-document-list-popover_list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .medical-document-list-popover_list-item {
            display: flex;
            align-items: center;
            width: 200px;
            height: 32px;
            padding: 6px 8px;
            margin-bottom: 4px;
            line-height: 20px;
            cursor: pointer;
            border-radius: var(--abc-border-radius-small);

            .medical-document-file-icon {
                height: 14px;
                margin-right: 4px;
            }

            &:hover {
                background: $P4;
            }
        }

        .medical-document-list-popover_list-item--disabled {
            color: $T3;
            cursor: not-allowed;

            &:hover {
                background: initial;
            }
        }
    }
}
</style>
