<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        custom-class="diagnosis-treatment-template-dialog"
        content-styles="padding:0;width: 1000px;height: 656px"
    >
        <hospital-template-manager
            v-if="version === 1"
            :owner-type="CATALOGUE_FILE_OWNER_TYPE.PERSONAL"
            :template-scene-type="TemplateSceneTypeEnum.DIAGNOSIS_TREATMENT"
            :bordered="false"
            @close="showDialog = false"
            @useTemplate="useTemplate"
        ></hospital-template-manager>
        <template-manager
            v-else
            :owner-type="3"
            template-type="diagnosis-treatment"
            :bordered="false"
            @close="showDialog = false"
            @useTemplate="useTemplate"
        ></template-manager>
    </abc-dialog>
</template>

<script>
    import {
        TemplateSceneTypeEnum, CATALOGUE_FILE_OWNER_TYPE,
    } from '@/utils/constants';

    export default {
        name: 'DiagnosisTreatmentTemplateDialog',

        components: {
            TemplateManager: () => import('src/views/layout/templates-manager/index'),
            HospitalTemplateManager: () => import('views/layout/templates-manager/hospital/index.vue'),
        },

        props: {
            value: Boolean,
            version: {
                type: Number,
                default: 0,
            },
        },

        data() {
            return {
                TemplateSceneTypeEnum,
                CATALOGUE_FILE_OWNER_TYPE,
            };
        },

        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },

        methods: {
            useTemplate(data) {
                this.showDialog = false;
                this.$emit('useTemplate', data);
            },
        },
    };
</script>
