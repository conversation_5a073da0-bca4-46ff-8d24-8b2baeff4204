<template>
    <abc-dialog
        v-model="showDialog"
        append-to-body
        content-styles="padding: 24px; width: 624px;"
        title="记录监护人"
        class="guardian-record-dialog"
    >
        <div class="guardian-record-dialog__desc">
            就诊患者年龄小于 6 岁，请登记监护人信息
        </div>

        <div
            class="content-wrapper medical-record-wrapper"
            :class="{
                'is-fixed': fixed
            }"
        >
            <abc-form ref="patientGuardianRef">
                <div class="medical-record-item">
                    <div
                        class="patient-guardian-first-label"
                    >
                        监护人身份
                    </div>
                    <abc-form-item required>
                        <abc-select
                            v-model="patientGuardian.relationShip"
                            :width="464"
                            no-icon
                            :input-style="{
                                borderRadius: 0,
                            }"
                        >
                            <abc-option
                                v-for="relation in relationShipList"
                                :key="relation.label"
                                :label="relation.label"
                                :value="relation.value"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </div>
                <div class="medical-record-item">
                    <label style="width: 110px;">监护人姓名</label>
                    <abc-form-item required>
                        <abc-edit-div
                            v-model="patientGuardian.name"
                            spellcheck="false"
                            :maxlength="40"
                            data-cy="abc-mr-监护人姓名"
                        ></abc-edit-div>
                    </abc-form-item>
                </div>
                <div class="medical-record-item">
                    <label style="width: 110px;">监护人手机号</label>
                    <abc-form-item required :validate-event="validateMobile">
                        <abc-edit-div
                            v-model="patientGuardian.mobile"
                            spellcheck="false"
                            :maxlength="40"
                            data-cy="abc-mr-监护人手机号"
                        ></abc-edit-div>
                    </abc-form-item>
                </div>
            </abc-form>
        </div>
        <div slot="footer" class="dialog-footer">
            <abc-button style="margin-left: auto;" @click="confirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>
<script>
    import { validateMobile } from 'utils/validate';
    export default {
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            fixed: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                patientGuardian: {
                    name: '',
                    mobile: '',
                    relationShip: '',
                },

                relationShipList: [
                    {
                        label: '父亲',
                        value: 1,
                    },
                    {
                        label: '母亲',
                        value: 2,
                    },
                    {
                        label: '爷爷',
                        value: 3,
                    },
                    {
                        label: '奶奶',
                        value: 4,
                    },
                    {
                        label: '外公',
                        value: 5,
                    },
                    {
                        label: '外婆',
                        value: 6,
                    },
                    {
                        label: '其他',
                        value: 7,
                    },
                ],

            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        methods: {
            validateMobile,
            confirm() {
                this.$refs.patientGuardianRef.validate((valid) => {
                    if (valid) {
                        const payload = {
                            name: this.patientGuardian.name,
                            relationShip: this.patientGuardian.relationShip,
                            relationName: this.relationShipList.find((item) => item.value === this.patientGuardian.relationShip)?.label,
                            mobiles: [this.patientGuardian.mobile],

                        };
                        this.$emit('patient-guardian-change', payload);
                        this.showDialog = false;
                    }
                });
            },
        },
    };
</script>

<style lang="scss">
.guardian-record-dialog {
    &__desc {
        margin-bottom: 10px;
    }
}
</style>
