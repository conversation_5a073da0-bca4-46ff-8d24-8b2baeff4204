import ImportExcelDialog from 'views/inventory/goods-in/components/import-excel-dialog/import-excel-dialog.vue';
import ImportGoodsDialog from 'views/inventory/goods-in/components/import-goods-dialog/index.vue';
import ImportParseHeaderDialog
    from 'views/inventory/goods-in/components/import-excel-dialog/import-parse-header-dialog.vue';
import ImportLoadingDialog from 'views/inventory/goods-in/components/import-excel-dialog/import-loading-dialog.vue';
import GoodsImportAPI from 'api/goods/goods-import.js';
import GoodsInOrderModel from 'views/inventory/goods-in/model/in-order.js';

import { mapGetters } from 'vuex';


export default {
    components: {
        ImportExcelDialog,
        ImportParseHeaderDialog,
        ImportLoadingDialog,
        ImportGoodsDialog,
    },
    data() {
        return {
            excelId: '',
            showParseHeader: false,
            showImportDialog: false,
            showImportOrderDialog: false, //随货同行单导入的弹窗
            showLoadingDialog: false,

            // 保存当前正在解析的数据
            currentParseData: {
                totalCount: 0,
                currentCount: 0,
                currentGoods: null,
            },

            orderDraftId: '', // 草稿id
            // 表头数据
            matchedDataList: [],
            originExcelOptions: [],
            // 完整的table数据
            importExcelData: {},
            // 当前正在新增药品所在行数
            currentGoodsIndex: -1,
            // 新建档案默认填充的商品信息
            importGoodsInfo: {},
        };
    },
    computed: {
        ...mapGetters(['goodsConfig', 'multiPharmacyCanUse', 'modulePermission']),
        canAddProduct() {
            return this.modulePermission.hasGoodsModule && !this.isChainSubStore;
        },
    },
    methods: {
        /**
         * @desc 解析表头
         * <AUTHOR>
         * @date 2023-09-07 11:50:40
         * isHeaderAllMatched 全部都匹配中了, 直接开始解析表格，不需要确认表头
         */
        async handleLoadedExcel({
            orderDraftId,
            matchedDataList,
            originExcelOptions,
            isHeaderAllMatched,
        }) {
            this.orderDraftId = orderDraftId;
            if (isHeaderAllMatched) {
                await this.handleParsedHeaderAfter();
            } else {
                this.originExcelOptions = originExcelOptions;
                this.matchedDataList = matchedDataList;
                this.showImportDialog = false;
                this.showImportOrderDialog = false;
                this.showParseHeader = true;
            }
        },

        /**
         * @desc 表格解析完成
         * <AUTHOR>
         * @date 2023-09-07 12:00:38
         */
        async handleParsedHeaderAfter() {
            this.showParseHeader = false;
            this.showImportDialog = false;
            this.showLoadingDialog = true;
            try {
                await GoodsImportAPI.parseFileData(this.orderDraftId, (curData) => {
                    const {
                        curRow,
                        totalRow,
                    } = curData;
                    this.currentParseData = {
                        totalCount: totalRow,
                        currentCount: curRow,
                        currentGoods: curData,
                    };
                });
                const data = await this.fetchFileData();
                this.importExcelData = data;
                // 赋值给入库单
                this.updateGoodsInFormOrder();
            } catch (e) {
                if (e.code === 12815) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: e.message,
                    });
                }
            } finally {
                const timer = setTimeout(() => {
                    this.showLoadingDialog = false;
                    this.handleResetParseData();
                    clearTimeout(timer);
                }, 500);

            }
        },
        async fetchFileData() {
            const { data } = await GoodsImportAPI.getFileData(this.orderDraftId, {
                offset: 0,
                limit: 9999,
            });
            return data;
        },
        async fetchMallData() {
            const { data } = await GoodsImportAPI.getFileData(this.importDraftId, {
                offset: 0,
                limit: 9999,
            });
            return data;
        },

        updateGoodsInFormOrder() {
            const {
                list,
                supplierId,
                outOrderNo,
            } = GoodsInOrderModel.importExcelDataTransformInOrderList(this.importExcelData);
            this.order.list = this.order.list.concat(list);
            this.order.supplierId = supplierId;
            this.order.outOrderNo = outOrderNo;
        },

        /**
         * @desc 打开随货同行单下载入口
         * <AUTHOR>
         * @date 2023-09-12 21:31:41
         */
        handleOpenImportOrderDialog() {
            this.showImportDialog = false;
            this.showImportOrderDialog = true;
        },

        /**
         * @desc 商城数据解析
         * <AUTHOR>
         * @date 2023-09-19 14:41:41
         */
        async handleParseMallData() {
            this.showLoadingDialog = true;
            try {
                const goodsList = this.mallGoodsList.map((item) => {
                    return item.mallGoodsInfo;
                });
                await GoodsImportAPI.parseMallData(this.importDraftId, {
                    rows: goodsList,
                }, (curData) => {
                    const {
                        curRow,
                        totalRow,
                    } = curData;
                    this.currentParseData = {
                        totalCount: totalRow,
                        currentCount: curRow,
                        currentGoods: curData,
                    };
                });
                const data = await this.fetchMallData();
                this.importExcelData = data;
                // 赋值给入库单
                this.updateGoodsInFormOrder();
            } catch (e) {
                console.log('获取解析数据失败',e);
            } finally {
                const timer = setTimeout(() => {
                    this.showLoadingDialog = false;
                    this.handleResetParseData();
                    clearTimeout(timer);
                }, 500);
            }
        },

        handleResetParseData() {
            this.currentParseData = {
                totalCount: 0,
                currentCount: 0,
                currentGoods: null,
            };
        },

        /**
         * @desc 新增各类商品成功, 赋值给正在编辑的药品信息，匹配状态改为 已匹配成功
         * <AUTHOR>
         * @date 2023-09-20 10:52:30
         * @params
         * @return
         */
        handleSuccessAdd(goods) {
            this.importGoodsInfo = {};
            if (this.order.list[this.currentGoodsIndex]) {
                this.order.list[this.currentGoodsIndex].goods = goods;
                this.order.list[this.currentGoodsIndex].goodsId = goods.id;
                this.order.list[this.currentGoodsIndex].searchGoodsKey = goods.medicineCadn || goods.name;
            } else {
                if (typeof this.selectGoods === 'function') {
                    this.selectGoods(goods);
                }
            }
        },

        /**
         * @desc 打开商城订单详情窗口
         * <AUTHOR>
         * @date 2023-10-10 10:20:19
         * @params
         * @return
         */
        async handleOpenMallOrderDetail() {
            const mall = await this.$abcPlatform.module.mall;
            const mallOrderId = this.mallOrderId || this.order.mallOrderId;
            mall.service.OrderDetailDialog({
                orderId: mallOrderId,
            });
        },
    },
};
