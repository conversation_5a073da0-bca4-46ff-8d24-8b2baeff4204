<template>
    <abc-popover
        placement="top-start"
        trigger="hover"
        theme="yellow"
        :popper-style="{
            padding: '0px'
        }"
    >
        <abc-link slot="reference">
            <abc-icon slot="prepend" size="16" icon="n-data-1-line"></abc-icon>
            <span>操作记录</span>
        </abc-link>

        <div class="order-change-log-wrapper">
            <div class="order-log-content">
                <div v-for="(item, index) in logs" :key="index" class="log-item">
                    <span>{{ formatTime(item) }}</span>
                    <span>{{ item.createdUser }}</span>
                    <span>{{ item.action }}</span>
                </div>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import { formatCacheTime } from 'utils/index';

    export default {
        name: 'OrderChangeLog',
        props: {
            logs: {
                type: Array,
                required: true,
            },
        },
        methods: {
            formatTime(item) {
                return formatCacheTime(item.createdDate);
            },
        },
    };
</script>
