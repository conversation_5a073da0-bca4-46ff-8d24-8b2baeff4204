<template>
    <abc-card class="invoice-wrapper">
        <abc-layout style="height: 100%;">
            <abc-layout-content style="padding: 0;">
                <abc-descriptions
                    :column="1"
                    :label-width="56"
                    :label-margin="12"
                    ellipsis
                    :bordered="false"
                >
                    <abc-descriptions-item label="发票号" :title="value.invoiceNo">
                        {{ value.invoiceNo }}
                    </abc-descriptions-item>
                    <abc-descriptions-item label="开票日期">
                        {{ value.invoiceDate }}
                    </abc-descriptions-item>
                </abc-descriptions>
            </abc-layout-content>
            <abc-layout-footer v-if="showEdit" class="invoice-footer">
                <abc-button
                    variant="text"
                    theme="primary"
                    size="small"
                    @click="editSettlement"
                >
                    编辑
                </abc-button>
                <abc-button
                    variant="text"
                    theme="danger"
                    size="small"
                    style="margin-left: 4px;"
                    @click="deleteHandle"
                >
                    删除
                </abc-button>
            </abc-layout-footer>
        </abc-layout>
        <invoice-dialog
            v-if="showInvoiceDialog"
            v-model="showInvoiceDialog"
            :invoice="value"
            title="编辑发票"
            @change="changeInvoice"
        ></invoice-dialog>
    </abc-card>
</template>

<script>
    import InvoiceDialog from './invoice-dialog';
    export default {
        name: 'Invoice',
        components: {
            InvoiceDialog,
        },
        props: {
            value: {
                type: Object,
                required: true,
            },
            showEdit: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                showInvoiceDialog: false,
            };
        },
        methods: {
            /**
             * @desc 编辑发票 弹出发票的弹窗
             * <AUTHOR>
             * @date 2019/11/26 11:06:59
             */
            editSettlement() {
                this.showInvoiceDialog = true;
            },
            /**
             * @desc 删除发票
             * <AUTHOR>
             * @date 2019/11/26 11:07:19
             */
            deleteHandle() {
                this.$emit('delete', this.value);
            },
            changeInvoice(val) {
                this.value.invoiceNo = val.invoiceNo;
                this.value.invoiceDate = val.invoiceDate;
            },

        },

    };
</script>

