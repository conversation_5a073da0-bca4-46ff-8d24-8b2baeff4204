/**
 * @desc 函数式打开新增出库弹窗
 * <AUTHOR>
 * @date 2022-11-25 11:15
 */

// eslint-disable-next-line max-classes-per-file
import InForm from './form.vue';
import OutForm from './out-form.vue';
import ApplyReturn from './return.vue';
import { FunctionalDialog } from '@/views/common/functional-dialog.js';

// 领入
export class ReTransInDialog extends FunctionalDialog {
    constructor(props, id = 'abc-goods-apply-in-dialog') {
        super(props, InForm, id, 'showDialog');
    }
}

// 领出
export class ReTransOutDialog extends FunctionalDialog {
    constructor(props, id = 'abc-goods-apply-out-dialog') {
        super(props, OutForm, id, 'showDialog');
    }
}


// 退回
export class ApplyReturnDialog extends FunctionalDialog {
    constructor(props, id = 'abc-goods-apply-return-dialog') {
        super(props, ApplyReturn, id);
    }
}
