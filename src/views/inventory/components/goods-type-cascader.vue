<template>
    <abc-cascader
        v-model="selectTypes"
        v-bind="cascaderProps"
        data-cy="goods-type-cascader"
        @change="handleChangeType"
    >
        <slot></slot>
    </abc-cascader>
</template>

<script>
    import { cascaderDataAdapter } from 'utils/index.js';
    import GoodsAPI from 'api/goods';
    import { isNull } from 'utils/lodash';

    export default {
        name: 'GoodsTypeCascader',
        props: {
            value: {
                type: Array,
                require: true,
                default: () => ([]),
            },
            width: {
                type: Number,
                default: 128,
            },
            cascaderConfig: {
                type: Object,
                default: () => ({}),
            },
            // eslint-disable-next-line vue/require-default-prop
            goodsTypeOptions: {
                type: Array,
            },
            fetchParams: {
                type: Object,
                default: () => ({
                    queryType: 1,
                    needCustomType: 1,
                }),
            },
            filterTypeFunc: {
                type: Function,
                default: () => true,
            },
            allowInit: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                selectTypes: [],
                goodsAllTypes: [],
            };
        },
        computed: {
            options() {
                // 支持直接指定数据
                if (Array.isArray(this.goodsTypeOptions)) return this.goodsTypeOptions;
                // 支持外部自定义过滤内部数据
                return this.goodsAllTypes.filter(this.filterTypeFunc);
            },
            cascaderProps() {
                return {
                    value: this.value,
                    options: this.options,
                    placeholder: '类型',
                    multiple: true,
                    collapse: true,
                    width: this.width,
                    props: {
                        label: 'name',
                        value: 'id',
                        children: 'children',
                    },
                    'value-props': {
                        _label: 'name',
                        _value: 'id',
                    },
                    ...this.cascaderConfig,
                };
            },
        },
        async created() {
            if (isNull(this.goodsTypeOptions)) {
                await this.fetchAllGoodsTypes();
            }
            if (this.value.length && this.allowInit) {
                this.selectTypes = this.value;
            }
            this.$emit('getOptions', this.options);
        },
        methods: {
            async fetchAllGoodsTypes() {
                const { data } = await GoodsAPI.fetchGoodsClassificationV3(this.fetchParams);
                this.goodsAllTypes = data && data.list.map((item) => {
                    const children = item.customTypes || [];
                    if (children.length) {
                        children.push({
                            id: -item.id,
                            name: '未指定',
                            sort: 999,
                            typeId: +item.id,
                        });
                    }
                    return {
                        ...item,
                        children,
                    };
                });
            },
            handleChangeType() {
                const {
                    typeIdList, customTypeIdList,
                } = cascaderDataAdapter(this.options, this.selectTypes);
                this.$emit('input',this.selectTypes);
                this.$emit('change', typeIdList.map(Number), customTypeIdList.map(Number));
            },
            clear() {
                this.selectTypes = [];
                this.$emit('input', []);
                this.$emit('change', [], []);
            },
        },
    };
</script>
