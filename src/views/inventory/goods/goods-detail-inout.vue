
<template>
    <div class="goods-detail-inout__wrapper">
        <abc-layout preset="dialog-table">
            <abc-layout-header>
                <abc-flex align="center" justify="space-between">
                    <abc-space>
                        <abc-date-picker
                            v-model="selectDate"
                            :picker-options="pickerOptions"
                            type="daterange"
                            :clearable="false"
                            value-format="YYYY-MM-DD"
                            placeholder="选择日期范围"
                            @change="changeDate"
                        >
                        </abc-date-picker>

                        <clinic-select
                            v-if="isChainAdmin"
                            v-model="params.clinicId"
                            placeholder="门店"
                            :show-all-clinic="false"
                            clearable
                            @change="filterFetch"
                        ></clinic-select>
                        <abc-cascader
                            v-if="params.dimension === 'detail'"
                            v-model="action"
                            :options="actionOptions"
                            data-cy="cascader-stock-entry-stat-dimension-action"
                            multiple
                            :props="{
                                children: 'childList',
                                label: 'name',
                                value: 'name'
                            }"
                            placeholder="全部动作"
                            :width="128"
                            @change="handleActionChange"
                        >
                        </abc-cascader>
                        <abc-select
                            v-if="isVirtualPharmacy"
                            v-model="params.supplierId"
                            :width="160"
                            placeholder="全部供应商"
                            :pharmacy-type="pharmacyType"
                            @change="filterFetch"
                        >
                            <abc-option
                                v-for="supplier in virtualSuppliers"
                                :key="supplier.id"
                                :value="supplier.id"
                                :label="supplier.name"
                            >
                            </abc-option>
                        </abc-select>
                        <eyeglass-select-popover
                            v-if="from === 'eyeglass'"
                            :params="selectParams"
                            :sub-type="goodsSubType"
                            :item-keys="goodsSubType === 1 ? ['customType'] : []"
                            :include-zero-stock="1"
                            :show-zero-stock="false"
                            :include-disable-stock="1"
                            :show-disable-stock="false"
                            @change="onSelectParamsChange"
                        ></eyeglass-select-popover>
                    </abc-space>
                    <abc-space align="center" justify="space-between">
                        <abc-radio-group v-model="params.dimension" @change="handleDimensionChange">
                            <abc-radio-button key="r1" label="stat">
                                <abc-tooltip placement="top" content="查看药品在某一时间段内的出库入库信息" disabled>
                                    <div>{{ goodsLabel }}</div>
                                </abc-tooltip>
                            </abc-radio-button>
                            <abc-radio-button v-if="inventoryStatDimension" key="r2" label="detail">
                                <abc-tooltip placement="top" content="查看药品在某一时间段内的进销存流水" disabled>
                                    <div>流水</div>
                                </abc-tooltip>
                            </abc-radio-button>
                            <abc-radio-button v-else key="r3" label="detail">
                                <abc-tooltip placement="top" content="查看药品所有批次在某一时间段内的进销存流水" disabled>
                                    <div>流水</div>
                                </abc-tooltip>
                            </abc-radio-button>
                        </abc-radio-group>
                        <abc-dropdown
                            :min-width="136"
                            :max-width="236"
                            placement="bottom-end"
                        >
                            <div slot="reference">
                                <abc-button
                                    variant="ghost"
                                    icon="n-settings-line"
                                    @click="handleSettingCLick"
                                >
                                </abc-button>
                            </div>
                            <div>
                                <abc-dropdown-item style="padding: 0;">
                                    <div style="padding: 6px 12px;" @click="handleOpenGroupSettingDialog">
                                        <span>按{{ statDimensionText }}维度统计</span>
                                        <span style=" margin-left: 5px;">
                                            <abc-icon icon="Arrow_Rgiht"></abc-icon>
                                        </span>
                                    </div>
                                </abc-dropdown-item>
                            </div>
                        </abc-dropdown>
                    </abc-space>
                </abc-flex>
            </abc-layout-header>
            <template v-if="showStatTab">
                <abc-layout-content>
                    <div v-if="from !== 'eyeglass'" class="goods-detail-inout__content">
                        <table-key-data-card :list="tableKeyData" @warning-btn-click="handleOpenStockModifyDialog">
                        </table-key-data-card>
                        <abc-table-fixed2
                            :loading="loading"
                            :header="tableHeaderRender"
                            :data="tableData"
                            :class="{
                                'goods-detail-table': showGoodsDetailStyle, 'goods-detail-table-no-border': goodsDetailTableNoBorder
                            }"
                            :min-height="300"
                            :empty-opt="{ label: '暂无数据' }"
                        >
                        </abc-table-fixed2>
                    </div>
                    <!--汇总表-眼镜-->
                    <abc-table-fixed2
                        v-else
                        :loading="loading"
                        :header="tableHeaderRender"
                        :summary-method="handleSummaries"
                        :data="tableData"
                        :show-total="true"
                        :min-height="300"
                        :empty-opt="{ label: '暂无数据' }"
                    >
                        <template #tableFooter>
                            <abc-pagination
                                :pagination-params="pageParams"
                                :count="totalCount"
                                @current-change="handlePageChange"
                            >
                                <ul slot="tipsContent">
                                    <li>
                                        共 <span>{{ totalCount }}</span> 条 <template v-if="summaryData.actionCountText && fetchParams.action !== '全部动作'">
                                            ，总量{{ summaryData.actionCountText }}
                                        </template>
                                    </li>
                                </ul>
                            </abc-pagination>
                        </template>
                    </abc-table-fixed2>
                </abc-layout-content>
                <!--<abc-layout-footer v-if="from === 'eyeglass'">-->
                <!--    <abc-pagination-->
                <!--        :pagination-params="pageParams"-->
                <!--        :count="totalCount"-->
                <!--        @current-change="handlePageChange"-->
                <!--    >-->
                <!--        <ul slot="tipsContent">-->
                <!--            <li>-->
                <!--                共 <span>{{ totalCount }}</span> 条 <template v-if="summaryData.actionCountText && fetchParams.action !== '全部动作'">-->
                <!--                    ，总量{{ summaryData.actionCountText }}-->
                <!--                </template>-->
                <!--            </li>-->
                <!--        </ul>-->
                <!--    </abc-pagination>-->
                <!--</abc-layout-footer>-->
            </template>
            <!--明细表-->
            <abc-layout-content v-else v-abc-loading.coverOpaque="loading" style="position: relative;">
                <abc-table
                    v-if="tableHeaderDetailRender.list?.length"
                    :class="{ 'stock-detail-table': showDetailTab }"
                    :render-config="tableHeaderDetailRender"
                    :data-list="tableData"
                    empty-size="small"
                    :pagination="tablePagination"
                    empty-content="暂无数据"
                    @pageChange="handlePageChange"
                >
                    <template v-if="summaryData.actionCountText && params.actions" #footer>
                        <abc-space style="padding: 0 12px; margin-left: auto;">
                            <abc-text theme="gray">
                                总量
                            </abc-text>
                            <abc-text theme="black">
                                {{ summaryData.actionCountText }}
                            </abc-text>
                        </abc-space>
                    </template>
                </abc-table>
            </abc-layout-content>
        </abc-layout>

        <setting-stat-dimension-dialog v-if="formDialogVisible" v-model="formDialogVisible" @refresh="handleRefresh"></setting-stat-dimension-dialog>
    </div>
</template>
<script>
    import { PharmacyTypeEnum } from '@abc/constants';
    import { mapGetters } from 'vuex';
    import PickerOptions from 'views/common/pickerOptions';
    import StockEntryAPI from 'views/statistics/core/api/stock-entry.js';
    import {
        paddingMoney, moneyDigit, parseTime,
    } from '@/utils';
    import { formatMoney } from '@/filters';
    import {
        complexCount, goodsTotalCostPrice,
    } from 'src/filters/goods';
    import InventoryDataPermission from '../mixins/inventory-data-permission';
    import Invoicing from 'views/inventory/goods/mixins/invoicing';
    import TableKeyDataCard from 'views/inventory/goods/components/table-key-data-card.vue';
    import EyeglassSelectPopover from 'views/inventory/eyeglass/components/eyeglass-select-popover.vue';

    import clinicSelect from 'views/layout/clinic-select/clinic-select';
    import SettingStatDimensionDialog from 'src/views/statistics/common/setting-stat-dimension-dialog/index.vue';
    import { resolveHeader } from 'src/views/statistics/utils.js';
    import {
        resolveHeader as resolveHeaderRender, getSummaryRenderKeys,
    } from 'utils/table';
    import { Popover as AbcPopover } from '@abc/ui-pc';
    import { StockModifyDialog } from 'views/inventory/goods/components/stock-modify-dialog/index.js';
    import {
        TABLE_CHAIN_CONFIG, TABLE_CONFIG,
    } from './contant';
    import fecha from 'utils/fecha';
    import { prevDate } from '@abc/utils-date';

    export default {
        name: 'GoodsDetailInout',
        components: {
            clinicSelect,
            SettingStatDimensionDialog,
            TableKeyDataCard,
            EyeglassSelectPopover,
        },
        mixins: [PickerOptions, InventoryDataPermission, Invoicing],
        props: {
            goodsId: String,
            goodsType: {
                type: Number,
            },
            goodsSubType: {
                type: Number,
            },
            pharmacyNo: {
                type: Number,
                default: 0,
            },
            pharmacyType: {
                type: Number,
            },
            selectedClinicId: {
                type: String,
            },
            viewType: {
                type: Number,
                default: 0,
            },
            from: {
                type: String,
                default: 'goods-detail',
            },
            goods: {
                type: Object,
                default: () => ({}),
            },
            spuSpecItem: Object,
            spuGoodsId: {
                type: String,
                default: '',
            },
            stockGoodsId: {
                type: String,
                default: '',
            },
        },
        data() {
            const selectParams = {};
            // 如果是sku查看详情，需要筛选数据展示当前sku
            if (this.spuSpecItem) {
                const {
                    type,color,spec,focalLength,spherical,lenticular,
                } = this.spuSpecItem;
                Object.assign(selectParams,{
                    color,
                    spec,
                    customType: this.goodsSubType === 1 ? type : '',
                    focalLength: focalLength?.start,
                    spherical: spherical?.start,
                    lenticular: lenticular?.start,
                });
            }
            return {
                tableHeader: [],
                loading: true,
                goodsList: [],
                tableData: [],
                totalCount: 0,
                action: [],
                params: {
                    pageIndex: 1,
                    pageSize: 10,
                    beginDate: '',
                    endDate: '',
                    dimension: 'stat',
                    actions: '',
                    spherical: '',
                    lenticular: '',
                    material: '',
                    spec: '',
                    color: '',
                    wearCycle: '',
                },
                itemKeys: ['spherical', 'lenticular', 'material', 'spec', 'color', 'wearCycle'],
                selectParams,
                actionOptions: [],
                selectedAction: '全部动作',
                selectDate: [],
                virtualSuppliers: [],
                statDimensionText: '药品',
                formDialogVisible: false,
                tableKeyData: [],
                panelData: {
                    rows: [],
                    count: 0,
                    totalCountStr: 0,
                },
                tableConfig: [],
                summaryData: {},
                pharmacyNumber: '',
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'subClinics', 'isChain', 'isChainAdmin', 'isChainSubStore', 'inventoryStatDimension', 'isCanSeeGoodsCostInInventory']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            goodsLabel() {
                return this.viewDistributeConfig.Statistics.stockEntryStat.goodsLabel;
            },
            chainId() {
                return this.currentClinic && this.currentClinic.chainId;
            },
            showGoodsDetailStyle() {
                return this.showStatTab && !this.loading && this.from !== 'eyeglass';
            },
            goodsDetailTableNoBorder() {
                return this.showStatTab && this.loading && this.from !== 'eyeglass';
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.currentClinic?.clinicId;
            },
            isVirtualPharmacy() {
                return this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            tableHeaderRender() {
                return resolveHeader(this.tableHeader, this.renderTypeList);
            },
            tableHeaderDetailRender() {
                return {
                    hasInnerBorder: true,
                    list: resolveHeaderRender(this.tableHeader, this.renderTypeList),
                };
            },
            displaySummaryData() {
                return this.tableData.length ? this.summaryData : null;
            },
            summaryRenderKeys() {
                return getSummaryRenderKeys(this.tableHeader);
            },
            tablePagination() {
                return {
                    showTotalPage: this.showDetailTab || (this.from === 'eyeglass' && this.showStatTab),
                    pageIndex: this.pageParams.pageIndex,
                    pageSize: this.pageParams.pageSize,
                    count: this.totalCount,
                };
            },
            showStatTab() {
                return this.params.dimension === 'stat';
            },
            showDetailTab() {
                return this.params.dimension === 'detail';
            },
            renderTypeList() {
                return {
                    styleRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return <div class="cell" style="display: flex; justify-content: center; font-weight: bold;align-items: center; background: #f5f7fb; width: 100%;  height: 100%;">{ displayValue }</div>;
                    },
                    countRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        const status = +this.tableKeyData?.filter((item) => item.text === 'status')[0]?.value;
                        return !status ? <div class="cell" style="text-align: end">
                              {
                                (displayValue !== '0') ?
                                <span>{displayValue}</span> :
                                <span>0.00</span>
                              }
                            </div> : <div class="cell" style="text-align: end">{ parseFloat(+displayValue || 0).toFixed(2) }</div>;
                    },
                    styleHeaderRender: (h, col) => {
                        return <div class="cell" style="font-weight: bold; ">{ col.label }</div>;
                    },
                    stockActionRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        const icon = <abc-icon icon="info_bold" size="16" color="#dadbe0" style="cursor: pointer"></abc-icon>;
                        const isInStockModify = displayValue === '入库修正';
                        const isSpecificationModify = displayValue === '规格修改';
                        const isRateModify = displayValue === '税率修改';
                        const modifyText = '指修改入库单产生的变化库存数量';

                        if (isInStockModify) {
                            return (
                                <AbcPopover trigger="hover"
                                        z-index="10000"
                                        placement="top-start"
                                        visibleArrow={false}
                                        theme="yellow">
                                    <div slot="reference" class="cell" style="font-weight: bold;background: #f5f7fb; height: 40px; line-height: 40px">{displayValue} { icon }</div>
                                    <div>
                                        <span>{ modifyText }</span>
                                    </div>
                                </AbcPopover>
                            );
                        }
                        if (isSpecificationModify) {
                            const icon = <abc-icon icon="Attention" size="14" style="margin-left: 5px; cursor: pointer" onClick={this.handleOpenStockModifyDialog}></abc-icon>;
                            return (
                                <div class="cell" style="font-weight: bold; background: #f5f7fb; width: 100%;  height: 40px; line-height: 40px" title={displayValue}>
                                    {displayValue}
                                    {icon}
                                </div>
                            );
                        }
                        if (isRateModify) {
                            const icon = <abc-icon icon="Attention" size="14" style="margin-left: 5px; cursor: pointer" onClick={this.handleOpenRateModifyDialog}></abc-icon>;
                            return (
                                <div class="cell" style="font-weight: bold; background: #f5f7fb; width: 100%;  height: 40px; line-height: 40px" title={displayValue}>
                                    {displayValue}
                                    {icon}
                                </div>
                            );
                        }
                        return <div class='cell' style="font-weight: bold; background: #f5f7fb; width: 100%;  height: 40px; line-height: 40px">{displayValue}</div>;
                    },

                    actionPopover: (h, row) => {
                        if (this.showSupplier(row.action) && row.supplierText) {
                            return (
                                <AbcPopover
                                    placement='bottom-start'
                                    trigger="hover"
                                    theme="white"
                                >

                                    <abc-table-cell slot='reference'>
                                        <span>{row.action}</span>
                                        <abc-icon icon="info_bold" size="14" style="margin-left: 4px; color: #dadbe0" ></abc-icon>
                                    </abc-table-cell>
                                    <div >
                                        供应商：{row.supplierText}
                                    </div>
                                </AbcPopover>
                            );
                        }
                        return <abc-table-cell class='ellipsis'>{row.action}</abc-table-cell>;
                    },
                    actionCountTextRender: (h, row) => {
                        if (row.inOrderModified) {
                            return (
                                <AbcPopover
                                    placement='bottom-start'
                                    trigger="hover"
                                    theme="white">
                                    <abc-table-cell slot='reference'>
                                        <span>{row.actionCountText}</span>
                                        <abc-icon icon="info_bold" size="14" style="margin-left: 4px; color: #dadbe0" ></abc-icon>
                                    </abc-table-cell>
                                    <div >
                                        该入库单的入库数量已被修改，请在入库单中查看修改记录
                                    </div>
                                </AbcPopover>
                            );
                        }
                        return <abc-table-cell class='ellipsis'>{row.actionCountText}</abc-table-cell>;
                    },
                    traceableCodeCountStrRender: (h, row) => {
                        return <AbcPopover
                                    placement='top-start'
                                    trigger="hover"
                                    theme="yellow"
                                    popperStyle={{ 'padding-right': '0px' }}
                                    disabled={!row.traceableCodeList?.length}
                                >
                                    <abc-table-cell slot='reference'>
                                        <span title={row.traceableCodeCountStr}>{row.traceableCodeCountStr}</span>
                                    </abc-table-cell>
                                    <abc-scrollbar style="max-height: 400px;" paddingSize="none">
                                            <abc-flex vertical>
                                                {
                                                    row.traceableCodeList?.map((item) => {
                                                        return <abc-text theme="black">
                                                            { item?.no ? item.no : item }
                                                            {
                                                                item?.count > 1 ? <abc-text theme="gray" style="margin-left:8px">x{item.count}</abc-text> : null
                                                            }
                                                        </abc-text>;
                                                    })
                                                }
                                            </abc-flex>
                                    </abc-scrollbar>
                                </AbcPopover>;
                    },
                };
            },
            modifyParams() {
                const {
                    goodsId, pharmacyType,
                } = this;
                const {
                    beginDate, endDate,
                } = this.params;
                return {
                    goodsId,
                    pharmacyNo: this.pharmacyNumber,
                    pharmacyType,
                    beginDate,
                    endDate,
                };
            },
        },
        watch: {
            pharmacyNo: {
                handler(no) {
                    if (this.isChainAdmin) {
                        this.pharmacyNumber = '';
                    } else {
                        this.pharmacyNumber = no;
                    }
                },
                immediate: true,
            },
        },
        async created() {
            const nowDate = new Date();
            const DATE_FORMATE = 'YYYY-MM-DD';
            this.params.beginDate = fecha.format(prevDate(nowDate, 91), DATE_FORMATE);
            this.params.endDate = fecha.format(nowDate, DATE_FORMATE);
            this.$store.dispatch('fetchInventoryStatDimension');
            if (this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                this.fetchVirtualSuppliers();
            }
            // params中有初始值
            this.selectDate = [this.params.beginDate, this.params.endDate];
            this.getActionSelectOptions();
            await this.getGoodsId();
            this.getTableData();
        },
        methods: {
            parseTime,
            formatMoney,
            paddingMoney,
            moneyDigit,
            complexCount,
            goodsTotalCostPrice,
            async onSelectParamsChange(val) {
                this.selectParams.spherical = val.params.spherical;
                this.selectParams.lenticular = val.params.lenticular;
                this.selectParams.material = val.params.material;
                this.selectParams.spec = val.params.spec;
                this.selectParams.color = val.params.color;
                this.selectParams.wearCycle = val.params.wearCycle;
                this.selectParams.customType = val.params.customType;
                await this.getGoodsId();
                this.getTableData();
            },
            handleOpenStockModifyDialog() {
                new StockModifyDialog({
                    business: 'spec',
                    modifyParams: this.modifyParams,
                    tips: '本期内存在规格变更，变更前后规格与库存数量均不相同，无法简单求和计算',
                    isChainAdmin: this.isChainAdmin,
                }, 'stock-modify-dialog').generateDialog();
            },
            handleOpenRateModifyDialog() {
                new StockModifyDialog({
                    business: 'rate',
                    modifyParams: this.modifyParams,
                    tips: '本期内存在税率变更',
                }, 'stock-modify-dialog').generateDialog();
            },
            handlePageChange(index) {
                this.params.pageIndex = index;
                this.getTableData(false);
            },
            handleActionChange(list) {
                const actions = new Set() ;
                list.forEach((item) => {
                    if (item[2]?.value) {
                        actions.add(item[2]?.value);
                        return;
                    }
                    if (item[1]?.value) {
                        actions.add(item[1]?.value);
                    }
                });
                this.params.actions = [...actions].join();

                this.getTableData();
            },
            handleSummaries(data, col) {
                return col.type === 'money' ? parseFloat(this.summaryData[col.prop] || 0).toFixed(2) : this.summaryData[col.prop] ;
            },

            async getGoodsId() {
                try {
                    const {
                        spherical,
                        lenticular,
                        material,
                        spec,
                        color,
                        wearCycle,
                        customType,
                    } = this.selectParams;
                    const { data } = await StockEntryAPI.getGoodsId({
                        spherical,
                        lenticular,
                        material,
                        spec,
                        color,
                        wearCycle,
                        customType,
                        spuGoodsId: this.viewType ? '' : this.spuGoodsId,
                        goodsId: this.stockGoodsId,
                    });

                    this.goodsList = data.data;

                } catch (err) {
                    console.log(err);
                }

            },

            // 动作下拉选项
            async getActionSelectOptions() {
                const {
                    beginDate,
                    endDate,
                } = this.params;
                const {
                    queryClinicId: clinicId, pharmacyType,
                } = this;
                try {
                    const { data } = await StockEntryAPI.action({
                        clinicId,
                        beginDate,
                        endDate,
                        pharmacyType,
                    });
                    this.actionOptions = data || [];
                } catch (err) {
                    console.error(err);
                }
            },
            /**
             * @desc 获取供应商列表
             * <AUTHOR>
             * @date 2022-04-26 14:13:40
             */
            async fetchVirtualSuppliers() {
                try {
                    const { data } = await StockEntryAPI.virtualSupplier();
                    this.virtualSuppliers = data?.rows || [];
                } catch (e) {
                    console.warn('获取供应商列表失败', e);
                }
            },
            handlePageIndexChange(index) {
                this.params.pageIndex = index;
                this.getTableData(false);
            },


            async handleDimensionChange(val) {
                this.params.dimension = val;
                this.totalCount = 0;
                this.tableHeader = [];
                await this.getGoodsId();
                this.getTableData();
            },
            /**
             * @desc 是否是修正入库
             * <AUTHOR>
             */
            isUpdatedInOrder(item) {
                if (item) {
                    return item.action === '修正入库';
                }
                return false;
            },
            handleSettingCLick() {
                this.statDimensionText = this.inventoryStatDimension ? this.goodsLabel : '批次';
            },
            handleOpenGroupSettingDialog() {
                this.formDialogVisible = true;
            },
            changeDate(picker) {
                if (picker.length === 2) {
                    this.params.beginDate = picker[0];
                    this.params.endDate = picker[1];
                } else {
                    this.params.beginDate = '';
                    this.params.endDate = '';
                }
                this.params.pageIndex = 1;
                this.getActionSelectOptions();
                this.getTableData();
            },
            handleRefresh() {
                this.tableConfig = this.isChain ? TABLE_CHAIN_CONFIG : TABLE_CONFIG;
                if (this.inventoryStatDimension) {
                    this.tableConfig = this.tableConfig.filter((config) => {
                        return config.prop !== 'batchNo' && config.prop !== 'expiryDate' ;
                    });
                }
                this.getTableData();
            },
            getTableParams() {
                const {
                    actions,
                    beginDate,
                    endDate,
                    pageIndex,
                    pageSize,
                    supplierId,
                } = this.params;
                const { pharmacyType } = this;
                // 1表示能看，0表示不能看，默认不能看
                let enableCost = 0;
                if (this.isChainAdmin) {
                    // 如果是总部管理员，则可以看
                    enableCost = 1;
                } else {
                    // 如果是有库存权限，则可以看
                    enableCost = this.isCanSeeGoodsCostInInventory ? 1 : 0;
                }
                const baseParams = {
                    clinicId: this.queryClinicId,
                    beginDate,
                    endDate,
                    pharmacyType,
                    dimension: this.inventoryStatDimension,
                };
                if (this.showStatTab) {
                    if (this.from === 'eyeglass') {
                        return {
                            ...baseParams,
                            pageIndex,
                            pageSize,
                            pharmacyNo: this.pharmacyNumber,
                            goodsId: this.goodsList?.join(','),
                            headerType: 'stockContainsEyeglass',
                        };
                    }
                    return {
                        ...baseParams,
                        goodsId: this.goodsId,
                        pharmacyNo: this.pharmacyNumber,
                        enableCost,
                    };
                }
                if (this.from === 'eyeglass') {
                    return {
                        ...baseParams,
                        supplierId,
                        pageIndex,
                        pageSize,
                        pharmacyNos: [String(this.pharmacyNumber)],
                        actions: actions.split(','),
                        goodsId: this.goodsList?.join(','),
                        headerType: 'stockContainsEyeglass',
                    };
                }
                return {
                    ...baseParams,
                    goodsId: this.goodsId,
                    pharmacyNos: [String(this.pharmacyNumber)],
                    supplierId,
                    pageIndex,
                    pageSize,
                    actions: actions.split(','),
                    enableCost,
                };
            },
            setTableData(isClear = false, tableData = {}) {
                if (isClear) {
                    this.tableHeader = [];
                    this.tableData = [];
                    this.totalCount = 0;
                    this.summaryData = {};
                } else {
                    const {
                        header = [], data = [], total = {}, keyData = [],summary = {},
                    } = tableData || {};
                    this.tableHeader = header || [];
                    this.tableData = data || [];
                    this.totalCount = total?.count || 0;
                    this.tableKeyData = keyData || [];
                    this.summaryData = summary || {};
                }
                this.$emit('loaded');
            },
            async getTableData(resetPageParams = true) {
                if (resetPageParams) {
                    this.params.pageIndex = 1;
                    this.params.pageSize = 10;
                }

                this.loading = true;

                if (this.showStatTab) {
                    if (this.from === 'eyeglass') {
                        const params = this.getTableParams();
                        try {
                            const { data } = await StockEntryAPI.goods({
                                ...params,
                                pharmacyType: 0,
                            });
                            this.setTableData(false, data);
                        } catch (err) {
                            console.error(err);
                            this.setTableData(true);
                        } finally {
                            this.loading = false;
                        }
                    } else {
                        const params = this.getTableParams();
                        try {
                            const { data } = await StockEntryAPI.summary({
                                ...params,
                            });
                            this.setTableData(false, data);
                        } catch (err) {
                            console.error(err);
                            this.setTableData(true);
                        } finally {
                            this.loading = false;
                        }
                    }
                } else {
                    if (this.from === 'eyeglass') {
                        try {
                            const params = this.getTableParams();
                            const { data } = await StockEntryAPI.record({
                                ...params,
                                pharmacyType: 0,
                            });
                            this.setTableData(false, data);
                        } catch (err) {
                            console.error(err);
                            this.setTableData(true);
                        } finally {
                            this.loading = false;
                        }
                    } else {
                        try {
                            const params = this.getTableParams();
                            const { data } = await StockEntryAPI.record({
                                ...params,
                                headerType: 'stock',
                                pharmacyType: this.pharmacyType,
                            });
                            this.setTableData(false, data);
                        } catch (err) {
                            console.error(err);
                            this.setTableData(true);
                        } finally {
                            this.loading = false;
                        }
                    }
                }
            },

            async filterFetch() {
                this.params.pageIndex = 1;
                this.getActionSelectOptions();
                await this.getTableData();
            },

            showSupplier(action) {
                if (!action) return false;
                const inActions = ['入库单增加', '采购入库'];
                if (inActions.indexOf(action) !== -1) return true;
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';
    @import 'src/styles/mixin.scss';

    .goods-detail-inout__wrapper {
        //min-height: 510px;

        .goods-detail-inout__content {
            padding-bottom: 24px;
        }

        .abc-fixed-table.goods-detail-table {
            border-right: 1px solid $P6;
            border-left: 1px solid $P6;

            table thead tr th {
                &:first-child {
                    border-right: none;
                }
            }
        }

        .abc-fixed-table.stock-detail-table {
            table thead th {
                border-right: none;
            }

            .abc-table__body-wrapper {
                table tr {
                    border-right: none;
                    border-left: none;
                }

                tbody td {
                    border-right: none !important;
                    border-left: none;
                }
            }
        }

        .abc-fixed-table.goods-detail-table-no-border {
            .detail-table-container::after {
                border-bottom: none;
            }
        }

        .goods-detail-inout-content {
            .is-update {
                color: $T2;
            }

            .abc-table-wrapper {
                width: 100%;
                margin: 0 auto;

                .table-title {
                    background-color: $P5;
                    border-bottom: 1px solid $P6;
                }

                .table-tr {
                    border-color: $P6;
                }
            }

            .clinic {
                display: inline-block;
                width: 80px;
                max-width: 100px;
                height: 100%;
                padding-left: 4px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .supplier-wrapper {
                display: flex;
                height: 100%;
                padding-left: 4px;
                white-space: nowrap;

                .supplier-info {
                    position: relative;
                    display: inline-block;
                    width: 14px;

                    .iconfont {
                        font-size: 14px;
                        color: $T3;
                    }

                    .suppliers {
                        position: absolute;
                        bottom: -37px;
                        left: 0;
                        z-index: 1;
                        display: none;
                        height: 40px;
                        padding: 0 16px;
                        font-weight: normal;
                        line-height: 40px;
                        text-align: center;
                        white-space: nowrap;
                        background: #ffffff;
                        border: 1px solid $P1;
                        border-radius: 5px;
                        box-shadow: 0 2px 8px 0 $P1;
                        transform: translateX(-50%);

                        &::before {
                            position: absolute;
                            top: 0;
                            left: 50%;
                            display: block;
                            margin-top: -7px;
                            content: ' ';

                            @include triangle(14px, 7px, $P1, 'up');
                        }

                        &::after {
                            position: absolute;
                            top: 0;
                            left: 50%;
                            display: block;
                            margin-top: -6px;
                            content: ' ';

                            @include triangle(14px, 6px, #fff, 'up');
                        }
                    }

                    &:hover {
                        .suppliers {
                            display: inline-block;
                        }
                    }
                }

                .action-count-text {
                    display: inline-block;
                    flex: 1;

                    @include ellipsis;
                }
            }
        }
    }

    .abc-group-select_popper {
        z-index: 9999;
    }
</style>
