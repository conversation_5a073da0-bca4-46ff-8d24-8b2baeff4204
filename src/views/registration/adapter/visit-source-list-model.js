/**
 *  created by lllwx
 *  date: 2021/4/8
 *  time: 5:32 下午
 *  version: 1.0
 *  desc: 就诊来源选项处理为viewModel
 */
import BaseModel from 'views/common/adapter/base-model';

class VisitSourceListModel extends BaseModel {
    static get defaultValue() {
        return [];
    }

    convert(data) {
        return data.map((item) => {
            return {
                ...item,
                childs: item.children,
            };
        });
    }
}

export default VisitSourceListModel;
