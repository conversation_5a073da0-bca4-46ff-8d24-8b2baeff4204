<template>
    <div class="member-manage">
        <abc-layout preset="page-table" style="padding: 0 0 !important;">
            <abc-layout-header>
                <head-bar
                    :keyword="keyword"
                    :share="share"
                    @keyword="(val) => (keyword = val)"
                    @btnJoin="onClickAdd"
                    @btnSetMemberType="onClickMemberFeature('handleMemberType')"
                    @refresh="onFetchMemberList(true)"
                ></head-bar>
            </abc-layout-header>
            <abc-layout-content v-if="visible" @layout-mounted="handleMounted">
                <main-table
                    ref="table"
                    :keyword="keyword"
                    :share="share"
                    :current-pagination-params="paginationParams"
                    @edit="onClickEdit"
                    @editMember="onClickEdit"
                    @charge="onClickMemberHandle('handleMemberCharge', ...arguments)"
                    @refund="onClickMemberHandle('handleMemberRefund', ...arguments)"
                    @record="onClickMemberHandle('handleMemberRecord', ...arguments)"
                    @password="onClickMemberHandle('handleMemberPassword', ...arguments)"
                ></main-table>
            </abc-layout-content>
            <abc-layout-footer>
                <abc-pagination
                    :count="tablePagination.count"
                    :pagination-params="tablePagination"
                    :show-total-page="true"
                    @current-change="onChangePageIndex"
                >
                </abc-pagination>
            </abc-layout-footer>
        </abc-layout>

        <view-member-handles ref="member-handles" is-manage @fetch-patient-info="onFetchMemberList"></view-member-handles>

        <view-member-family-manage ref="member-family-manage" @fetch-patient-info="onFetchMemberList"></view-member-family-manage>
    </div>
</template>

<script>
    import HeadBar from './head-bar.vue';
    import MainTable from './table.vue';

    import ViewMemberHandles from '../common/package-member/view-member-handles.vue';
    import ViewMemberFamilyManage from '../common/package-info/view-member-family-manage.vue';
    import MarketingAPI from 'api/marketing';
    import ClinicAPI from 'api/clinic';
    export default {
        name: 'MemberManageMain',
        components: {
            HeadBar,
            MainTable,

            ViewMemberHandles,
            ViewMemberFamilyManage,
        },
        data() {
            return {
                keyword: '',
                // 成员管理类型
                operateType: 'join',
                currentItem: {},

                share: {
                    begin: '',
                    end: '',
                    dateRange: [],
                    clinicId: '',
                    typeId: '',
                    clinics: [],
                    types: [],
                    patientId: undefined,
                },
                printOpt: {
                    finishSelect: [],
                },
                isFirstPrint: true,
                printLoading: false,
                visible: false,
                paginationParams: {
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },
            };
        },
        computed: {
            tablePagination() {
                return {
                    showTotalPage: true,
                    pageIndex: this.paginationParams.pageIndex,
                    pageSize: this.paginationParams.pageSize,
                    count: this.paginationParams.count,
                };
            },
        },
        async created() {
            await Promise.all([this.getAllClinics(), this.getAllMembertypes()]);
        },
        mounted() {
            // 不加这个的话第一次计算table高度的时候tabs还未渲染出来，高度计算会少56px
            this.$nextTick(() => {
                this.visible = true;
            });
        },
        methods: {
            handleMounted(data) {
                this.$refs.table.handleMounted(data.paginationLimit);
            },
            onChangePageIndex(data) {
                this.$refs.table.onChangePageIndex(data);
            },
            async getAllClinics() {
                try {
                    const { data } = await ClinicAPI.chainClinicV3();
                    this.share.clinics = data.rows || [];
                } catch (error) {
                    console.log('fetchChainClinics error', error);
                }

            },
            async getAllMembertypes() {
                try {
                    const { rows } = await MarketingAPI.getAllMemberCardByChainId(0, 1000);
                    this.share.types = rows || [];
                } catch (error) {
                    console.log('fetchMemberTypes error', error);
                }
            },
            /**
             * desc [当点击新增会员信息]
             */
            onClickAdd() {
                this.operateType = 'join';
                this.onClickMemberFeature('handleInsertMember', null);
            },
            /**
             * desc [当点击编辑会员信息]
             */
            onClickEdit(item) {
                this.operateType = 'edit';
                this.onClickMemberFeature('handleUpdateMemberByPatient', item.patientId);
            },
            /**
             * desc [会员充值、退费、积分兑换、流水灯操作]
             */
            onClickMemberHandle(handleFunc, item) {
                const memberHandlesNode = this.$refs['member-handles'];
                memberHandlesNode && memberHandlesNode[handleFunc] && memberHandlesNode[handleFunc](item);
            },
            /**
             * desc [当点击会员相关功能时]
             */
            onClickMemberFeature(handleFunc, ...args) {
                const memberFamilyManage = this.$refs['member-family-manage'];
                memberFamilyManage && memberFamilyManage[handleFunc] && memberFamilyManage[handleFunc](...args);
            },
            onFetchMemberList() {
                this.$refs.table.initPageParams();
                this.$refs.table.fetchMemberDataList();
            },
        },
    };
</script>

<style lang="scss" rel="stylesheet/scss">
@import '~styles/theme';
@import '~styles/mixin.scss';

.member-manage {
    height: calc(100% - 8px);
    padding: 20px 20px;
    margin-top: 8px;
    background-color: $S2;
    border-top: 1px solid $P8;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;

    .form-title {
        height: 47px;
        padding-bottom: 14px;
        font-size: 0;
        border-bottom: 1px solid $P4;

        button {
            padding: 0;
            vertical-align: top;
        }

        .btn-back {
            margin-top: 6px;
            color: $theme2;

            i,
            b {
                vertical-align: middle;
            }

            b {
                font-weight: normal;
            }
        }

        span {
            margin-left: 12px;
            font-size: 20px;
            font-weight: 500;
            color: #333333;
            vertical-align: top;
        }
    }

    .pay-mode {
        span {
            display: inline-block;
            height: 32px;
            padding: 0 16px;
            margin-right: 4px;
            font-size: 14px;
            font-weight: normal;
            line-height: 30px;
            color: $T2;
            text-align: center;
            cursor: pointer;
            border: 1px solid $P3;
            border-radius: var(--abc-border-radius-small);
        }

        .cash.selected {
            color: $Y2;
            background-color: #fef4e5;
            border-color: #fed395;
        }

        .wechat.selected {
            color: $C3;
            background-color: #effae6;
            border-color: #b8e890;
        }

        .alipay.selected {
            color: #0787e2;
            background-color: #e6f3fc;
            border-color: #a2d2f3;
        }

        .yicard.selected {
            color: #17acdb;
            background-color: rgba(23, 172, 219, 0.1);
            border-color: rgba(23, 172, 219, 0.3);
        }

        .card.selected {
            color: #fe7d7d;
            background-color: #fef4e5;
            border-color: #fdcece;
        }

        i {
            float: left;
            margin-right: 5px;
            font-size: 16px;
            vertical-align: middle;
        }

        &.disabled {
            span {
                cursor: not-allowed;
            }
        }
    }
}
</style>
