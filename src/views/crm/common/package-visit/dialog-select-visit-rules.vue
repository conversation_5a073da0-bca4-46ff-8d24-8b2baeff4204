<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        class="select-visit-rules_dialog"
        title="自动生成随访规则"
        content-styles="padding: 24px 12px 24px 24px; width: 514px; height: 326px; overflow-x: hidden;"
        append-to-body
    >
        <div class="select-visit-rules_dialog--box">
            <div
                v-for="(item,index) in ruleList"
                :key="index"
                class="select-visit-rules_dialog--box-line"
                :class="{
                    'select-visit-rules_dialog--box-line-select': isSelect === item.id, 'select-visit-rules_dialog--box-line-own': item.key === 0
                }"
                @click="selectRule(item.id)"
            >
                <div class="select-visit-rules_dialog--box-line-left">
                    <div class="select-visit-rules_dialog--box-line-title">
                        {{ item.title }}
                    </div>
                    <div v-if="item.desc" :title="item.desc" class="select-visit-rules_dialog--box-line-desc">
                        目的: {{ item.desc }}
                    </div>
                    <div class="select-visit-rules_dialog--box-line-filter">
                        条件: <span v-for="(i,number) in item.packageFilters" :key="number">
                            {{ i.label }}{{ number === item.packageFilters.length - 1 ? '' : ';' }}
                        </span>
                    </div>
                </div>
                <div class="select-visit-rules_dialog--box-line-right">
                    <abc-icon icon="duigou" color="#2680F7" class="select-visit-rules_dialog--box-line-icon"></abc-icon>
                </div>
            </div>
            <div class="select-visit-rules_dialog--box-line select-visit-rules_dialog--box-add" @click="addSelect">
                <abc-icon
                    icon="add_hover"
                    size="14"
                    color="#005ed9"
                    style="margin-right: 9px;"
                ></abc-icon>自定义规则
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <abc-button :disabled="!isSelect" @click="confirmSelect">
                确定
            </abc-button>
            <abc-button class="abc-button-blank" @click="visible = false">
                取消
            </abc-button>
        </div>
        <dialog-filter
            v-if="visibleFilter"
            v-model="visibleFilter"
            last-outpatient
            :filter-config="filterConfig"
            @change="onChangeFilterConfig"
        ></dialog-filter>
    </abc-dialog>
</template>

<script>
    import { VISIT_TYPE } from 'views/crm/constants';
    const DialogFilter = () => import('views/crm/common/package-filter/index.vue');
    import MixinFilter from 'views/crm/common/package-filter/mixin-filter';
    import { mapGetters } from 'vuex';
    import { formatDate } from '@tool/date';

    export default {
        name: 'DialogSelectVisitRules',
        components: {
            DialogFilter,
        },
        mixins: [ MixinFilter],
        props: {
            value: {
                type: Boolean,
            },
            visitType: {
                type: Number,
                default: VISIT_TYPE.AFTER_OUTPATIENT,
            },
        },
        data() {
            return {
                isSelect: null,
                visibleFilter: false,
                filterConfig: null,
                ownRules: [],
                ownRulesCount: 1,
            };
        },
        computed: {
            ...mapGetters(['hasChildHealthModulePower']),
            visible: {
                get() {
                    return this.value;
                },
                set(value) {
                    this.$emit('input', value);
                },
            },
            filterInfo() {
                return this.createFilterParams(this.filterConfig);
            },
            ruleList() {
                return this.modelRules.concat(this.ownRules);
            },
            modelRules() {
                if (this.visitType === VISIT_TYPE.HOSPITAL_DISCHARGE) {
                    return [
                        {
                            id: 1,
                            key: 1,// 0自己创建 1模版，
                            title: '出院患者',
                            desc: '随访出院患者，监测病情变化，指导康复，提升患者满意度',
                            packageFilters: [
                                {
                                    key: 'dischargeDate',
                                    label: '出院时间[当天]',
                                },
                            ],
                            params: {
                                dischargeBeginDate: formatDate(new Date(), 'YYYY-MM-DD'),
                                dischargeEndDate: formatDate(new Date(), 'YYYY-MM-DD'),
                            },
                        },
                    ];
                }
                const model = [
                    {
                        id: 1,
                        key: 1,// 0自己创建 1模版，
                        title: '初诊患者',
                        desc: '精细化随访初诊患者，积累好感提升复诊概率',
                        packageFilters: [
                            {
                                key: 'treatDate',
                                label: '就诊时间[当天]',
                            },
                            {
                                key: 'clinicVisit',
                                label: '门店初/复诊[初诊]',
                            },
                            {
                                key: 'outpatientType',
                                label: '门诊类型[普通门诊]',
                            },
                        ],
                        params: {
                            beginLastOutpatientDate: formatDate(new Date(), 'YYYY-MM-DD'),
                            endLastOutpatientDate: formatDate(new Date(), 'YYYY-MM-DD'),
                            clinicVisit: 0,
                            outpatientType: 0,
                        },
                    },
                    {
                        id: 2,
                        key: 1,// 0自己创建 1模版，
                        title: '每日门诊患者',
                        desc: '随访所有门诊患者，适用于社区医疗机构，做好口碑运营',
                        packageFilters: [
                            {
                                key: 'treatDate',
                                label: '就诊时间[当天]',
                            },
                            {
                                key: 'outpatientType',
                                label: '门诊类型[普通门诊]',
                            },
                        ],
                        params: {
                            beginLastOutpatientDate: formatDate(new Date(), 'YYYY-MM-DD'),
                            endLastOutpatientDate: formatDate(new Date(), 'YYYY-MM-DD'),
                            outpatientType: 0,
                        },
                    },
                    // {
                    //     id: 3,
                    //     key: 1,// 0自己创建 1模版，
                    //     title: '高消费患者',
                    //     desc: '锁定高消费人群，可通过项目介绍再次刺激到店',
                    //     packageFilters: [
                    //         {
                    //             key: 'treatDate',
                    //             label: '就诊时间[当天]',
                    //         },
                    //         {
                    //             key: 'paidNum',
                    //             label: '付费次数[2 ~ 5]',
                    //         },
                    //         {
                    //             key: 'paidFee',
                    //             label: '累计消费[2000 ~ 10000]',
                    //         },
                    //         {
                    //             key: 'oneFee',
                    //             label: '客单价[1000 ~ 不限]',
                    //         },
                    //     ],
                    //     params: {
                    //         beginLastOutpatientDate: fecha.format(new Date(), 'YYYY-MM-DD'),
                    //         endLastOutpatientDate: fecha.format(new Date(), 'YYYY-MM-DD'),
                    //         isFilter: 1,
                    //         maxPayAmountTotal: '10000',
                    //         maxPayTimes: '5',
                    //         minPayTimes: '2',
                    //         minPayAmountEach: '1000',
                    //         minPayAmountTotal: '2000',
                    //     },
                    // },
                ];
                // if (this.hasChildHealthModulePower) {
                //     model.push(
                //         {
                //             id: 4,
                //             key: 1,// 0自己创建 1模版，
                //             title: '儿保患者',
                //             desc: '提升专科患者就诊感受，提升服务医疗次数，增强营收',
                //             packageFilters: [
                //                 {
                //                     key: 'treatDate',
                //                     label: '就诊时间[当天]',
                //                 },
                //                 {
                //                     key: 'outpatientType',
                //                     label: '门诊类型[儿保门诊]',
                //                 },
                //             ],
                //             params: {
                //                 beginLastOutpatientDate: fecha.format(new Date(), 'YYYY-MM-DD'),
                //                 endLastOutpatientDate: fecha.format(new Date(), 'YYYY-MM-DD'),
                //                 outpatientType: 1,
                //             },
                //         },
                //     );
                // }
                return model;
            },

        },
        methods: {
            selectRule(id) {
                if (id === this.isSelect) {
                    this.isSelect = null;
                    return false;
                }
                this.isSelect = id;
            },
            onChangeFilterConfig(filterConfig) {
                this.visibleFilter = false;
                this.updateFilterConfig(filterConfig);
            },
            updateFilterConfig(filterConfig) {
                this.filterConfig = filterConfig;
                const rules = {
                    id: this.ruleList.length + this.ownRulesCount,
                    key: 0,// 0自己创建 1模版，
                    title: '',
                    desc: '',
                    packageFilters: this.filterInfo?.labels || [],
                    params: this.filterInfo.params,

                };
                this.ownRulesCount += 1;
                this.ownRules.push(rules);
                this.$emit('confirmSelect', rules);
            },
            confirmSelect() {
                const item = this.ruleList.find((item) => { return item.id === this.isSelect;});
                this.$emit('confirmSelect', item);
            },
            addSelect() {
                this.openFilter();
            },
            openFilter() {
                this.filterConfig = null;
                this.visibleFilter = true;
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/abc-common.scss";

.select-visit-rules_dialog {
    &--box {
        width: 100%;
        height: 100%;
        overflow-y: overlay;

        &-line {
            display: flex;
            align-items: center;
            width: calc(100% - 12px);
            min-height: 70px;
            padding: 12px 16px;
            margin-bottom: 8px;
            cursor: pointer;
            background: $S2;
            border: 1px solid $P6;
            border-radius: var(--abc-border-radius-small);

            &:last-child {
                margin-bottom: 0;
            }

            &:hover {
                background-color: $P4;
            }

            &:active {
                background-color: $P4;
            }

            &-left {
                width: 400px;
                height: auto;
            }

            &-right {
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: flex-end;
                height: 100%;
            }

            &-delete {
                display: none;
                margin-right: 20px;
                font-size: 14px;
                color: #ff3333;
                cursor: pointer;
            }

            &-icon {
                display: none;
            }

            &-title {
                height: 24px;
                font-size: 16px;
                font-weight: bold;
                line-height: 24px;
                color: $S1;
            }

            &-desc {
                margin-top: 4px;
                font-size: 14px;
                line-height: 20px;
                color: $T2;
            }

            &-filter {
                margin-top: 4px;
                font-size: 14px;
                line-height: 20px;
                color: $T2;
            }
        }

        &-line-select {
            background: #ebf4ff;
            border: 1px solid $theme4;

            .select-visit-rules_dialog--box-line-right {
                .select-visit-rules_dialog--box-line-icon {
                    display: inline-block;
                }
            }

            &:hover {
                background: #ebf4ff !important;
                border: 1px solid $theme4 !important;

                .select-visit-rules_dialog--box-line-right {
                    .select-visit-rules_dialog--box-line-icon {
                        display: inline-block;
                    }
                }
            }
        }

        &-add {
            min-height: 64px !important;
            padding: 22px 17px !important;
            font-size: 14px;
            line-height: 20px;
            color: $B1;
            cursor: pointer;
        }
    }
}
</style>
