<template>
    <abc-modal
        :title="''"
        :value="isShowDialog"
        append-to-body
        class="dialog-payment-password"
        data-cy="member-password-dialog"
        content-styles="width: 320px; padding: 0"
        :before-close="cancel"
        :show-footer="passwordValidateSuccess"
        :show-cancel="false"
        :disabled-keyboard="true"
        :on-confirm="finish"
        @input="(val) => $emit('input', val)"
    >
        <div class="top-info">
            <div v-if="isNew" class="title">
                设置支付密码
            </div>
            <div v-else class="title">
                修改支付密码
            </div>
            <div>密码为6位数字，初始密码为手机号后6位</div>
        </div>
        <div class="password-input-wrapper">
            <div v-if="passwordValidateSuccess" class="validate-success validate-tip">
                <i class="iconfont cis-icon-chosen"></i>
                <span>密码设置成功</span>
            </div>
            <abc-input-password
                v-else-if="rePasswordVisible"
                v-model="rePassword"
                v-abc-loading="passwordValidating"
                :allow-exceed-length="true"
                @enter="onRePasswordConfirm"
                @clear="clear"
            ></abc-input-password>
            <abc-input-password
                v-else-if="passwordVisible"
                v-model="password"
                :allow-exceed-length="true"
                @clear="clear"
                @enter="onPasswordConfirm"
            ></abc-input-password>
        </div>
        <div
            v-if="!passwordValidateSuccess"
            :class="{
                'info-tip': true,
                'validation-error': isValidationError,
            }"
        >
            {{ validationMessage }}
        </div>
    </abc-modal>
</template>

<script>
    import CrmAPI from 'api/crm';
    import {
        on, off,
    } from 'utils/dom';

    export default {
        props: {
            value: Boolean,
            memberInfo: {
                type: Object,
                default() {
                    return {};
                },
            },
            isNew: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                password: '',
                rePassword: '',
                passwordValidateSuccess: null, // 验证结果
                passwordValidating: false, // 正在验证密码
                passwordShorterError: false,
                passwordLongerError: false,
                passwordMismatchError: false,
                passwordVisible: true,
                rePasswordVisible: false,
                showCloseConfirm: false,
            };
        },
        computed: {
            isShowDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            isValidationError() {
                const hasError =
                    this.passwordValidateSuccess === false ||
                    this.passwordShorterError ||
                    this.passwordLongerError ||
                    this.passwordMismatchError;

                if (hasError) {
                    if (
                        (this.passwordVisible && this.password.length === 0) ||
                        (this.rePasswordVisible && this.rePassword.length === 0)
                    ) {
                        return true;
                    }
                }

                return false;
            },
            validationMessage() {
                if (this.passwordVisible) {
                    if (this.isValidationError && this.passwordMismatchError) {
                        return '两次密码不一致，等待重新设置...';
                    } if (this.isValidationError && this.passwordShorterError) {
                        return '密码长度不够，等待重新设置...';
                    } if (this.isValidationError && this.passwordLongerError) {
                        return '密码长度超长，等待重新设置...';
                    } if (this.password.length === 0) {
                        return '等待密码设置...';
                    } if (this.password.length > 0) {
                        return '密码设置中...';
                    }
                } else if (this.rePasswordVisible) {
                    if (this.isValidationError && this.passwordShorterError) {
                        return '密码长度不够，等待重新设置...';
                    } if (this.isValidationError && this.passwordLongerError) {
                        return '密码长度超长，等待重新设置...';
                    } if (this.rePassword.length === 0) {
                        return '再次确认密码...';
                    } if (this.rePassword.length > 0) {
                        return '密码确认中...';
                    }
                }
                return '';
            },
        },
        watch: {},
        created() {
            this.password = '';
            this.rePassword = '';
        },
        mounted() {
            on(document, 'keyup', this.keyupHandle);
        },
        beforeDestroy() {
            off(document, 'keyup', this.keyupHandle);
        },
        methods: {
            keyupHandle(e) {
                if (e.keyCode === 13 && this.passwordValidateSuccess) {
                    this.finish();
                }
            },
            onPasswordConfirm() {
                this.passwordMismatchError = false;

                if (this.password.length === 6) {
                    this.passwordVisible = false;
                    this.passwordShorterError = false;
                    this.passwordLongerError = false;
                    this.rePasswordVisible = true;
                    return;
                }

                if (this.password.length < 6) {
                    this.password = '';
                    this.passwordLongerError = false;
                    this.passwordShorterError = true;
                } else if (this.password.length > 6) {
                    this.password = '';
                    this.passwordShorterError = false;
                    this.passwordLongerError = true;
                }
            },
            onRePasswordConfirm() {
                if (this.rePassword.length === 6 && this.password === this.rePassword) {
                    this.save();
                    return;
                }
                this.password = '';
                this.rePassword = '';
                this.passwordShorterError = false;
                this.passwordLongerError = false;
                this.passwordMismatchError = true;
                this.rePasswordVisible = false;
                this.passwordVisible = true;
            },
            async save() {
                this.passwordValidating = true;
                try {
                    await CrmAPI.updateMemberPassword(this.memberInfo.patientId, {
                        password: this.password,
                    });
                    this.passwordValidateSuccess = true;
                } catch (e) {
                    this.passwordValidateSuccess = false;
                } finally {
                    this.passwordValidating = false;
                }
            },
            clear() {
                this.passwordShorterError = false;
                this.passwordLongerError = false;
                this.passwordMismatchError = false;
            },
            cancel() {
                if (this.isNew && !this.passwordValidateSuccess) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        confirmText: '确定',
                        cancelText: '取消',
                        content: '是否放弃设置密码？',
                        onConfirm: async () => {
                            this.confirmClose();
                        },
                    });

                } else {
                    this.finish();
                }
            },
            confirmClose() {
                this.finish();
            },
            finish() {
                this.$emit('finish', true);
                this.isShowDialog = false;
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .dialog-payment-password {
        .abc-dialog {
            min-width: 280px;
        }

        .top-info {
            height: 88px;
            padding: 20px;
            color: $T2;
            border-bottom: 1px solid $P6;

            .title {
                margin-bottom: 5px;
                font-weight: 500;
                color: black;
            }
        }

        .cancel {
            position: absolute;
            top: 5px;
            right: 5px;
        }

        .top-tip {
            margin-bottom: 10px;
            color: #7a8794;
        }

        .password-input-wrapper {
            position: relative;
            display: flex;
            justify-content: center;
            padding: 25px 0;
        }

        .validate-tip {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 216px;
            height: 36px;
            background: rgba(249, 251, 255, 1);
            border: 2px solid $P6;
            border-radius: var(--abc-border-radius-small);

            &.validate-success {
                color: $G1;
            }

            &.validate-error {
                color: $Y2;
            }

            span {
                margin-left: 8px;
            }
        }

        .info-tip {
            margin-bottom: 25px;
            color: $T2;
            text-align: center;

            &.validation-error {
                color: $Y2;
            }
        }

        .pay-button {
            width: 100%;
        }
    }
</style>
