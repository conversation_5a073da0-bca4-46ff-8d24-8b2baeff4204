<template>
    <abc-dialog
        v-model="showDialog"
        :title="typeInfo.title"
        class="dialog-template-edit"
        append-to-body
        content-styles="width: 520px; padding: 24px"
        @input="(val) => $emit('input', val)"
    >
        <abc-form
            ref="postData"
            label-position="left"
            :label-width="80"
            item-block
        >
            <abc-form-item label="模板名称" required class="left-item">
                <abc-input
                    v-model.trim="postData.name"
                    :width="392"
                    :max-length="15"
                    trim
                    type="text"
                ></abc-input>
            </abc-form-item>
            <abc-form-item label="模板内容" required class="left-item edit-content">
                <abc-textarea
                    v-model.trim="postData.content"
                    placeholder="填写模板内容"
                    trim
                    :height="172"
                    :maxlength="500"
                ></abc-textarea>
            </abc-form-item>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button
                type="blank"
                style=" width: 80px; min-width: 80px; color: #ff3333;"
                :disabled="deleteLoading"
                :loading="deleteLoading"
                @click="onClickDelete"
            >
                删除
            </abc-button>
            <div class="track"></div>
            <abc-button
                style=" width: 80px; min-width: 80px;"
                :disabled="!isUpdated || submitLoading"
                :loading="submitLoading"
                @click="onClickConfirm"
            >
                确定
            </abc-button>
            <abc-button style=" width: 80px; min-width: 80px;" type="blank" @click="$emit('input', false)">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone';
    import CrmAPI from 'api/crm';
    import { CRM_VISIT_TEMPLATE_EDIT } from 'views/crm/common/package-template/constant';
    export default {
        name: 'DialogTemplateEdit',
        props: {
            type: {
                type: String,
                required: true,
                validator: (val) => !!CRM_VISIT_TEMPLATE_EDIT[val],
            },
            templateInfo: {
                type: Object,
                required: true,
            },
            value: Boolean,
        },
        data() {
            return {
                postData: this.getPostData(),
                deleteLoading: false,
                submitLoading: false,
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            /**
             * desc [模板id]
             */
            templateId() {
                return this.templateInfo.id;
            },
            /**
             * desc [是否对内容有修改]
             * true  => 有修改
             * false => 无修改
             */
            isUpdated() {
                return !isEqual(this.postData, this.getPostData());
            },
            /**
             * desc [匹配的类型信息]
             */
            typeInfo() {
                return CRM_VISIT_TEMPLATE_EDIT[this.type];
            },
        },
        methods: {
            /**
             * desc [创建一份需要提交]
             */
            getPostData() {
                const {
                    name, content,
                } = this.templateInfo;
                return {
                    name,
                    content,
                };
            },
            /**
             * desc [点击确认保存]
             */
            onClickConfirm() {
                this.$refs.postData.validate(async (valid) => {
                    if (valid) {
                        this.submitLoading = true;
                        const params = clone(this.postData);
                        const success = await this.handleUpdateSubmit(params);
                        this.submitLoading = false;
                        if (success) {
                            this.$Toast({
                                type: 'success',
                                message: '操作成功',
                            });
                            this.$emit('success');
                        }
                    }
                });
            },
            /**
             * desc [点击删除按钮]
             */
            async onClickDelete() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除该模板？',
                    onConfirm: async () => {
                        this.deleteLoading = true;
                        const success = await this.handleDeleteSubmit();
                        this.deleteLoading = false;
                        if (success) {
                            this.$Toast({
                                type: 'success',
                                message: '操作成功',
                            });
                            this.$emit('delete');
                        }
                    },
                });
            },
            /**
             * desc [处理修改提交]
             */
            async handleUpdateSubmit() {
                try {
                    const params = clone(this.postData);
                    params.type = this.typeInfo.type;
                    await CrmAPI.updateVisitTemplate(this.templateId, params);
                    return true;
                } catch (error) {
                    console.log('handleUpdateSubmit error', error);
                    this.$Toast({
                        type: 'error',
                        message: error.message,
                    });
                    return false;
                }
            },
            /**
             * desc [处理删除提交]
             */
            async handleDeleteSubmit() {
                try {
                    await CrmAPI.deleteVisitTemplate(this.templateId);
                    return true;
                } catch (error) {
                    console.log('handleDeleteSubmit error', error);
                    return false;
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    .dialog-template-edit {
        .abc-form-item.edit-content {
            margin-bottom: 0;

            @include flex(row, flex-start, flex-start);

            label {
                line-height: 32px;
            }
        }

        .abc-dialog-footer {
            .track {
                flex: 1;
            }
        }
    }
</style>
