export const PATIENT_MERGE_FIELD_IN_HOSPITAL = [
    {
        label: '姓名', key: 'name', alert: false, value: '',
    },
    {
        label: '性别', key: 'sex', alert: false, value: '',
    },
    {
        label: '生日', key: 'birthday', alert: false, value: '',
    },
    {
        label: '手机', key: 'mobile', alert: false, value: '',
    },
    {
        label: '微信', key: 'wxchat', alert: false, value: '',
    },
    {
        label: '证件', key: 'idCard', alert: false, value: '',
    },
    {
        label: '首诊来源', key: 'source', alert: false, value: '',
    },
    {
        label: '会员卡', key: 'member', alert: false, value: '',
    },
    {
        label: '出生地', key: 'birthAddress', alert: false, value: '',
    },
    {
        label: '籍贯', key: 'ancestralAddress', alert: false, value: '',
    },
    {
        label: '国籍', key: 'nationality', alert: false,value: '',
    },
    {
        label: '民族', key: 'ethnicity', alert: false, value: '',
    },
    {
        label: '婚否', key: 'marital', alert: false, value: '',
    },
    {
        label: '职业', key: 'profession', alert: false, value: '',
    },
    {
        label: '医保号', key: 'shebaoCardNo', alert: false, value: '',
    },
    {
        label: '户口地址', key: 'registerAddress', alert: false, value: '',
    },
    {
        label: '户口详细地址', key: 'registerAddressDetail', alert: false, value: '',
    },
    {
        label: '户口邮编', key: 'registerAddressPostCode', alert: false, value: '',
    },
    {
        label: '家庭地址', key: 'address', alert: false, value: '',
    },
    {
        label: '家庭详细地址', key: 'addressDetail', alert: false, value: '',
    },
    {
        label: '家庭邮编', key: 'addressPostCode', alert: false, value: '',
    },
    {
        label: '家庭电话', key: 'familyMobile', alert: false, value: '',
    },
    {
        label: '单位', key: 'company', alert: false, value: '',
    },
    {
        label: '单位地址', key: 'companyAddress', alert: false, value: '',
    },
    {
        label: '单位详细地址', key: 'companyAddressDetail', alert: false, value: '',
    },
    {
        label: '单位邮编', key: 'companyAddressPostCode', alert: false, value: '',
    },
    {
        label: '单位电话', key: 'companyMobile', alert: false, value: '',
    },
    {
        label: '联系人', key: 'contactName', alert: false, value: '',
    },
    {
        label: '联系人电话', key: 'contactMobile', alert: false, value: '',
    },
    {
        label: '联系人关系', key: 'contactRelation', alert: false, value: '',
    },
    {
        label: '联系人地址', key: 'contactAddress', alert: false, value: '',
    },
    {
        label: '详细地址', key: 'contactAddressDetail', alert: false, value: '',
    },
    {
        label: '体重', key: 'weight', alert: false, value: '',
    },
    {
        label: '既往史', key: 'pastHistory', alert: false, value: '',
    },
    {
        label: '过敏史', key: 'allergicHistory', alert: false, value: '',
    },
    {
        label: '到店原因', key: 'visitReason', alert: false, value: '',
    },
    {
        label: '备注', key: 'remark', alert: false, value: '',
    },
    {
        label: '首评治疗师', key: 'primaryTherapistName', alert: false, value: '',
    },
    {
        label: '责任治疗师', key: 'dutyTherapistName', alert: false, value: '',
    },
];

export const PATIENT_MERGE_FIELD_COMMON = [
    {
        label: '姓名', key: 'name', alert: false, value: '',
    },
    {
        label: '性别', key: 'sex', alert: false, value: '',
    },
    {
        label: '生日', key: 'birthday', alert: false, value: '',
    },
    {
        label: '手机', key: 'mobile', alert: false, value: '',
    },
    {
        label: '微信', key: 'wxchat', alert: false, value: '',
    },
    {
        label: '证件', key: 'idCard', alert: false, value: '',
    },
    {
        label: '首诊来源', key: 'source', alert: false, value: '',
    },
    {
        label: '会员卡', key: 'member', alert: false, value: '',
    },
    {
        label: '家庭地址', key: 'address', alert: false, value: '',
    },
    {
        label: '家庭详细地址', key: 'addressDetail', alert: false, value: '',
    },
];

const compareAddress = (one, two, withDetail = false) => {
    const noDetailCompareResult = (
        one.address.addressCityId === two.address.addressCityId &&
        one.address.addressProvinceId === two.address.addressProvinceId &&
        one.address.addressDistrictId === two.address.addressDistrictId
    );
    const onlyDetailCompareResult = one.address.addressDetail === two.address.addressDetail;
    return withDetail ? (noDetailCompareResult && onlyDetailCompareResult) : noDetailCompareResult;
};

const compareAddressDetail = (one, two, key) => {
    return one[key]?.addressDetail === two[key]?.addressDetail;
};

const compareAddressPostCode = (one, two, key) => {
    return one[key]?.addressPostcode === two[key]?.addressPostcode;
};

export const PATIENT_FIELD_EQUAL_MAP = {
    wxchat: (one, two) => {
        return one.wxOpenId === two.wxOpenId;
    },
    source: (one,two) => {
        return (
            one.patientSource.id === two.patientSource.id &&
            one.patientSource.sourceFrom === two.patientSource.sourceFrom
        );
    },
    member: () => {
        return false;
    },
    defaultCompare: (one, two, key) => {
        return one[key] === two[key];
    },
    birthAddress: (one,two) => {
        return compareAddress(one, two, true);
    },
    ancestralAddress: (one,two) => {
        return compareAddress(one, two, true);
    },
    shebaoCardNo: (one, two) => {
        return one.shebaoCardInfo?.shebaoCardNo === two.shebaoCardInfo?.shebaoCardNo;
    },
    registerAddress: compareAddress,
    address: compareAddress,
    companyAddress: compareAddress,
    contactAddress: compareAddress,
    registerAddressDetail: (one, two) => compareAddressDetail(one, two, 'registerAddress'),
    addressDetail: (one, two) => compareAddressDetail(one, two, 'address'),
    companyAddressDetail: (one, two) => compareAddressDetail(one, two, 'companyAddress'),
    contactAddressDetail: (one, two) => compareAddressDetail(one, two, 'contactAddress'),
    registerAddressPostCode: (one, two) => compareAddressPostCode(one, two, 'registerAddress'),
    addressPostCode: (one, two) => compareAddressPostCode(one, two, 'address'),
    companyAddressPostCode: (one, two) => compareAddressPostCode(one, two, 'companyAddress'),
    primaryTherapistName: (one, two) => {
        return one.primaryTherapistId === two.primaryTherapistId;
    },
    dutyTherapistName: (one, two) => {
        return one.dutyTherapistId === two.dutyTherapistId;
    },
};

const mergeAddressInfo = (mergedPatientInfo, patientInfo, filed, withDetail = false) => {
    const keys = [
        'addressCityId',
        'addressCityName',
        'addressDistrictId',
        'addressDistrictName',
        'addressProvinceId',
        'addressProvinceName',
    ];

    const target = patientInfo[filed] || {};

    const pickValue = keys.reduce((res, key) => {
        res[key] = target[key];
        return res;
    }, {});

    if (withDetail) {
        pickValue.addressDetail = target.addressDetail;
    }

    if (!mergedPatientInfo[filed]) {
        mergedPatientInfo[filed] = {};
    }
    mergedPatientInfo[filed] = {
        ...mergedPatientInfo[filed],
        ...pickValue,
    };
};

const mergeAddressDetail = (mergedPatientInfo, patientInfo, filed) => {
    if (!mergedPatientInfo[filed]) {
        mergedPatientInfo[filed] = {};
    }
    mergedPatientInfo[filed].addressDetail = patientInfo[filed]?.addressDetail || '';
};

const mergeAddressPostCode = (mergedPatientInfo, patientInfo, filed) => {
    if (!mergedPatientInfo[filed]) {
        mergedPatientInfo[filed] = {};
    }
    mergedPatientInfo[filed].addressPostcode = patientInfo[filed]?.addressPostcode || '';
};

export const PATIENT_FIELD_FORMAT_MAP = {
    birthday: (mergedPatientInfo, patientInfo) => {
        mergedPatientInfo.age = patientInfo.age;
        mergedPatientInfo.birthday = patientInfo.birthday;
    },

    wxchat: (mergedPatientInfo, patientInfo) => {
        const IS_BIND_WECHAT = 3;

        mergedPatientInfo.wxOpenId = patientInfo.wxOpenId;
        if (patientInfo.wxBindStatus === IS_BIND_WECHAT) {
            mergedPatientInfo.selectedBindPatientId = patientInfo.id;
        }
    },

    source: (mergedPatientInfo, patientInfo) => {
        if (patientInfo.patientSource) {
            mergedPatientInfo.patientSource = {
                id: patientInfo.patientSource.id,
                sourceFrom: patientInfo.patientSource.sourceFrom,
            };
        }
    },

    member: (mergedPatientInfo, patientInfo) => {
        if (patientInfo.memberFlag === 1 && patientInfo.memberInfo) {
            mergedPatientInfo.memberFlag = 1;
            mergedPatientInfo.memberPatientId = patientInfo.id;
        } else {
            mergedPatientInfo.memberFlag = 0;
            mergedPatientInfo.memberPatientId = '';
        }
    },

    defaultFn: (mergedPatientInfo, patientInfo, key) => {
        mergedPatientInfo[key] = patientInfo[key];
    },

    birthAddress: (mergedPatientInfo, patientInfo) => {
        mergeAddressInfo(mergedPatientInfo, patientInfo, 'birthAddress', true);
    },

    ancestralAddress: (mergedPatientInfo, patientInfo) => {
        mergeAddressInfo(mergedPatientInfo, patientInfo, 'ancestralAddress', true);
    },

    shebaoCardNo: (mergedPatientInfo, patientInfo) => {
        if (!mergedPatientInfo.shebaoCardInfo) {
            mergedPatientInfo.shebaoCardInfo = {};
        }
        mergedPatientInfo.shebaoCardInfo.shebaoCardNo = patientInfo.shebaoCardInfo?.shebaoCardNo;
    },

    registerAddress: (mergedPatientInfo, patientInfo) => {
        mergeAddressInfo(mergedPatientInfo, patientInfo, 'registerAddress');
    },

    address: (mergedPatientInfo, patientInfo) => {
        mergeAddressInfo(mergedPatientInfo, patientInfo, 'address');
    },

    companyAddress: (mergedPatientInfo, patientInfo) => {
        mergeAddressInfo(mergedPatientInfo, patientInfo, 'companyAddress');
    },

    contactAddress: (mergedPatientInfo, patientInfo) => {
        mergeAddressInfo(mergedPatientInfo, patientInfo, 'contactAddress');
    },

    registerAddressDetail: (mergedPatientInfo, patientInfo) => {
        mergeAddressDetail(mergedPatientInfo, patientInfo, 'registerAddress');
    },

    addressDetail: (mergedPatientInfo, patientInfo) => {
        mergeAddressDetail(mergedPatientInfo, patientInfo, 'address');
    },

    companyAddressDetail: (mergedPatientInfo, patientInfo) => {
        mergeAddressDetail(mergedPatientInfo, patientInfo, 'companyAddress');
    },

    contactAddressDetail: (mergedPatientInfo, patientInfo) => {
        mergeAddressDetail(mergedPatientInfo, patientInfo, 'contactAddress');
    },

    registerAddressPostCode: (mergedPatientInfo, patientInfo) => {
        mergeAddressPostCode(mergedPatientInfo, patientInfo, 'registerAddress');
    },

    addressPostCode: (mergedPatientInfo, patientInfo) => {
        mergeAddressPostCode(mergedPatientInfo, patientInfo, 'address');
    },

    companyAddressPostCode: (mergedPatientInfo, patientInfo) => {
        mergeAddressPostCode(mergedPatientInfo, patientInfo, 'companyAddress');
    },

    primaryTherapistName: (mergedPatientInfo, patientInfo) => {
        mergedPatientInfo.primaryTherapistId = patientInfo.primaryTherapistId;
    },

    dutyTherapistName: (mergedPatientInfo, patientInfo) => {
        mergedPatientInfo.dutyTherapistId = patientInfo.dutyTherapistId;
    },
};
