<template>
    <div class="crm-module__package-communicate__item-search-res"
        :class="{ 'active': active }">
        <img :src="patientHeadImg" alt="">
        <div class="content-box">
            <div class="top">
                <span class="names">{{ patientInfo.name }}</span>
                <span class="nickname" v-if="!!patientInfo.wxNickName">（{{ patientInfo.wxNickName }}）</span>
            </div>
            <div class="bot">{{ patientInfo.mobile }}</div>
        </div>
    </div>
</template>

<script>
import DEFAULT_HEADIMAGE from 'assets/images/Avatar_Male.png'
export default {
    name: "ItemSearchRes",
    props: {
        patientInfo: Object,
        active: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        //患者头像
        patientHeadImg() {
            const { wxHeadImgUrl } = this.patientInfo
            return wxHeadImgUrl || DEFAULT_HEADIMAGE
        }
    }
}
</script>

<style lang="scss">
@import "styles/abc-common.scss";
.crm-module__package-communicate__item-search-res {
    padding: 10px 12px;
    border-bottom: 1px solid $P6;
    background-color: #FFFFFF;
    @include flex(row, flex-start, center);
    img {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
    }
    .content-box {
        flex: 1;
        height: 40px;
        margin-left: 12px;
        @include flex(column, center, flex-start);
        .top {
            height: 20px;
            width: 100%;
            @include flex(row, flex-start, center);
            .names {
                color: $T1;
                font-size: 14px;
                display: inline-block;
                max-width: 120px;
                @include ellipsis(1);
            }
            .nickname {
                color: $T2;
                font-size: 14px;
                margin-left: 4px;
                display: inline-block;
                max-width: 85px;
                @include ellipsis(1);
            }
        }
        .bot {
            color: $T2;
            font-size: 12px;
            height: 16px;
            margin-top: 4px;
            @include ellipsis(1);
        }
    }

    &:hover:not(.active) {
        background-color: $P5;
    }
    &.active {
        background-color: $P6;
    }
}
</style>