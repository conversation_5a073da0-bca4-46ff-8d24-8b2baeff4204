<template>
    <abc-popover
        width="auto"
        :open-delay="500"
        :close-delay="0"
        :placement="placement"
        trigger="hover"
        theme="yellow"
    >
        <div slot="reference">
            <slot></slot>
        </div>

        <div>
            <div :class="['content-popover',Array.isArray(content) ? 'wMin' : '']">
                <template v-if="Array.isArray(content)">
                    <div v-for="item in content" :key="item.id + Math.random()" class="content-list">
                        <div class="name">
                            <span>{{ item.name }}</span>
                        </div>
                        <div class="desc">
                            <span>{{ item.desc }}</span>
                        </div>
                        <div class="amount">
                            <span>{{ item.len }}张</span>
                        </div>
                    </div>
                </template>
                <template v-else>
                    {{ content }}
                </template>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    export default {
        name: 'ContentPopover',
        props: {
            placement: {
                type: String,
                default: 'bottom-start',
            },
            content: {
                type: [String,Array],
            },
        },
    };
</script>


<style lang="scss">
@import "~styles/theme.scss";

.content-popover {
    max-width: 320px;

    &.wMin {
        min-width: 240px;
    }

    .content-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;

        &:last-child {
            margin-bottom: 0;
        }

        .name {
            span {
                display: inline-block;
                width: 150px;
            }
        }

        .desc {
            color: #696969;
        }
    }
}
</style>
