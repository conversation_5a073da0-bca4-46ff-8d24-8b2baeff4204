export default [
    {
        label: '模板名',
        key: 'name',
        style: {
            width: '120px',
            maxWidth: '120px',
        },
    },
    {
        label: '创建人',
        key: 'createdByName',
        style: {
            width: '86px',
            maxWidth: '86px',
        },
    },
    {
        label: '模板内容',
        width: 260,
        key: 'content',
        style: {
            minWidth: '260px',
            flex: 1,
        },
    },
    {
        label: '操作',
        key: 'handle',
        style: {
            width: '120px',
            maxWidth: '120px',
            textAlign: 'right',
        },
        // eslint-disable-next-line no-unused-vars
        headerAppendRender: (h) => {
            return <span style="display: inline-block; width: 9px;"></span>;
        },
    },
];
