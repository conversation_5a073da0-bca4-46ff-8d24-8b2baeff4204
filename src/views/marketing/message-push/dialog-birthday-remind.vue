<template>
    <abc-dialog
        class="dialog-birthday-remind"
        title="会员生日祝福"
        :value="true"
        content-styles="padding: 0px; width: 500px;"
        @input="(val) => $emit('input', val)"
    >
        <section>
            <div class="item-box">
                <label class="heig">通知实际</label>
                <div class="cont">
                    <span>会员生日当天</span>
                </div>
            </div>
            <div class="item-box">
                <label>通知方式</label>
                <div class="cont">
                    <div class="top">
                        <abc-checkbox
                            v-model="config.checkbox[0].value"
                            @input="$emit('switch', config.checkbox[0])"
                        >
                            短信通知
                        </abc-checkbox>
                    </div>
                    <div class="example-list">
                        <div
                            v-for="(item, index) in exampleList"
                            :key="index"
                            class="item-temp"
                            @click="onSwitchSmsTemplate(item.id)"
                        >
                            <abc-radio
                                :label="item.id"
                                :value="config.birthDaySmsTemplateId"
                            >
                                <span></span>
                            </abc-radio>
                            <div class="desc">
                                {{ item.content }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </abc-dialog>
</template>

<script>
    export default {
        props: {
            signName: String,
            config: Object,
        },
        computed: {
            exampleList() {
                return [
                    {
                        id: '3-1',
                        content: `【${this.signName}】某某某，在这个特殊的日子，代表全体医护人员祝你生日快乐，身体健康~`,
                    },
                    {
                        id: '3-2',
                        content: `【${this.signName}】某某某，我夜观星象，发现有福瑞之兆，掐指得知原是您生日到来，仅代表全体医护人员祝你生日快乐，身体健康~`,
                    },
                ];
            },
        },
        methods: {
            /**
             * desc [切换短信模板时]
             */
            onSwitchSmsTemplate(id) {
                this.config.birthDaySmsTemplateId = id;
                this.$emit('fetch-other', this.config, 'birthDaySmsTemplateId', id);
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/abc-common.scss";

.dialog-birthday-remind {
    section {
        padding: 24px;

        .item-box {
            @include flex(row, flex-start, flex-start);

            margin-bottom: 16px;

            >label {
                margin-right: 16px;
                font-size: 14px;
                color: $T2;
            }

            .cont {
                flex: 1;
                font-size: 14px;
                color: $T1;

                .item-temp {
                    @include flex(row, flex-start, center);

                    margin-bottom: 16px;
                    cursor: pointer;

                    .abc-radio {
                        margin-right: 6px;
                    }

                    .desc {
                        flex: 1;
                        padding: 10px 12px;
                        font-size: 12px;
                        line-height: 1.6;
                        color: $T3;
                        background-color: $P4;
                        border-radius: var(--abc-border-radius-small);
                    }
                }
            }

            .example-list {
                margin-top: 14px;
            }
        }

        :last-child {
            margin-bottom: 0;
        }
    }
}
</style>
