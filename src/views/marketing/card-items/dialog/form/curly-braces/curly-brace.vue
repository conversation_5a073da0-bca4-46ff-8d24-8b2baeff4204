<template>
    <div class="brace-wrapper">
        <div ref="content" class="brace-content">
            <slot></slot>
        </div>

        <svg
            class="brace"
            width="6"
            :style="{ transform: `translateY(${deltaY}px)` }"
            :height="height"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path :d="path" :stroke="color" fill="transparent" />
        </svg>

        <slot name="right"></slot>
    </div>
</template>

<script>
    let ro;

    export default {
        props: {
            deltaH: {
                type: Number,
                default: 0,
            },

            deltaY: {
                type: Number,
                default: 0,
            },

            color: {
                type: String,
                default: 'black',
            },
        },

        data() {
            return {
                height: 100,
            };
        },

        computed: {
            path() {
                const x = 0,
                      y = 0,
                      { height } = this;
                const p1 = [x + 4, y];
                const p2 = [x + 2, y + height / 4];
                const middle = [x + 6, y + height / 2];
                return (
                    `M${x},${y} ` +
                    `Q${p1[0]},${p1[1]} ${p2[0]},${p2[1]} T${middle[0]},${middle[1]}` +
                    `M${middle[0]},${middle[1]} ` +
                    `Q${x},${y + height / 2} ${p2[0]},${p2[1] + height / 2} T${x},${
                        y + height
                    }`
                );
            },
        },

        watch: {
            deltaH () {
                this.rerender();
            },
        },

        mounted() {
            ro = new ResizeObserver(() => {
                this.rerender();
            });

            ro.observe(this.$refs.content);
        },

        beforeDestroy() {
            ro.disconnect();
        },

        methods: {
            rerender() {
                this.height = this.getContentHeight() + this.deltaH;
            },

            getContentHeight() {
                return this.$refs && this.$refs.content && this.$refs.content.getClientRects()[0].height;
            },
        },
    };
</script>

<style scoped>
.brace-wrapper {
    display: flex;
    align-items: center;
}

.brace {
    margin-right: 20px;
    margin-left: 20px;
}
</style>
