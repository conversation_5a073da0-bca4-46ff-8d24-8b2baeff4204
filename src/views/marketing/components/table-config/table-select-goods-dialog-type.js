import BaseProTable from '@/views/layout/tables/base-pro-table.js';

export default class SelectGoodsDialogTypeTable extends BaseProTable {
    name = 'SelectGoodsDialogTable';

    // 由产品.设计提供的静态配置, 开发只能修改key
    static staticConfig = {
        hasInnerBorder: false,
        list: [
            {
                label: ' ',
                isCheckbox: true,
                style: {
                    flex: 'none',
                    width: '40px',
                    maxWidth: '',
                    paddingLeft: '',
                    paddingRight: '',
                    textAlign: 'left',
                },
            },
            {
                key: 'displayName',
                label: '名称',
                style: {
                    width: '236px',
                    minWidth: '236px',
                    textAlign: 'left',
                },

            },
            {
                key: 'typeName',
                label: '类型',
                style: {
                    flex: '1',
                    width: '96px',
                    minWidth: '96px',
                    textAlign: 'left',
                },

            }, {
                key: 'packagePrice',
                label: '售价',
                style: {
                    width: '84px',
                    minWidth: '84px',
                    textAlign: 'right',
                },

            }, {
                key: 'profitRat',
                label: '毛利率',
                sortable: true,
                style: {
                    width: '76px',
                    minWidth: '76px',
                    textAlign: 'center',
                },
            }, {
                key: 'stockPieceCount',
                label: '可售库存',
                style: {
                    width: '76px',
                    minWidth: '76px',
                    textAlign: 'center',
                },
            },
        ],
    };
}
