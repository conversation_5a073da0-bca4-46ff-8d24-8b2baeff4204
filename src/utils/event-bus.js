export class EventBus {
    constructor (vue) {
        this.Vue = vue;

        /**
         * _uid和event name的映射
         * 在Vue init 时候，每个vue实例都有一个_uid唯一标识
         */
        this.eventMapUid = {};
    }

    $on (eventName, callback, vm) {
        /**
         * vm是在组件内部使用时组件当前的this用于取_uid
         */
        const uid = vm._uid;

        if (vm instanceof this.Vue) {
            if (!this.eventMapUid[uid]) this.eventMapUid[uid] = {};

            if (!this.eventMapUid[uid][eventName]) this.eventMapUid[uid][eventName] = [];

            this.eventMapUid[uid][eventName].push(callback);
        }
    }

    $emit (...args) {
        const [eventName, ...params] = [...args];

        Object.keys(this.eventMapUid).forEach((uid) => {
            if (this.eventMapUid[uid][eventName]?.length) {
                this.eventMapUid[uid][eventName].forEach((event) => {
                    event(...params);
                });
            }
        });
    }

    $offVmEvent (uid) {
        const currentInstanceEvents = this.eventMapUid[uid] || {};

        if (!Object.keys(currentInstanceEvents).length) return;

        delete this.eventMapUid[uid];
    }
}

const AbcEventBus = {};

AbcEventBus.install = (Vue) => {
    Vue.prototype.$abcEventBus = new EventBus(Vue);

    Vue.mixin({
        beforeDestroy () {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
    });
};

export default AbcEventBus;
