import clone from '@/utils/clone';
import Logger from '@/utils/logger';


// function isJSON(obj) {
//     return typeof (obj) === 'object' && Object.prototype.toString.call(obj).toLowerCase() === '[object object]' && !obj.length;
// }
function isEmptyObject(obj) {
    if (!obj) {
        return true;
    }
    if (obj.length && obj.length > 0) {
        return false;
    }
    for (const key in obj) {
        if (hasOwnProperty.call(obj, key)) {
            return false;
        }
    }
    return true;
}

const Storage = window.localStorage;

const DataKeeper = Object.create(null);
const Expires = 3600 * 24 * 30 * 1000;
/**
 * 设置存储
 * @param key
 * @param value [value===null时,删除, false, undefined 等值无效, 0有效]
 * @param isObj
 */
const SetStore = function (key, value, isObj = false) {
    try {
        if (typeof key === 'string') {
            if (value === null) {
                Storage.removeItem(key);
                delete DataKeeper[ key ];
            } else if (value || value === 0 || value === '') {
                let val;
                if (isObj) {
                    val = value;
                } else {
                    val = {
                        value,
                        expires: Expires,
                        time: new Date().getTime(),
                    };
                }

                DataKeeper[ key ] = clone(val);
                Storage.setItem(key, JSON.stringify(val));
            }
        }

        return this;
    } catch (error) {
        console.error(error);

        Logger.report({
            scene: 'LocalStorage',
            data: {
                info: 'SetStore',
                data: {
                    key,
                    error,
                },
            },
        });

        return undefined;
    }
};

/**
 * 获取
 * @param {string}  key
 * @param {Boolean} isJson [是否是JSON数据]
 * @param {Boolean} isObj  [是否是Object结构]
 */
const GetStore = function (key, isJson, isObj = false) {
    try {
        if (key && typeof key === 'string') {
            const info = DataKeeper[ key ];
            if (info !== undefined) {
                if (new Date().getTime() - info.time > info.expires) {
                    delete DataKeeper[ key ];
                } else {
                    if (isObj) {
                        return info;
                    }
                    return info.value;

                }
            }

            let data = Storage.getItem(key);
            if (!data) { return null; }
            if (isJson) {
                data = JSON.parse(data);
            }
            if (new Date().getTime() - data.time > data.expires) {
                Storage.removeItem(key);
                delete DataKeeper[ key ];

            } else {

                DataKeeper[ key ] = data;

                if (isObj) {
                    return data;
                }
                return data.value;

            }

        }
        return undefined;
    } catch (err) {
        console.error(err);

        Logger.report({
            scene: 'getStore',
            data: {
                info: 'getStore',
                data: {
                    key,
                    error: err,
                },
            },
        });

        Storage.removeItem(key);
        return undefined;
    }
};

/**
 * 设置存储
 * @param {string} key localStorage key
 * @param {string} objKey obj的key a = {name : 1} name === objKey
 * @param {type} objVal [value===null时,删除, false, undefined 等值无效, 0有效]
 * 过期时间 默认 一个月毫秒数
 */
const setObj = function (key, objKey, objVal) {
    try {
        if (typeof key === 'string') {

            if (objVal === null) {
                // 传入null 时 清空 local相应字段
                const data = GetStore(key , true, true);
                if (data && data[objKey]) {
                    delete data[objKey];
                }
                SetStore(key, isEmptyObject(data) ? null : data, true);
            } else {

                let data = GetStore(key, true, true);
                if (!data) {
                    data = {};
                }

                data[objKey] = objVal;
                SetStore(key, data , true);
            }

        } else {
            console.error('localStorage key 必须为字符串');
        }

        return this;
    } catch (error) {
        console.error(error);

        Logger.report({
            scene: 'localStorage',
            data: {
                info: 'setObj',
                data: {
                    key,
                    error,
                },
            },
        });

        return undefined;
    }
};

/**
 * 获取
 * @param {string}  key
 * @param {string}  objKey
 * @param {Boolean} isJson [是否是JSON数据]
 */
const getObj = function (key, objKey, isJson) {
    try {
        if (key && typeof key === 'string' && objKey && typeof objKey === 'string') {

            const info = DataKeeper[ key ];

            if (info !== undefined && info[objKey]) {

                const obj = info[ objKey ];

                return obj;

            }

            let data = Storage.getItem(key);
            if (!data) { return null; }
            if (isJson) {
                data = JSON.parse(data);
            }
            const obj = data[objKey];
            if (!obj) { return null; }
            if (typeof obj === 'object' && obj.value) {
                return obj.value;
            }
            return obj;


        }
        return undefined;
    } catch (err) {
        console.error(err);

        Logger.report({
            scene: 'localStorage',
            data: {
                info: 'getObj',
                data: {
                    key,
                    error: err,
                },
            },
        });

        Storage.removeItem(key);
        return undefined;
    }
};


const getBoolean = function(key, defaultValue = false) {
    try {
        const item = Storage.getItem(key);
        if (item === null) {
            return defaultValue;
        }
        if (item === 'true') {
            return true;
        }
        return false;
    } catch (error) {
        console.error(error);

        Logger.report({
            scene: 'LocalStorage',
            data: {
                info: 'getBoolean',
                data: {
                    key,
                    error,
                },
            },
        });

        return false;
    }
};

const setBoolean = function (key, value) {
    try {
        Storage.setItem(key, !!value);
    } catch (error) {
        console.error(error);

        Logger.report({
            scene: 'LocalStorage',
            data: {
                info: 'setBoolean',
                data: {
                    key,
                    error,
                },
            },
        });
    }
};

const removeItem = function (key) {
    try {
        if (key) {
            Storage.removeItem(key);
        }
    } catch (error) {
        console.error(error);

        Logger.report({
            scene: 'LocalStorage',
            data: {
                info: 'removeItem',
                data: {
                    key,
                    error,
                },
            },
        });
    }
};

export default {
    set: SetStore,
    get: GetStore,
    setObj,
    getObj,
    getBoolean,
    setBoolean,
    removeItem,
};
