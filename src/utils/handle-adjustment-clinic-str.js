export function handleAdjustmentClinicStr(item) {
    const {
        modifyClinic = {},
    } = item || {};
    const {
        affectedClinicList = [],
    } = item || {};
    const {
        chainId = '', clinicId = '',
    } = modifyClinic;
    // 代表是门店调价
    if (chainId === clinicId) {
        // 排除总部
        const handleAffectedClinicList = affectedClinicList.filter((it) => {
            return it.clinicId !== it.chainId;
        });
        return `连锁统一定价门店(${handleAffectedClinicList.length}家)`;
    }
    return modifyClinic.shortName || modifyClinic.name;
}
