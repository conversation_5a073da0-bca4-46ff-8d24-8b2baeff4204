/*
 * <AUTHOR> 
 * @DateTime 2020-12-30 13:56:52 
 */
export default class ScanCode {
    static INPUT_SPEED_THRESHOLD = 300;
    static Status = {
        'INIT': 0,
        'SCANNING': 1,
        'SUCCESS': 2,
        'ERROR': -1,
    }
    constructor(options, listener) {
        this.el = options.el;
        this.validators = options.validators; // 必传
        this.regExp = options.regExp; // 必传
        this.listener = listener;
        
        this.keypressListener = null;
        this.timeoutTimer = null;
    }
    
    startDetect() {
        if (this.keypressListener) {
            return;
        }
        if (typeof window != "undefined" && window) {
            this.reset();
            this.keypressListener = ( e ) => {
                let curTime = new Date();
                let curKey = e.key;
                
                if (this.timeoutTimer) {
                    this.stopTimer();
                    this.startTimer(e);
                } else {
                    this.startTimer(e);
                }
                
                let deltaTime = curTime - this.lastTime;
                
                if (this.buffer.length >= 3 && this.status !== ScanCode.Status.SCANNING && !this.isEnd) {
                    console.info( '开始检测 ' + this.buffer );
                    this.notifyListener(e, ScanCode.Status.SCANNING);
                }
                
                if (this.lastTime && deltaTime < ScanCode.INPUT_SPEED_THRESHOLD) {
                    if (curKey !== 'Enter' && this.regExp.test( curKey )) {
                        this.buffer += curKey;
                    }
                    // console.log( '快速, buffer: ' + this.buffer );
                } else {
                    this.reset();
                    console.log( '慢速', this.buffer, deltaTime );
                    if (curKey !== 'Enter' && this.regExp.test( curKey )) {
                        this.buffer += curKey;
                    }
                }
                
                if (curKey === 'Enter') {
                    if (this.isEnd) {
                        console.info( '健康码已经检测到，该 Enter 吃掉，不给 input' + this.buffer );
                        e.preventDefault();
                        e.stopImmediatePropagation();
                        this.isEnd = false;
                        this.stopTimer();
                        this.reset();
                    }
                } else {
                    // 扫码结果可能有多种结构，因此多种验证函数时，不走下面的逻辑
                    if (this.validators.length === 1) {
                        const [validator] = this.validators || []
                        if (validator) {
                            if (validator(this.buffer)) {
                                this.isEnd = true;
                                e.preventDefault();
                                e.stopImmediatePropagation();
                                console.info( '扫码数据获取结束: ' + this.buffer );
                                this.notifyListener(e, ScanCode.Status.SUCCESS);
                                this.stopTimer();
                            } else {
                                this.isEnd = false;
                            }
                        }
                    }
                }
                this.lastTime = curTime;
            };
            window.document.addEventListener( 'keydown', this.keypressListener, true );
        }
    }
    
    notifyListener(e, status) {
        this.status = status;
        this.listener && this.listener( e, status, this.buffer );
    }
    
    startTimer(e) {
        this.timeoutTimer = setTimeout(() => {
            console.log('执行 timer, 当前状态：', this.status);
            if (this.status === ScanCode.Status.SCANNING) {
                const valid = this.validators.some((validator) => validator(this.buffer))
                if (valid) {
                    this.notifyListener(e, ScanCode.Status.SUCCESS);
                } else {
                    this.notifyListener(e, ScanCode.Status.ERROR);
                }
                this.reset();
            }
            this.timeoutTimer = null;
        }, 500);
    }
    
    stopTimer() {
        if (this.timeoutTimer) {
            clearTimeout(this.timeoutTimer);
            this.timeoutTimer = null;
        }
    }
    
    reset() {
        this.lastTime = null;
        this.buffer = '';
        this.status = ScanCode.Status.INIT;
    }
    
    stopDetect() {
        this.reset();
        if (this.keypressListener) {
            window.document.removeEventListener( 'keydown', this.keypressListener, true );
            this.keypressListener = null;
        }
        this.listener = null;
    }
}
