<template>
    <abc-autocomplete
        ref="autoComplete"
        v-model="searchKey"
        v-abc-focus-selected
        :placeholder="placeholder"
        :width="width"
        :inner-width="660"
        :delay-time="0"
        :fetch-suggestions="medicineSearch"
        :async-fetch="true"
        :auto-focus-first="autoFocusFirst"
        :focus-show="focusShow"
        :clearable="clearable"
        custom-class="inventory-suggestion-wrapper"
        @enterEvent="selectGoods"
        @blur="handleBlur"
        @focus="handleFocus"
        @enter="enter"
        @clear="handleClear"
    >
        <template
            slot="suggestions"
            slot-scope="{
                suggestion, index, currentIndex
            }"
        >
            <dt
                class="suggestions-item"
                :class="{
                    selected: index === currentIndex,
                    'is-disabled': suggestion.v2DisableStatus || suggestion.disabled
                }"
                @click="selectGoods(suggestion)"
            >
                <div
                    style="width: 206px; min-width: 206px; max-width: 206px; padding-right: 10px;"
                    class="ellipsis"
                    :title="suggestion | goodsFullName"
                >
                    {{ suggestion | goodsFullName }}
                </div>
                <div style="width: 140px; padding-right: 10px;" class="ellipsis" :title="suggestion | goodsDisplaySpec">
                    {{ suggestion | goodsDisplaySpec }}
                </div>
                <div v-if="withStock && showStock" style="width: 94px; padding-right: 10px; text-align: right;">
                    {{ showAssignGoodsCount(suggestion) }}
                </div>
                <div style="flex: 1; padding-right: 10px;" class="ellipsis" :title="suggestion.manufacturer">
                    {{ suggestion.manufacturer }}
                </div>
                <div style="width: 60px;">
                    <template v-if="suggestion.v2DisableStatus">
                        已停用
                    </template>
                </div>
            </dt>
        </template>
        <slot slot="prepend" name="prepend"></slot>
        <slot slot="append" name="append"></slot>
    </abc-autocomplete>
</template>
<script>
    import * as repository from 'MfFeEngine/repository';

    import GoodsAPI from 'api/goods';
    import EnterEvent from 'views/common/enter-event';
    import BarcodeHandler from 'src/views/inventory/mixins/barcode-handler';
    import { isBarcode } from '@/utils';
    import { showAssignGoodsCount } from 'views/inventory/goods-utils';
    import { GoodsTypeEnum } from '@abc/constants';

    export default {
        name: 'GoodsAutoComplete',
        mixins: [EnterEvent, BarcodeHandler],
        props: {
            search: String,
            placeholder: String,
            isOut: Boolean,
            formatCountKey: String,// 'out'
            withStock: {
                type: Boolean,
                default: true,
            },
            showStock: {
                type: Boolean,
                default: true,
            },
            needFilterDisable: {
                // 是否需要过滤停用的药品
                type: Boolean,
                default: false,
            },
            onlyStock: {
                type: Boolean,
                default: false,
            },
            clinicId: {
                type: String,
                default: '',
            },
            clearSearchKey: {
                type: Boolean,
                default: true,
            },
            enableBarcodeDetector: {
                type: Boolean,
                default: true,
            },
            filter: {
                type: Function,
                default: null,
            },
            autoFocusFirst: {
                type: Boolean,
                default: true,
            },
            width: {
                type: Number,
                default: 200,
            },
            focusShow: {
                type: Boolean,
                default: false,
            },
            clearable: {
                type: Boolean,
                default: false,
            },
            type: {
                type: [String, Number],
                default: '',
            },
            // 采购模块支持西成药，允许多个subType 和 subType的参数
            isTypeArr: {
                type: Boolean,
                default: false,
            },
            typeArr: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            subType: {
                type: [String, Number],
                default: '',
            },
            subTypeArr: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            cMSpec: {
                type: [String, Number],
                default: '',
            },
            customTypeIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            typeIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            // 支持模糊搜索
            supportKeywordSearch: {
                type: Boolean,
                default: false,
            },
            inorderConfig: {
                type: Number,
            },
            pharmacyNo: {
                type: [String, Number],
            },
            enableLocalSearch: {
                type: Boolean,
                default: false,
            },
            // 是否搜索SPU
            isSpu: {
                type: Boolean,
                default: false,
            },
            formatSearchResultFn: {
                type: Function,
                default: (res) => res,
            },
            needSearchNoStock: {
                type: Boolean,
                default: true,
            },

            currentClinic: {
                type: Object,
            },
            isSingleStore: {
                type: [Boolean, Number],
                required: true,
            },
            isChainAdmin: {
                type: [Boolean, Number],
                required: true,
            },
            hasEyeglasses: {
                type: [Boolean, Number],
                default: false,
            },
        },
        data() {
            return {
                searchKey: '',
            };
        },
        watch: {
            searchKey(v) {
                this.$emit('update:search', v);
                if (!v) {
                    this.$emit('clear');
                }
            },
            search(v) {
                this.searchKey = v;
            },
            enableBarcodeDetector: {
                handler(v) {
                    // console.log('watch enableBarcodeDetector, v ' + v);
                    if (v) {
                        this.createBarcodeDetector(this.onDetectBarcode);
                    } else {
                        this.destroyBarcodeDetector();
                    }
                },
                immediate: true,
            },
        },

        mounted() {
            // console.log('GoodsAutoComplete.mounted');
            this.createBarcodeDetector(this.onDetectBarcode);
        },
        methods: {
            showAssignGoodsCount(goods) {
                if (this.formatCountKey) {
                    return showAssignGoodsCount(goods,this.formatCountKey);
                }
                return showAssignGoodsCount(goods);
            },
            handleClose() {
                this.$refs.autoComplete.handleClose();
            },
            async enter(e, selectItem) {
                e.preventDefault();
                e.stopPropagation();
                if (!selectItem && isBarcode(e.currentTarget.value)) {
                    // 条形码搜索
                    this.handleBarcode(e.currentTarget.value);
                    this.$emit('searchGoods', '');
                } else if (!selectItem && e.currentTarget.value.trim()) {
                    // 关键字搜索
                    this.$emit('searchGoods', e.currentTarget.value);
                } else {
                    this.enterEvent(e);
                    if (this.supportKeywordSearch) {
                        this.$emit('searchGoods', '');
                    }
                }
                // 敲回车后药品推荐弹窗收起
                this.handleClose();
            },

            async handleBarcode(barcode) {
                this.searchKey = barcode;
                try {
                    const { data } = await GoodsAPI.search({
                        key: barcode,
                        withStock: this.withStock,
                        onlyStock: this.onlyStock,
                        clinicId: this.clinicId,
                        needFilterDisable: this.needFilterDisable,
                        inorderConfig: this.inorderConfig,
                        pharmacyNo: this.pharmacyNo,
                        customTypeId: this.customTypeIdList,
                        typeId: this.typeIdList,
                    });
                    const ret = (data && data.list) || [];
                    if (ret.length) {
                        this.selectGoods(ret[0]);
                        if (this.clearSearchKey) {
                            this.searchKey = '';
                        }
                    } else {
                        this.errorHandler();
                    }
                } catch (err) {
                    if (err.code === 12015) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: err.message,
                        });
                    }
                // this.errorHandler(err);
                }
            },

            errorHandler(err) {
                $(this.$el).find('input').blur();
                if (this.isOut) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: err && err.message ? err.message : '未找到该药品的入库记录，请先入库再进行操作',
                        onClose: () => {
                            this.searchKey = '';
                            $(this.$el).find('input').focus();
                        },
                    });
                } else {
                    // todo 未入库
                    if (this.isSingleStore) {
                        // console.log('这个不是连锁哦')
                        // console.log('未入库barCode', this.searchKey)
                        this.$emit('hasNoMedicine', this.searchKey);
                    } else {
                        // console.log('这个是连锁哦')
                        this.$alert({
                            type: 'warn',
                            title: err && err.message ? err.message : '药品条码信息未录入，无法识别',
                            content: err && err.message ? '' : ['请联系药品管理员补充条码信息'],
                            onClose: () => {
                                this.searchKey = '';
                                $(this.$el).find('input').focus();
                            },
                        });
                    }
                }
            },

            async medicineSearch(key, callback) {
                key = key.trim();
                if (key) {
                    this._goods_repo_instance = repository.GoodsRepositoryService.getInstance();

                    let {
                        pharmacyNo,
                        onlyStock,
                        enableLocalSearch,
                    } = this;
                    let defaultType = this.isTypeArr ? this.typeArr : this.type;
                    // 本地搜索参数处理
                    if (enableLocalSearch && this._goods_repo_instance?.searchConfig?.useLocalSearchGoodsAPI) {
                        // 总部启用本地搜索的情况下，不带药房号进行搜索（总部没入库的会搜不出)
                        if (this.isChainAdmin) {
                            pharmacyNo = '';
                            if (this.needSearchNoStock) {
                                onlyStock = '';
                            }
                            // 总部指定门店搜索时，不走本地搜索
                            if (this.clinicId && this.clinicId !== this.currentClinic.clinicId) {
                                enableLocalSearch = false;
                            }
                        }
                        // 保证本地搜索只搜药品物资商品眼镜
                        if (!defaultType || !defaultType.length) {
                            defaultType = [GoodsTypeEnum.MEDICINE, GoodsTypeEnum.GOODS, GoodsTypeEnum.MATERIAL];
                            if (this.hasEyeglasses) {
                                defaultType.push(GoodsTypeEnum.EYEGLASSES);
                            }
                        }
                    }
                    const res = await this._goods_repo_instance.searchStockGoods({
                        key,
                        isSpu: this.isSpu,
                        withStock: this.withStock ? 1 : '',
                        onlyStock: onlyStock ? 1 : '',
                        clinicId: this.clinicId,
                        type: defaultType,
                        subType: this.isTypeArr ? this.subTypeArr : this.subType,
                        cMSpec: this.cMSpec,
                        disable: this.needFilterDisable ? 0 : '',
                        inorderConfig: this.inorderConfig,
                        pharmacyNo,
                        customTypeId: this.customTypeIdList.length ? this.customTypeIdList.map(Number) : '',
                        typeId: this.typeIdList.length ? this.typeIdList.map(Number) : '',
                        limit: 100,// 默认只展示40条，可能要搜索在40条后，导致看不到。
                    }, enableLocalSearch, this.hasEyeglasses ? 2 : 1);

                    let list = this.formatSearchResultFn(res?.list ?? []);
                    if (this.$props.filter) {
                        list = this.$props.filter(list);
                    }
                    callback(list);
                } else {
                    callback([]);
                }
            },
            async selectGoods(goods) {
                if (!goods) {
                    return false;
                }
                if (goods.disabled) {
                    return false;
                }
                this.$emit('selectGoods', goods);
            },
            handleBlur(event) {
                this.$emit('blur', event);
                this.blurBarcode(event);
            },
            handleFocus(event) {
                this.$emit('focus', event);
                this.focusBarcode(event);
            },
            handleClear() {
                this.searchKey = '';
                this.$emit('clear');
            },
            blurBarcode() {
                // console.log('goods.blurBarcoode');
                this.createBarcodeDetector(this.onDetectBarcode);
            },

            onDetectBarcode(e, barcode) {
                // console.log('goods.detect, barcode = ', barcode, ', e = ', e);
                // this.searchKey = barcode;
                this.$nextTick(() => {
                    this.handleBarcode(barcode);
                });
                // 抛出事件，让业务能够自由处理
                this.$emit('enterBarcode', barcode, e);
            },

            focusBarcode() {
                // console.log('goods.focusBarcode');
                this.destroyBarcodeDetector();
            },
        },
    };
</script>
<style lang="scss">
.abc-autocomplete-wrapper {
    .append-input i {
        color: var(--abc-color-T3) !important;
    }
}

.inventory-suggestion-wrapper {
    .suggestions-item {
        &.is-disabled {
            color: var(--abc-color-T2);
        }
    }
}
</style>
