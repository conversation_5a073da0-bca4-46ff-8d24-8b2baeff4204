## 系统功能 使用权

这里把系统中对于`版本`、`购买功能`等影响功能的因素统一收敛为`使用权`   
版本到期默认统一失去`使用权`

`使用权`按粒度可区分：
- 模块级别权限
- 组件级别权限
- 接口级别权限

#### 模块级别权限
等价于路由权限，决定用户能否访问该路由模块，影响路由生成；

#### 组件级别权限
决定该组件的可见性

#### 接口级别权限
决定该接口能否被发起，又细分为：
1. 发起`修改资源`请求前用户表单的拦截，提醒，可以对接口调用前的操作步骤前置进行拦截
2. `get`请求的拦截

`模块级别权限`在路由定义、生成处限制；`组件级别权限`、`接口级别权限`可由高阶组件拦截

### `使用权`可能共同且交叉存在  
比如`多屏叫号系统`在没开通的情况下  
- 对应`模块级别权限`，关闭了`设置模块`中 `叫号设置` 功能的展示
- 对应`组件级别权限`，关闭了`门诊模块`中 `小工具`-`叫号` 的展示
- 对应`接口级别权限`，阻止了各模块中对于 `叫号列表` 接口的拉取