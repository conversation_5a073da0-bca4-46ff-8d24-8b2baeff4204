import PrintAPI from 'api/print';
import PropertyAPI from 'api/property';
import Store from 'utils/localStorage-handler';

const PRINT_CONFIG = '_print_config_';

const PRINTER_CONFIG = '_printer_config_';

const Horizontal = 'horizontal';
const clinic = {
    state: {
        config: Store.get(PRINT_CONFIG, true) || {
            registration: null,
            cashier: null,
            inventory: null,
        },

        printerConfig: Store.get(PRINTER_CONFIG, true) || {
            pharmacy: {
                prescription: null, // 处方
                dispensing: null, // 发药单
                infusionExecute: null, // 输液注射单
                medicineTag: null, // 用药标签
                patientTag: null, // 患者标签
            },
        },
        cashier: {
            patientInfo: {
                sex: 1, // 性别
                age: 1, // 年龄
                mobile: 1, // 手机号
                patientOrderNo: 1, // 诊号
                doctor: 1, // 医生
            },
            feeInfo: {
                totalAmount: 0, // 只打印所有费用合计 1：显示 合计费用，不显示具体项目
                examination: 2, // 检查检验
                treatment: 2, // 治疗理疗
                westernMedicine: 2, // 西药项目
                chinesePatentMedicine: 2, // 中成药项目
                chineseMedicine: 2, // 中药项目
                materialGoods: 2, // 材料/商品
            },
            chinese: {
                position: 0, // 柜号
                specialRequirement: 0, // 煎法
            },
            cashierInfo: {
                totalFee: 1, // 合计
                discountFee: 1, // 优惠
                receivableFee: 1, // 应付
                netIncomeFee: 1, // 实付
            },

            clinicInfo: {
                chargeOperator: 1, // 收费员
                chargeDate: 1, // 收费日期
                address: 1, // 地址
                mobile: 1, // 电话
                qrCode: 1, // 二维码
                replacementReminder: 1, // 退换提醒
            },
            composeDetail: 0, // 套餐是否打印详情
        },
        registration: {
            registrationInfo: {
                doctor: 1, // 医生
                department: 1, // 科室
            },
            feeInfo: {
                amount: 1, // 金额
            },
            outpatientInfo: {
                outpatientDate: 1, // 就诊日期
                outpatientTime: 1, // 就诊时间
                consultingRoom: 1, // 就诊诊室
            },
            ticketFooter: {
                operator: 1, // 操作员
                reserveDate: 1, // 预约日期
                address: 1, // 地址
                mobile: 1, // 电话
                expiredReminder: 1, // 过期提醒
                invoiceCode: 0, // 查看电子发票
            },
        },
        registrationTag: {
            enable: 0,
        },
        dispensing: {
            patientInfo: {
                sex: 1, // 性别
                patientOrderNo: 1, // 诊号
                doctor: 1, // 医生
            },
            westernMedicine: {
                spec: 0,
            },
            chineseMedicine: {
                spec: 0,
                position: 1,
                specialRequirement: 0,
            },
            ticketFooter: {
                operator: 1, // 操作员
                reviewDispense: 1, // 审核发药
                printDate: 1, // 打印日期
                printTime: 1, // 打印时间
                address: 1, // 地址
                mobile: 1, // 电话
                replacementReminder: 1, // 退换提醒
            },
        },
        prescriptionConfig: {
            // perKindCount: 5, // 单张处方药品  5：不超过 5 种  999 按处方实际药品种数打印
            // mobile: 1, // 患者手机号 0 不打印 1 打印
            // amount: 1, // 处方总金额 0 不打印 1 打印
            // logo: 0, // 品牌logo

            mobile: 1, // 患者手机号 0 不打印 1 打印
            feeType: 0, // 费别
            socialCode: 0, // 医保卡号
            personalCode: 1,
            logo: 0, // 品牌logo
            latin: 0, // 医嘱是否采用拉丁文
            medicalLatin: 0, // 病历医嘱采用拉丁文

            medical: {
                medicalItem: 1, // 打印处置内容
            },
            pr: {
                standardKindCount: 1, // 1, 单张处方不超过5中， 0 超过5种，
                amount: 0, // 打印金额  0, 不打印  1 打印
                amountType: 1, // 1 药品总金额 2 药品和诊疗项目的总金额
                doctorAdvice: 1, // 是否打印医嘱
            },
            infusionExecute: {
                spec: 1, // 输注单规格
                totalCount: 0, // 输注单药品总量
                includeTreatment: 0, // 是否包含治疗项目
                tradeName: 0, // 打印药品的商品名
            },
            treatmentExecute: {
                unitPrice: 1, // 治疗单 项目单价
                totalPrice: 1,
            },
            examination: {
                unitPrice: 1, // 检验申请单 项目单价
                totalPrice: 1,
            },
        },
        inventoryGoodsIn: {
            header: {
                // 抬头信息
                goodsInStore: 1, // 入库门店
                goodsInWarehouse: 1, // 入库库房
                goodsInDate: 1, // 入库日期
                goodsInOrder: 1, // 入库单号
                supplier: 1, // 供应商
                supplierOrder: 1, // 随货单号
            },
            content: {
                // 药品信息
                goodsInfo: {
                    goodsCode: 1, // 药品编码
                    goodsName: 1, // 药品名称
                    goodsSpec: 1, // 规格
                    goodsManufacturer: 1, // 厂家
                    goodsType: 1, // 类型
                    goodsSecondClass: 1, // 二级分类
                    goodsPrice: 1, // 售价
                    goodsTaxRate: 1, // 税率
                },
                // 入库信息
                goodsInInfo: {
                    goodsBatch: 1, // 生产批号
                    goodsProductionDate: 1, // 生产日期
                    goodsExpiryDate: 1, // 效期
                    goodsNumber: 1, // 数量
                    goodsPurchasePrice: 1, // 进价
                    goodsTaxAmount: 1, // 含税金额
                    goodsNoTaxAmount: 1, // 无税金额
                    goodsGrossProfitRate: 1, // 预计毛利率
                },
            },
            footer: {
                // 统计信息
                statisticInfo: {
                    totalAmount: 1, // 合计金额（大写）
                    noTaxTotal: 1, // 无税合计
                    totalVariety: 1, // 合计品种
                },
                // 签字行
                signInfo: {
                    consignee: 1, // 收货人
                    acceptor: 1, // 验收人
                    quality: 1, // 质量情况
                    custodian: 1, // 保管人
                    maker: 1, // 制单人
                },
            },
        },
        isPrintConfigInited: false,

        billConfig: {
            format: '', // 省市的模板
            type: 0, // 票据类型  0 财政发票  1 税务发票
            guangdong: {
                chargeInstitution: 0, // 是否打印收费单位
                institutionName: '', // 收费单位
                flowNumber: 0, // 流水号
                medicalRecordNumber: 0, // 病历号
                socialSecurityNumber: 0, // 社会保障号
            },
            chongqing: {
                outpatientNo: 0, // 门诊号
                direction: Horizontal, // 纵向三联
            },
            chengdu: {
                outpatientNo: 0, // 门诊号
            },
        },
        hospitalBillConfig: {
            format: '', // 省市的模板
            type: 0, // 票据类型  0 财政发票  1 税务发票
            guangdong: {
                chargeInstitution: 0, // 是否打印收费单位
                institutionName: '', // 收费单位
                flowNumber: 0, // 流水号
                medicalRecordNumber: 0, // 病历号
                socialSecurityNumber: 0, // 社会保障号
            },
            chongqing: {
                outpatientNo: 0, // 门诊号
                direction: Horizontal, // 纵向三联
            },
            chengdu: {
                outpatientNo: 0, // 门诊号
            },
        },
        // 用药标签
        medicineTag: {
            title: '',
            mobileType: 1, // 0 不展示 1 部分展示  2 完整展示
            chineseRemark: '',
        },
        // 患者标签
        patientTag: {
            title: '',
            mobileType: 1, // 0 不展示 1 部分展示  2 完整展示
        },
        // 体检-总评报告
        peReport: {
            cover: {
                mainTitle: '健康体检报告',//报告封面主标题
                subTitle: 'HEALTH EXAMINATION REPORT',//报告封面副标题
                institutionLogoUrl: '',
                institutionName: '',
                personInfo: {
                    basicInfo: 1,//受检者  可选值：0 1 默认值 1
                    peDate: 1,//体检日期 可选值：0 1 默认值 1
                    peOrganName: 1,//体检单位 可选值：0 1 默认值 1
                    peOrderNo: 1,//体检单号 可选值：0 1 默认值 1
                    reportDate: 1,//报告日期 可选值：0 1 默认值 1
                    mobile: 1,//联系手机 可选值：0 1 默认值 1
                    company: 1,//所属单位 可选值：0 1 默认值 1
                    address: 1,//联系地址 可选值：0 1 默认值 1
                    xAxis: 23,//横向位置
                    yAxis: 20,//纵向位置
                },
                contact: '', //联系方式
                address: '', //地址
                qrCode: 1,//二维码 可选值：0 1 默认值 1
            },
            introduction: {
                content: '',//报告导读
                abnormalImage: 1, //异常图示 可选值：0 1 默认值 1
            },
        },
        // 体检 - 公卫报告
        publicHealthReport: {
            cover: {
                mainTitle: '健康体检报告',//报告封面主标题
                subTitle: 'HEALTH EXAMINATION REPORT',//报告封面副标题
                institutionLogoUrl: '',
                institutionName: '',
                personInfo: {
                    basicInfo: 1,//受检者  可选值：0 1 默认值 1
                    peDate: 1,//体检日期 可选值：0 1 默认值 1
                    peOrganName: 1,//体检单位 可选值：0 1 默认值 1
                    peOrderNo: 1,//体检单号 可选值：0 1 默认值 1
                    reportDate: 1,//报告日期 可选值：0 1 默认值 1
                    mobile: 1,//联系手机 可选值：0 1 默认值 1
                    company: 1,//所属单位 可选值：0 1 默认值 1
                    address: 1,//联系地址 可选值：0 1 默认值 1
                    xAxis: 23,//横向位置
                    yAxis: 20,//纵向位置
                },
                contact: '', //联系方式
                address: '', //地址
                qrCode: 1,//二维码 可选值：0 1 默认值 1
            },
            introduction: {
                content: '',//报告导读
                abnormalImage: 1, //异常图示 可选值：0 1 默认值 1
            },
        },
        // 体检 - 团体报告
        peGroupReport: {},
        medicalListConfig: {},
        statement: {},
        medicalDocuments: {},
        hospitalMedicalDocuments: {}, // 住院病例文书配置
        hospitalFeeBills: {}, // 住院收费票据配置
        hospitalTags: {}, // 住院标签配置
        headerConfig: {
            header: 0, // 0 长名  1 短名
        },
        receipt: {}, // 收据
    },
    mutations: {
        // 打印机配置
        SET_PRINT_CONFIG: (state, data) => {
            state.config[data.key] = data.value;
            Store.set(PRINT_CONFIG, state.config);
        },

        SET_PRINTER_CONFIG: (state, data) => {
            Object.assign(state.printerConfig, data);
            Store.set(PRINTER_CONFIG, state.printerConfig);
        },

        CLEAR_PRINT_CONFIG: (state, key) => {
            state.config[key] = null;
            Store.set(PRINT_CONFIG, state.config);
        },
        // 收费单打印配置
        SET_PRINT_CASHIER_CONFIG: (state, data) => {
            Object.assign(state.cashier, data);
        },
        // 挂号单打印配置
        SET_PRINT_REGISTRATION_CONFIG: (state, data) => {
            Object.assign(state.registration, data);
        },
        // 挂号标签打印配置
        SET_PRINT_REGISTRATION_TAG_CONFIG: (state, data) => {
            Object.assign(state.registrationTag, data);
        },
        // 发药单打印配置
        SET_PRINT_DISPENSING_CONFIG: (state, data) => {
            Object.assign(state.dispensing, data);
        },
        // 处方打印配置
        SET_PRINT_PRESCRIPTION_CONFIG: (state, data) => {
            state.prescriptionConfig = data;
        },
        // 判断打印配置是否加载
        SET_PRINT_ALL_CONFIG_INIT: (state, data) => {
            state.isPrintConfigInited = data;
        },

        SET_PRINT_BILL_CONFIG: (state, data) => {
            Object.assign(state.billConfig, data);
        },
        SET_PRINT_HOSPITAL_BILL_CONFIG: (state, data) => {
            Object.assign(state.hospitalBillConfig, data);
        },
        SET_PRINT_MEDICAL_DOCUMENTS_CONFIG: (state, data) => {
            state.medicalDocuments = data;
        },
        SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_CONFIG: (state, data) => {
            state.hospitalMedicalDocuments = data;
        },
        SET_PRINT_HOSPITAL_FEE_BILLS_CONFIG: (state, data) => {
            state.hospitalFeeBills = data;
        },
        SET_PRINT_HOSPITAL_TAGS_CONFIG: (state, data) => {
            state.hospitalTags = data;
        },
        SET_PRINT_MEDICAL_DOCUMENTS_SUB_CONFIG: (state, {
            key, data,
        }) => {
            state.medicalDocuments[key] = data;
        },
        SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_SUB_CONFIG: (state, {
            key, data,
        }) => {
            state.hospitalMedicalDocuments[key] = data;
        },
        SET_PRINT_HOSPITAL_FEE_BILLS_SUB_CONFIG: (state, {
            key, data,
        }) => {
            state.hospitalFeeBills[key] = data;
        },
        SET_PRINT_HOSPITAL_TAGS_SUB_CONFIG: (state, {
            key, data,
        }) => {
            state.hospitalTags[key] = data;
        },
        SET_PRINT_MEDICAL_DOCUMENTS_TITLE: (state, {
            title, subtitle,
        }) => {
            state.medicalDocuments.prescription.header.title = title;
            state.medicalDocuments.prescription.vertical.title = title;
            state.medicalDocuments.prescription.horizontal.title = title;
            state.medicalDocuments.medical.header.title = title;
            state.medicalDocuments.infusion.header.title = title;
            state.medicalDocuments.treatment.header.title = title;
            state.medicalDocuments.inspection.header.title = title;
            state.medicalDocuments.illnessCert.header.title = title;
            state.medicalDocuments.examination.header.title = title;
            state.medicalDocuments.examineReport.header.title = title;
            state.medicalDocuments.cdusReport.header.title = title;
            state.medicalDocuments.ctReport.header.title = title;
            state.medicalDocuments.drReport.header.title = title;
            state.medicalDocuments.mrReport.header.title = title;
            state.medicalDocuments.mgReport.header.title = title;
            state.medicalDocuments.eyeInspectReport.header.title = title;
            state.medicalDocuments.endoscopeReport.header.title = title;

            state.medicalDocuments.prescription.header.subtitle = subtitle;
            state.medicalDocuments.prescription.vertical.subtitle = subtitle;
            state.medicalDocuments.prescription.horizontal.subtitle = subtitle;
            state.medicalDocuments.medical.header.subtitle = subtitle;
            state.medicalDocuments.infusion.header.subtitle = subtitle;
            state.medicalDocuments.treatment.header.subtitle = subtitle;
            state.medicalDocuments.inspection.header.subtitle = subtitle;
            state.medicalDocuments.illnessCert.header.subtitle = subtitle;
            state.medicalDocuments.examination.header.subtitle = subtitle;
            state.medicalDocuments.examineReport.header.subtitle = subtitle;
            state.medicalDocuments.cdusReport.header.subtitle = subtitle;
            state.medicalDocuments.ctReport.header.subtitle = subtitle;
            state.medicalDocuments.drReport.header.subtitle = subtitle;
            state.medicalDocuments.mrReport.header.subtitle = subtitle;
            state.medicalDocuments.mgReport.header.subtitle = subtitle;
            state.medicalDocuments.eyeInspectReport.header.subtitle = subtitle;
            state.medicalDocuments.endoscopeReport.header.subtitle = subtitle;
        },
        SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_TITLE: (state, {
            title, subtitle,
        }) => {
            state.hospitalMedicalDocuments.advice.header.title = title;
            state.hospitalMedicalDocuments.advice.shandong.title = title;
            state.hospitalMedicalDocuments.adviceExecution.header.title = title;
            state.hospitalMedicalDocuments.dispenseAndUnDispenseOrder.header.title = title;
            state.hospitalMedicalDocuments.prescription.header.title = title;
            state.hospitalMedicalDocuments.infusionRecord.header.title = title;
            state.hospitalMedicalDocuments.hospitalizationCertificate.header.title = title;

            state.hospitalMedicalDocuments.advice.header.subtitle = subtitle;
            state.hospitalMedicalDocuments.advice.shandong.subtitle = subtitle;
            state.hospitalMedicalDocuments.adviceExecution.header.subtitle = subtitle;
            state.hospitalMedicalDocuments.dispenseAndUnDispenseOrder.header.subtitle = subtitle;
            state.hospitalMedicalDocuments.prescription.header.subtitle = subtitle;
            state.hospitalMedicalDocuments.infusionRecord.header.subtitle = subtitle;
            state.hospitalMedicalDocuments.hospitalizationCertificate.header.subtitle = subtitle;
        },
        SET_PRINT_HOSPITAL_FEE_BILLS_TITLE: (state, {
            title,
        }) => {
            state.hospitalFeeBills.chargeFeeList.title = title;
            state.hospitalFeeBills.depositReceipt.title = title;
            state.hospitalFeeBills.hospitalCashier.title = title;
        },
        SET_PRINT_MEDICAL_DOCUMENTS_TITLE_WITH_PARAM: (state, {
            title, subtitle, param = 'medical',
        }) => {
            state.medicalDocuments[param].header.title = title;
            state.medicalDocuments[param].header.subtitle = subtitle;
        },
        SET_PRINT_PRESCRIPTION_TITLE_WITH_PARAM: (state, {
            title, subtitle, param = 'header',
        }) => {
            state.medicalDocuments.prescription[param].title = title;
            state.medicalDocuments.prescription[param].subtitle = subtitle;
        },
        SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_TITLE_WITH_PARAM: (state, {
            title, subtitle, param = 'advice',
        }) => {
            state.hospitalMedicalDocuments[param].header.title = title;
            state.hospitalMedicalDocuments[param].header.subtitle = subtitle;
        },
        SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_CUSTOM_TITLE_WITH_PARAM: (state, {
            headerName = 'header', title, subtitle, param = 'advice',
        }) => {
            state.hospitalMedicalDocuments[param][headerName].title = title;
            state.hospitalMedicalDocuments[param][headerName].subtitle = subtitle;
        },
        SET_PRINT_HOSPITAL_FEE_BILLS_TITLE_WITH_PARAM: (state, {
            title, param = 'chargeFeeList',
        }) => {
            state.hospitalFeeBills[param].title = title;
        },
        SET_PRINT_MEDICAL_LIST_CONFIG: (state, data) => {
            state.medicalListConfig = data;
        },

        SET_COMMON_HEADER_CONFIG: (state, data) => {
            state.headerConfig = data;
        },
        SET_PRINT_MEDICINE_TAG_CONFIG: (state, data) => {
            Object.assign(state.medicineTag, data);
        },
        SET_PRINT_INVENTORY_GOODS_IN_CONFIG: (state, data) => {
            Object.assign(state.inventoryGoodsIn, data);
        },

        SET_PRINT_PATIENT_TAG_CONFIG: (state, data) => {
            Object.assign(state.patientTag, data);
        },
        SET_PRINT_STATEMENT_LIST_CONFIG: (state, data) => {
            Object.assign(state.statement, data);
        },
        SET_PRINT_INDIVIDUAL_PE_REPORT_CONFIG: (state, data) => {
            Object.assign(state.peReport, data);
        },
        SET_PRINT_PUBLIC_HEALTH_PE_REPORT_CONFIG: (state, data) => {
            Object.assign(state.publicHealthReport, data);
        },
        SET_PRINT_GROUP_REPORT_CONFIG: (state, data) => {
            Object.assign(state.peGroupReport, data);
        },
        SET_PRINT_RECEIPT_CONFIG: (state, data) => {
            Object.assign(state.receipt, data);
        },

    },
    actions: {
        setPrintConfig({ commit }, data) {
            commit('SET_PRINT_CONFIG', data);
        },
        setPrinterConfig({ commit }, data) {
            commit('SET_PRINTER_CONFIG', data);
        },

        clearPrintConfig({ commit }, key) {
            commit('CLEAR_PRINT_CONFIG', key);
        },
        // 获取收费单打印配置
        async fetchPrintCashierConfig({ commit }) {
            try {
                const { data } = await PrintAPI.fetchPrintConfig('clinic', 'print.cashier');
                commit('SET_PRINT_CASHIER_CONFIG', data.cashier);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        setPrintCashierConfig({ commit }, data) {
            commit('SET_PRINT_CASHIER_CONFIG', data.cashier);
        },
        async fetchPrintHeader({ commit }) {
            try {
                const { data } = await PrintAPI.fetchPrintConfig('clinic', 'print.commonSettings.header');
                commit('SET_COMMON_HEADER_CONFIG', data);

                return data;
            } catch (e) {
                console.error(e);
            }
        },

        async updatePrintHeader({ commit }, payload) {
            try {
                const { data } = await PrintAPI.updatePrintConfig('clinic', 'print.commonSettings.header', payload);
                commit('SET_COMMON_HEADER_CONFIG', data);
                return data;
            } catch (error) {
                console.error(error);
            }
        },
        // 获取挂号打印配置
        async fetchPrintRegistrationConfig({ commit }) {
            try {
                const { data } = await PrintAPI.fetchPrintConfig('clinic', 'print.registration');
                commit('SET_PRINT_REGISTRATION_CONFIG', data.registration);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        //获取挂号标签打印配置
        async fetchPrintRegistrationTagConfig({ commit }) {
            try {
                const { data } = await PrintAPI.fetchPrintConfig('clinic', 'print.registrationTag');
                commit('SET_PRINT_REGISTRATION_TAG_CONFIG', data.registrationTag);
                return data;
            } catch (e) {
                console.error(e);
            }
        },

        setPrintRegistrationConfig({ commit }, data) {
            commit('SET_PRINT_REGISTRATION_CONFIG', data.registration);
        },
        // 获取发药打印配置
        async fetchPrintDispensingConfig({ commit }) {
            try {
                const { data } = await PrintAPI.fetchPrintConfig('clinic', 'print.dispensing');
                commit('SET_PRINT_DISPENSING_CONFIG', data.dispensing);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        setPrintDispensingConfig({ commit }, data) {
            commit('SET_PRINT_DISPENSING_CONFIG', data.dispensing);
        },
        // 获取处方打印配置
        async fetchPrintPrescriptionConfig({ commit }) {
            try {
                const { data } = await PrintAPI.fetchPrintConfig('clinic', 'print.prescription');
                commit('SET_PRINT_PRESCRIPTION_CONFIG', data.prescription);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        setPrintPrescriptionConfig({ commit }, data) {
            commit('SET_PRINT_PRESCRIPTION_CONFIG', data.prescription);
        },
        // 获取医疗票据配置
        async fetchPrintMedicalBillConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('print.bill', 'clinic', 'v3');
                commit('SET_PRINT_BILL_CONFIG', data);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        // 获取住院票据配置
        async fetchPrintHospitalBillConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('print.hospital', 'clinic', 'v3');
                commit('SET_PRINT_HOSPITAL_BILL_CONFIG', data);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        // 获取医疗清单配置
        async fetchPrintMedicalListConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('print.medicalList', 'clinic', 'v3');
                commit('SET_PRINT_MEDICAL_LIST_CONFIG', data);
            } catch (e) {
                console.error(e);
            }
        },
        // 获取医保结算单配置
        async fetchPrintStatementListConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('print.statement', 'clinic', 'v3');
                commit('SET_PRINT_STATEMENT_LIST_CONFIG', data);
            } catch (e) {
                console.error(e);
            }
        },
        setPrintBillConfig({ commit }, data) {
            commit('SET_PRINT_BILL_CONFIG', data.bill);
        },
        setHospitalPrintBillConfig({ commit }, data) {
            commit('SET_PRINT_HOSPITAL_BILL_CONFIG', data.bill);
        },
        // 获取医疗文书配置
        async fetchPrintMedicalDocumentsConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('print.medicalDocuments', 'clinic', 'v3');
                commit('SET_PRINT_MEDICAL_DOCUMENTS_CONFIG', data);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        // 获取住院病例文书配置
        async fetchPrintHospitalMedicalDocumentsConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('hisPrint.medicalDocuments', 'clinic', 'v3');
                commit('SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_CONFIG', data);
            } catch (e) {
                console.error(e);
            }
        },
        // 获取住院收费票据配置
        async fetchPrintHospitalFeeBillsConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('hisPrint.feeBill', 'clinic', 'v3');
                commit('SET_PRINT_HOSPITAL_FEE_BILLS_CONFIG', data);
            } catch (e) {
                console.error(e);
            }
        },
        async fetchPrintHospitalTagsConfig({
            state, commit,
        }) {
            try {
                const { data } = await PropertyAPI.get('hisPrint.tags', 'clinic', 'v3');
                commit('SET_PRINT_HOSPITAL_TAGS_CONFIG', data);
                return data || state.hospitalTags;
            } catch (e) {
                console.error(e);
                return state.hospitalTags;
            }
        },
        async fetchPrintHospitalMedicineTagConfig({
            state, commit,
        }) {
            try {
                const { data } = await PropertyAPI.get('hisPrint.tags.medicine', 'clinic', 'v3');
                commit('SET_PRINT_HOSPITAL_TAGS_SUB_CONFIG', {
                    key: 'medicine', data,
                });
                return data || state.hospitalTags.medicine || {};
            } catch (e) {
                console.error(e);
                return state.hospitalTags.medicine || {};
            }
        },
        // 获取用药标签配置
        async fetchPrintMedicineTagConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('print.medicineTag', 'clinic', 'v3');
                commit('SET_PRINT_MEDICINE_TAG_CONFIG', data);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        // 获取患者标签配置
        async fetchPrintPatientTagConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('print.patientTag', 'clinic', 'v3');
                commit('SET_PRINT_PATIENT_TAG_CONFIG', data);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        // 获取库存打印
        async fetchPrintInventoryConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('inventoryPrint.inventoryGoodsIn', 'clinic', 'v3');
                commit('SET_PRINT_INVENTORY_GOODS_IN_CONFIG', data);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        // 获取总评报告模板打印配置 - 个人
        async fetchPrintPEIndividualReportConfig({ commit }) {
            try {
                const { data } = await PrintAPI.fetchPrintConfig('clinic', 'pe.report');
                const convertedData = data.report || {};
                convertedData.cover = convertedData.cover || {};
                convertedData.introduction = convertedData.introduction || {};
                convertedData.cover.personInfo = convertedData.cover.personInfo || {};
                commit('SET_PRINT_INDIVIDUAL_PE_REPORT_CONFIG', convertedData);
                return convertedData;
            } catch (e) {
                console.error(e);
            }
        },
        // 获取总评报告模板打印配置 - 公卫
        async fetchPrintPEPublicHealthyReportConfig({ commit }) {
            try {
                const { data } = await PrintAPI.fetchPrintConfig('clinic', 'pe.publicHealthReport');
                const convertedData = data.publicHealthReport || {};
                convertedData.cover = convertedData.cover || {};
                convertedData.introduction = convertedData.introduction || {};
                convertedData.cover.personInfo = convertedData.cover.personInfo || {};
                commit('SET_PRINT_PUBLIC_HEALTH_PE_REPORT_CONFIG', convertedData);
                return convertedData;
            } catch (e) {
                console.error(e);
            }
        },
        // 获取总评报告模板打印配置 - 团体
        async fetchPrintGroupReportConfig({ commit }) {
            try {
                const { data } = await PrintAPI.fetchPrintConfig('clinic', 'pe.groupReport');
                commit('SET_PRINT_GROUP_REPORT_CONFIG', data.groupReport);
                return data.groupReport;
            } catch (e) {
                console.error(e);
            }
        },
        // 拉取所有的打印配置
        async fetchPrintAllConfigIfNeed({
            state, commit,
        }) {
            try {
                if (!state.isPrintConfigInited) {
                    // 该方法在多个组件 created 周期中调用，不是同步过程会发重复请求，所以需要提前设置
                    commit('SET_PRINT_ALL_CONFIG_INIT', true);

                    const { data } = await PrintAPI.fetchPrintConfig('clinic', 'print');
                    const { data: peData } = await PrintAPI.fetchPrintConfig('clinic', 'pe');
                    const { data: hospitalData } = await PrintAPI.fetchPrintConfig('clinic', 'hisPrint');
                    const printData = data.print;
                    const hospitalPrintData = hospitalData.hisPrint || {};
                    commit('SET_PRINT_CASHIER_CONFIG', printData.cashier);
                    commit('SET_PRINT_REGISTRATION_CONFIG', printData.registration);
                    commit('SET_PRINT_DISPENSING_CONFIG', printData.dispensing);
                    commit('SET_PRINT_PRESCRIPTION_CONFIG', printData.prescription);
                    commit('SET_PRINT_BILL_CONFIG', printData.bill);
                    commit('SET_PRINT_RECEIPT_CONFIG', printData.receipt.outpatient);
                    commit('SET_PRINT_HOSPITAL_BILL_CONFIG', printData.hospital);
                    commit('SET_COMMON_HEADER_CONFIG', printData.commonSettings);
                    commit('SET_PRINT_MEDICAL_LIST_CONFIG', printData.medicalList);
                    commit('SET_PRINT_STATEMENT_LIST_CONFIG', printData.statement);
                    commit('SET_PRINT_MEDICAL_DOCUMENTS_CONFIG', printData.medicalDocuments);
                    commit('SET_PRINT_PATIENT_TAG_CONFIG', printData.patientTag);
                    commit('SET_PRINT_MEDICINE_TAG_CONFIG', printData.medicineTag);
                    commit('SET_PRINT_INVENTORY_GOODS_IN_CONFIG', data?.inventoryPrint?.inventoryGoodsIn);
                    // 体检打印配置
                    commit('SET_PRINT_INDIVIDUAL_PE_REPORT_CONFIG', peData?.pe?.report || {});
                    commit('SET_PRINT_PUBLIC_HEALTH_PE_REPORT_CONFIG', peData?.pe?.publicHealthReport || {});
                    commit('SET_PRINT_GROUP_REPORT_CONFIG', peData?.pe?.groupReport || {});
                    // 医院打印配置
                    commit('SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_CONFIG', hospitalPrintData?.medicalDocuments || {});
                    commit('SET_PRINT_HOSPITAL_FEE_BILLS_CONFIG', hospitalPrintData?.feeBill || {});
                    commit('SET_PRINT_HOSPITAL_TAGS_CONFIG', hospitalPrintData?.tags || {});
                    console.log('获取打印配置', new Date().toLocaleString());
                }
            } catch (e) {
                // 上面如果有设置失败，允许重拉
                commit('SET_PRINT_ALL_CONFIG_INIT', false);
                console.error(e);
            }
        },
    },
};

export default clinic;
