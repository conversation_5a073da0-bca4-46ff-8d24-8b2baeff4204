import ExecuteTimeAPI from 'api/hospital/setting/execute-time.js';
export default {
    namespaced: true,
    state: {
        // 执行时间列表
        allExecuteTimeList: [],
        isInit: false,
    },
    getters: {
        allExecuteTimeList(state) {
            return state.allExecuteTimeList || [];
        },
    },
    mutations: {
        /**
         * @desc 更新某个执行时间
         * <AUTHOR>
         * @date 2023-02-03 10:59:08
         */
        UPDATE_ITEM(state, item) {
            state.allExecuteTimeList = state.allExecuteTimeList.map((it) => {
                if (it.id === item.id) {
                    it = item;
                }
                return it;
            });
        },
        /**
         * @desc 设置 executeTimeList值
         * <AUTHOR>
         * @date 2023-02-03 11:00:23
         * @params list: executeTimeList
         */
        SET_LIST(state, list) {
            state.allExecuteTimeList = list;
        },
        SET_INIT(state) {
            state.isInit = true;
        },
    },
    actions: {
        async initExecuteTimeList({ dispatch, commit, state }) {
            try {
                if (state.isInit) return;
                const list = await dispatch('fetchExecuteTimeList');
                commit('SET_INIT');
                commit('SET_LIST', list);
            } catch (e) {
                console.warn('初始化执行时间设置失败', e);
            }
        },
        /**
         * @desc 获取执行时间列表
         * <AUTHOR>
         * @date 2023-02-03 10:49:58
         */
        async fetchExecuteTimeList(adviceRuleType = '') {
            const { data } = await ExecuteTimeAPI.fetchExecuteTimeList(adviceRuleType);
            return data?.rows || [];
        },


    },
};
