import ExaminationAPI from 'src/api/examination.js';

export const FILE_SYNC_DEVICE_KEY = 'abc_file_sync_local_device_id';
/**
 * @desc 检验报告文件同步功能store
 * <AUTHOR>
 * @date 2022-07-11 16:32:26
 */
const FileSync = {
    namespaced: true,
    state: {
        localDeviceId: window.localStorage.getItem(FILE_SYNC_DEVICE_KEY), // 本机uuid作为标识
        enableFileSync: 0, // 开始同步功能
        fileSyncPath: '', // 文件同步路径
        activeDeviceId: '', // 文件同步活跃设备id
    },

    getters: {
        enableFileSync(state) {
            return state.enableFileSync;
        },
        fileSyncPath(state) {
            return state.fileSyncPath;
        },
        activeDeviceSwitch(state) {
            return state.activeDeviceId && state.activeDeviceId === state.localDeviceId;
        },

        fileSyncIsActive(state) {
            return state.enableFileSync && state.fileSyncPath && state.activeDeviceId === state.localDeviceId;
        },

        hasActiveDevice(state) {
            return !!state.activeDeviceId;
        },
    },

    mutations: {
        SET_LOCAL_DEVICE_UUID: (state, activeDeviceId) => {
            state.localDeviceId = activeDeviceId;
            window.localStorage.setItem(FILE_SYNC_DEVICE_KEY, activeDeviceId || '');
        },
        SET_ENABLE_FILE_SYNC: (state, data) => {
            state.enableFileSync = data;
        },
        SET_FILE_SYNC_PATH: (state, data) => {
            state.fileSyncPath = data;
        },
        SET_ACTIVE_DEVICE_ID: (state, data) => {
            state.activeDeviceId = data;
        },
    },

    actions: {
        async initFileSync({ commit }) {
            const { data } = await ExaminationAPI.fetchExaminationFileSyncConfig();
            commit('SET_ENABLE_FILE_SYNC', data.enableFileSync);
            commit('SET_FILE_SYNC_PATH', data.fileSyncPath);
            commit('SET_ACTIVE_DEVICE_ID', data.activeDeviceId);
        },

        async createUpdateFileSyncConfig({ state, commit }, postData) {
            const { data } = await ExaminationAPI.createUpdateExaminationFileSyncConfig({
                activeDeviceId: state.localDeviceId,
                enableFileSync: +postData.enableFileSync,
                fileSyncPath: postData.fileSyncPath,
                activeDeviceSwitch: +postData.activeDeviceSwitch,
            });
            commit('SET_ENABLE_FILE_SYNC', data.enableFileSync);
            commit('SET_FILE_SYNC_PATH', data.fileSyncPath);
            commit('SET_ACTIVE_DEVICE_ID', data.activeDeviceId);
            commit('SET_LOCAL_DEVICE_UUID', postData.activeDeviceSwitch ? data.activeDeviceId : '');
        },
    },
};
export default FileSync;
