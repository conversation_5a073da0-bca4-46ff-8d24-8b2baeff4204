<template>
    <div
        class="table-form-item"
        :style="isFull ? {
            width: '100%',
            height: '100%'
        } : {}"
    >
        <slot>
            <component
                :is="tag"
                v-model="value"
                :params="params"
                :is-mobile="isMobile"
                :writable="writable"
                :current-popover-id="currentPopoverId"
                @change="formChangeFn"
                @enter="enterEventFn"
                @changePopoverId="popoverIdChangeFn"
            >
                {{ value }}
            </component>
        </slot>
        <slot name="delete"></slot>
    </div>
</template>

<script>
    import TableFormInput from './table-form-input.vue';
    import TableFormSelect from './table-form-select.vue';
    import TableFormRadio from './table-form-radio.vue';
    import TableFormEditDiv from './table-form-edit-div.vue';
    import TableFormTextArea from './table-form-textarea.vue';
    import TableFormUploader from './table-form-uploader.vue';
    import TableFormAutoComplete from './table-form-autocomplete.vue';
    import TableFormGlassesDegree from './table-form-glasses-degree.vue';
    import TableFormVisionSelect from './table-form-vision-select.vue';

    const ExaminationTableFormComponentsTypeEnum = Object.freeze({
        RADIO_TYPE: 0,
        CHECKBOX_TYPE: 1,
        AUTO_COMPLETE_TYPE: 2,
        INPUT_TYPE: 3,
        UPLOAD_TYPE: 4,
        EDIT_DIV_TYPE: 5,
        SELECT_TYPE: 6,
        CUSTOM_GLASSES_DEGREE_TYPE: 7,// 业务自定义相关组件，选择眼镜度数
        CUSTOM_VISION_SELECT_TYPE: 8,// 业务自定义相关组件，选择视力
        TEXT_AREA_TYPE: 9,
    });

    const ExaminationTableFormComponents = Object.freeze({
        [ExaminationTableFormComponentsTypeEnum.INPUT_TYPE]: 'TableFormInput',
        [ExaminationTableFormComponentsTypeEnum.AUTO_COMPLETE_TYPE]: 'TableFormAutoComplete',
        [ExaminationTableFormComponentsTypeEnum.SELECT_TYPE]: 'TableFormSelect',
        [ExaminationTableFormComponentsTypeEnum.RADIO_TYPE]: 'TableFormRadio',
        [ExaminationTableFormComponentsTypeEnum.CHECKBOX_TYPE]: 'TableFormRadio',
        [ExaminationTableFormComponentsTypeEnum.TEXT_AREA_TYPE]: 'TableFormTextArea',
        [ExaminationTableFormComponentsTypeEnum.EDIT_DIV_TYPE]: 'TableFormEditDiv',
        [ExaminationTableFormComponentsTypeEnum.UPLOAD_TYPE]: 'TableFormUploader',
        [ExaminationTableFormComponentsTypeEnum.CUSTOM_GLASSES_DEGREE_TYPE]: 'TableFormGlassesDegree',
        [ExaminationTableFormComponentsTypeEnum.CUSTOM_VISION_SELECT_TYPE]: 'TableFormVisionSelect',
    });
    export default {
        name: 'TableFormItem',
        components: {
            TableFormInput,
            TableFormSelect,
            TableFormRadio,
            TableFormTextArea,
            TableFormUploader,
            TableFormAutoComplete,
            TableFormGlassesDegree,
            TableFormVisionSelect,
            TableFormEditDiv,
        },
        props: {
            params: {
                type: Object,
                default: () => ({}),
            },
            // ?是否是移动端展示
            isMobile: Boolean,
            // ?可修改
            writable: {
                type: Boolean,
                default: true,
            },
            // popoverId
            currentPopoverId: {
                type: String,
                default: '',
            },
            // 表单 change事件
            formChangeFn: {
                type: Function,
                default: () => {},
            },
            // 触发 enter事件
            enterEventFn: {
                type: Function,
                default: () => {},
            },
            // 切换 popover
            popoverIdChangeFn: {
                type: Function,
                default: () => {},
            },
        },
        data() {
            return {
                tag: 'span',
                value: null,
            };
        },
        computed: {
            // value: {
            //     get() {
            //         console.log('get value', this.params.value);
            //         return this.params?.value;
            //     },
            //     set(val) {
            //         console.log('set value', val);
            //         this.$set(this.params,'value',val);
            //     },
            // },
            isFull() {
                return [
                    ExaminationTableFormComponentsTypeEnum.EDIT_DIV_TYPE,
                    ExaminationTableFormComponentsTypeEnum.TEXT_AREA_TYPE,
                ].includes(this.params.componentType);
            },
        },
        watch: {
            'params.value': {
                handler(val) {
                    if (val === this.value) return;
                    this.value = val;
                },
                immediate: true,
            },
            value(val) {
                this.$set(this.params,'value',val);
                this.formChangeFn(this.params,val);
            },
        },
        created() {
            if (!this.isMobile) {
                this.tag = ExaminationTableFormComponents[this.params.componentType] || 'span';
            }
        },
    };
</script>
