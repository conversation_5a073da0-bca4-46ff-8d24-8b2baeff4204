// /*eslint-disable*/
/**
 * @desc 给 table-form使用的公共逻辑
 * <AUTHOR>
 * @date 2022-07-18 11:02
 */
export default {
    computed: {
        // 每个指标项的 formItem配置，组件的一些配置，组件事件
        constraints() {
            return this.params?.constraints?.map((constraint, index) => {
                return {
                    ...constraint,
                    formItemConfig: this.parseFormItemConfig(constraint, index),
                    componentConfig: this.parseComponentConfig(constraint, index),
                    on: this.packingEvent(constraint.on || {}),
                };
            }) ?? [];
        },
        listeners() {
            return this.constraints?.map((constraint) => constraint.on);
        },
    },
    methods: {
        /**
         * @desc 触发表单校验时的钩子，会在外部覆写。
         * <AUTHOR>
         * @date 2022/7/21 18:40
         * @param {boolean} valid 校验是否通过
         * @param {number} index 当前指标中的第几个 form-item在触发校验
         * @param {object} constraint 校验相关规则对象
         */
        onFieldValidated({
        // eslint-disable-next-line no-unused-vars
            valid, index, constraint,
        } = {}) {
            // console.log('onFieldValidated', valid, index, constraint);
        },
        // enter切到下一个 input
        handleEnter(e) {
            this.$emit('enter',e);
        },
        // 触发组件聚焦
        handleFocus(refName, eventName = 'focus') {
            if (!this.writable) return;
            this.$nextTick(() => {
                this.$refs[refName]?.[eventName]?.();
            });
        },
        // 处理事件,on:{ change(){} }
        packingEvent(on) {
            return Object.keys(on).reduce((res, item) => {
                // 包装层
                res[item] = (...rest) => {
                    on[item].call(this, this.params, ...rest);
                    this.$emit(item, this.params, ...rest);
                };
                return res;
            }, {});
        },
        // 处理校验函数
        parseFormItemConfig(constraint, index) {
            if (!constraint.formItemConfig) {
                constraint.formItemConfig = {};
            }
            if (constraint.validateType) {
                // 使用下面定义的对应校验函数
                constraint.formItemConfig.validateEvent = (value, callback) => this[`validateType${constraint.validateType}`](value, callback, constraint, index);
            }
            if (!constraint.formItemConfig.required) {
                constraint.formItemConfig.required = constraint.required;
            }

            return constraint.formItemConfig || {};
        },
        parseComponentConfig(constraint) {
            if (!constraint.componentConfig) {
                constraint.componentConfig = {};
            }
            if (!constraint.componentConfig.config) {
                constraint.componentConfig.config = {};
            }
            // 兼容max最大值，2,4,6,10对应类型表示小数点前 2 位，3 位，5 位数
            if (!constraint.componentConfig.config.max && (constraint.max || [2,4,6,10].includes(constraint.validateType))) {
                const maxs = {
                    2: 100,
                    4: 100,
                    6: 1000,
                    10: 100000,
                };
                constraint.componentConfig.config.max = +constraint.max || maxs[constraint.validateType];
            }
            // 兼容保留小数
            if (!constraint.componentConfig.config.formatLength && constraint.scale) {
                constraint.componentConfig.config.formatLength = +constraint.scale;
            }
            // 最小值限制
            if (constraint.min !== undefined && constraint.min !== null) {
                constraint.componentConfig.config.min = constraint.min;
            }
            // 最小值为负数或者没有最小值支持负数输入
            if (constraint.min < 0 || constraint.min === null) {
                constraint.componentConfig.config.supportNegative = true;
            }
            // 兼容支持输入 0
            if (!constraint.componentConfig.config.supportZero) {
                constraint.componentConfig.config.supportZero = true;
            }
            // 兼容输入长度
            if (!constraint.componentConfig.maxLength && constraint.size) {
                constraint.componentConfig.maxLength = constraint.size;
            }

            return constraint.componentConfig || {};
        },

        /*?============================================以下都是校验函数开始============================================?*/
        // ?输入，小数，小数点前1位后2位，支持快捷选项，仅可选一个选项，多次选择覆盖原选项，
        // ?选项值：＜0.1、0.1、0.12、0.15、0.2、0.25、0.3、0.4、0.5、0.6、0.8、1.0、1.2、1.5、2.0
        validateType1(value, callback, constraint, index) {
            if (value !== '<0.1' && value) {
                const reg = /^([012])(\.\d{1,2})?$/;
                if (value > 2 || !reg.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }

            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });

        },
        // ?正负数，2位小数，小数点前2位后2位，单位D
        validateType2(value, callback, constraint, index) {
            if (value) {
                const reg = /^([+-]?)(\d{1,2})(\.\d{1,2})?$/;
                if (!reg.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        // ?0-180的正整数，单位度（°）
        validateType3(value, callback, constraint, index) {
            if (value) {
                if (value < 0 || value > 180) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }

            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });

        },
        // ?记录水平子午线的屈光度（曲率半径）和轴向两个字段：
        // ?屈光度：2位小数，小数点前2位后2位，正数
        // ?轴位：0-180°，正整数
        // ?例如：42.00D@180°/42.50D@90°
        validateType4(value, callback, constraint, index) {
            if (value) {
                const reg = /^(\d{1,2})(\.\d{1,2})?$/;
                if (!reg.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        // ?2位正整数，单位mm
        validateType5(value, callback, constraint, index) {
            if (value) {

                const reg = /^\d{1,2}$/;
                if (!reg.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        // ?数字，2位小数，小数点前3位后2位，单位mm
        validateType6(value, callback, constraint, index) {
            if (value) {

                const reg = /^(\d{1,3})(\.\d{1,2})?$/;
                if (!reg.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        // 输入，2位正整数 0-10
        validateType7(value, callback, constraint, index) {
            if (value) {

                if (value < 0 || value > 10) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        // ?2位小数，1以内
        validateType8(value, callback, constraint, index) {
            if (value) {
                const reg = /^0\.\d{1,2}$/;
                if ((value < 0 || value > 1) || !reg.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        // ?正数，整数，4位数
        validateType9(value, callback, constraint, index) {
            if (value) {

                const reg = /^(\d{1,4})$/;
                if (!reg.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        // ?输入，数字，小数前5位后2位，单位u㎡
        validateType10(value, callback, constraint, index) {
            if (value) {
                const reg = /^(\d{1,5})(\.\d{1,2})?$/;
                if (!reg.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        // 输入，100以内，2位小数，百分数
        validateType11(value, callback, constraint, index) {
            if (value) {
                const reg = /^(\d{1,2})(\.\d{1,2})?$/;
                if (value < 0 || value > 100 || !reg.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        // 输入，1位正整数 1-9
        validateType12(value, callback, constraint, index) {
            if (value) {
                if (!/^[1-9]$/.test(value)) {
                    callback({
                        validate: false,
                        message: constraint?.message || '格式不正确',
                    });
                    this.onFieldValidated({
                        valid: false,
                        index,
                        constraint,
                    });
                    return;
                }
            }
            callback({ validate: true });
            this.onFieldValidated({
                valid: true,
                index,
                constraint,
            });
        },
        /*?============================================校验函数结束============================================?*/
    },
};
