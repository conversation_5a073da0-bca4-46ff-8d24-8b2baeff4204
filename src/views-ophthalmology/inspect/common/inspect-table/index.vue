<!--
 * @Descripttion:
 * @version:
 * @Author: zengli
 * @Date: 2020-03-05 13:36:49
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2020-03-05 22:05:07
 -->
<template>
    <abc-table
        ref="tableRef"
        class="ophthalmology-exam-table"
        type="excel"
        :render-config="renderConfig"
        :data-list="forms?.[0]?.chargeFormItems"
        :custom-tr-class="customTrClass"
        support-delete-tr
        :show-checked="false"
        @delete-tr="handleDeleteTr"
    >
        <template #topHeader>
            <goods-autocomplete
                ref="goodsAutocomplete"
                focus-show
                placeholder="输入检查项目拼音码"
                :clinic-id="currentClinic?.clinicId"
                :suggestion-items="_suggestionItems"
                :json-type="_jsonType"
                size="large"
                adaptive-width
                @selectGoods="handleSelectGoods"
            ></goods-autocomplete>
        </template>

        <template #name="{ trData: item }">
            <abc-table-cell>
                <span class="ellipsis">
                    <span v-if="item.usageInfo && item.usageInfo.payType === 10">
                        自 ·
                    </span>
                    <span v-else-if="item.productInfo && item.productInfo.medicalFeeGrade" class="gray">
                        {{ item.productInfo.medicalFeeGrade | medicalFeeGrade2Str }} ·
                    </span>

                    {{ item.name }}
                </span>
            </abc-table-cell>
        </template>

        <template #unitPrice="{ trData: item }">
            <abc-tooltip :disabled="!isEditedUnitPrice(item)">
                <abc-form-item required>
                    <abc-input
                        v-model="item.unitPrice"
                        v-abc-focus-selected
                        size="small"
                        :disabled="disabled"
                        :input-custom-style="{ textAlign: 'right' }"
                        :class="{ 'warning-input': isEditedUnitPrice(item) }"
                        type="money"
                        :tabindex="-1"
                        :config="{
                            formatLength: 4, supportZero: true, max: 9999999
                        }"
                        @change="changeUnitPrice(item)"
                    >
                    </abc-input>
                    <abc-loading v-if="inputCalcLoading && showLoading === 'unitPrice'" small no-cover></abc-loading>
                </abc-form-item>
                <template v-if="isEditedUnitPrice(item)" #content>
                    议价前：{{ $t('currencySymbol') }}{{ item.sourceUnitPrice | formatMoney(false) }}
                </template>
            </abc-tooltip>
        </template>
        <template #count="{ trData: item }">
            <abc-form-item required>
                <abc-input
                    ref="count"
                    v-model.number="item.unitCount"
                    v-abc-focus-selected
                    size="small"
                    type="number"
                    class="focus-input"
                    :input-custom-style="{ textAlign: 'right' }"
                    :config="{
                        formatLength: 0, max: 9999999, supportZero: true
                    }"
                    @enter="enterEventHandler"
                    @input="handleCountChange(item)"
                >
                </abc-input>
            </abc-form-item>
        </template>
        <template #unit="{ trData: item }">
            <abc-form-item>
                <abc-select
                    v-model="item.unit"
                    :title="item.unit"
                    size="small"
                    :tabindex="-1"
                    adaptive-width
                    @change="(unit) => selectUnit(unit, item)"
                >
                    <abc-option
                        v-for="it in unitArray(item)"
                        :key="it.id"
                        :label="it.name"
                        :value="it.name"
                    >
                    </abc-option>
                </abc-select>
            </abc-form-item>
        </template>
        <template #totalPrice="{ trData: item }">
            <abc-tooltip :disabled="!isEditedTotalPrice(item)">
                <abc-form-item required>
                    <abc-input
                        v-model="item.totalPrice"
                        v-abc-focus-selected
                        size="small"
                        type="money"
                        :class="{ 'warning-input': isEditedTotalPrice(item) }"
                        :tabindex="-1"
                        :disabled="disabled"
                        :input-custom-style="{ textAlign: 'right' }"
                        :config="{
                            formatLength: 2,supportZero: true, max: 9999999
                        }"
                        @change="changeTotalPrice(item)"
                    >
                    </abc-input>
                    <abc-loading v-if="inputCalcLoading && showLoading === 'totalPrice'" small no-cover></abc-loading>
                </abc-form-item>
                <template v-if="isEditedTotalPrice(item)" #content>
                    议价前：{{ $t('currencySymbol') }}{{ item.sourceTotalPrice | formatMoney(false) }}
                </template>
            </abc-tooltip>
        </template>

        <template #footer>
            <abc-space style=" padding: 0 12px; margin-left: auto;">
                <abc-text theme="gray">
                    共
                    <abc-text theme="black">
                        {{ RETCount }}
                    </abc-text>
                    项
                </abc-text>
                <abc-text theme="black">
                    {{ $t('currencySymbol') }} {{ RETFee | formatMoney }}
                </abc-text>
            </abc-space>
        </template>
    </abc-table>
</template>

<script>
    import { SourceFormTypeEnum } from '@/service/charge/constants.js';

    import { mapGetters } from 'vuex';
    import GoodsAutocomplete from 'views/layout/goods-autocomplete/goods-autocomplete.vue';
    import {
        formatMoney, medicalFeeGrade2Str,
    } from '@/filters';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';

    export default {
        name: 'TreatmentForm',
        components: {
            GoodsAutocomplete,
        },
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
            forms: {
                type: Array,
                default: () => [],
            },
            inputCalcLoading: Boolean,
        },
        data() {
            return {
                SourceFormTypeEnum,
                buttonLoading: false,

                showLoading: '',
                hoverTotalPrice: false,

                renderConfig: {
                    list: [
                        {
                            key: 'name',
                            label: '开单项目',
                            style: {
                                flex: '1', textAlign: 'left',
                            },
                        },
                        {
                            key: 'unitPrice',
                            label: '单价',
                            style: {
                                flex: '0', width: '80px', textAlign: 'right',
                            },
                        },
                        {
                            key: 'count',
                            label: '数量',
                            style: {
                                flex: '0', width: '80px', textAlign: 'right',
                            },
                        },
                        {
                            key: 'unit',
                            label: '单位',
                            style: {
                                flex: '0', width: '80px', textAlign: 'center',
                            },
                        },
                        {
                            key: 'totalPrice',
                            label: '金额',
                            style: {
                                flex: '0', width: '80px', textAlign: 'right',
                            },
                        },
                    ],
                },
            };
        },
        created() {
            // 传给 good-auto-complete格式化展示使用
            this._suggestionItems = [
                {
                    prop: 'name',
                    style: 'flex: 1',
                    className: 'ellipsis',
                    formatFunction: this.displayName,
                },
                {
                    prop: 'medicalFeeGrade',
                    className: 'gray',
                    formatFunction: (suggestion) => {
                        if (suggestion.shebaoPayMode === 0) {
                            return medicalFeeGrade2Str(suggestion.medicalFeeGrade);
                        }
                        if (suggestion.shebaoPayMode === 1) {
                            return '自';
                        }
                    },
                },
                {
                    label: '价格',
                    className: 'gray',
                    style: 'width: 100px;padding-left: 10px;',
                    formatFunction: this.displayPrice,
                },
            ];
            this._jsonType = [
                {
                    type: GoodsTypeEnum.EXAMINATION, // 检查检验,
                    subType: [GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test],
                },
            ];
        },
        computed: {
            ...mapGetters(['currentClinic']),
            RETCount() {
                let count = 0;
                this.forms && this.forms.forEach((form) => {
                    count += form.chargeFormItems.filter((item) => {
                        return !item.chargeStatus;
                    }).length;
                });

                return count || '';
            },
            RETFee() {
                let fee = 0;
                this.forms && this.forms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        fee += item.chargeStatus ? 0 : +item.totalPrice;
                    });
                });

                return fee || 0;
            },
        },
        methods: {
            // 格式化项目名称
            displayName(item) {
                return item.medicineCadn || item.name;
            },
            // 格式化价格
            displayPrice(item) {
                if (item.packagePrice !== null) {
                    return `${this.$t('currencySymbol')} ${formatMoney(item.packagePrice || 0)}/${item.packageUnit || '次'}`;
                }
                return '';
            },
            customTrKey(tr) {
                return tr.keyId || tr.id;
            },
            customTrClass(tr) {
                return `abc-table-tr--${this.customTrKey(tr)}`;
            },
            handleSelectGoods(goods) {
                this.$emit('selectGoods', goods);
            },
            changeHandler() {
                this.$emit('change');
            },
            enterEventHandler() {
                this.$refs.goodsAutocomplete.focusInput();
            },
            handleDeleteTr(trIndex) {
                this.forms[0].chargeFormItems.splice(trIndex, 1);
                this.$emit('delete', this.forms[0]);
            },
            deleteItemHandler (form, index) {
                form.chargeFormItems.splice(index, 1);
                this.$emit('delete',form);
            },
            handleCountChange(item) {
                if (item.expectedTotalPrice) {
                    item.expectedTotalPrice = null;
                    if (item.sourceUnitPrice !== item.unitPrice) {
                        item.expectedUnitPrice = item.unitPrice;
                    }
                }
                this.changeHandler();
            },
            isEditedUnitPrice(item) {
                return item.expectedUnitPrice && +item.unitPrice !== +item.sourceUnitPrice;
            },
            changeUnitPrice(item) {
                this.showLoading = 'totalPrice';
                this.$set(item, 'expectedUnitPrice', item.unitPrice);
                this.$set(item, 'expectedTotalPrice', null);
                this.changeHandler();
                this.timeOutId = setTimeout(() => {
                    this.showLoading = '';
                },1000);
            },
            selectUnit(unit, item) {
                item.unit = unit;
                const curUnitCount = item.productInfo && item.productInfo.type === GoodsTypeEnum.GOODS ?
                    item.unitCount : ~~item.unitCount;
                if (item.unit !== item.pieceUnit && curUnitCount !== item.unitCount) {
                    item.unitCount = '';
                }
                item.useDismounting = +(item.productInfo &&
                    item.productInfo.dismounting &&
                    item.unit === item.productInfo.pieceUnit &&
                    item.unit !== item.productInfo.packageUnit);
                const {
                    piecePrice,
                    packagePrice,
                } = item.productInfo;
                item.unitPrice = item.useDismounting ? piecePrice : packagePrice;

                if (item.expectedTotalPrice) {
                    item.expectedTotalPrice = null;
                    if (item.sourceUnitPrice !== item.unitPrice) {
                        item.expectedUnitPrice = item.unitPrice;
                    }
                }

                this.changeHandler();
            },
            unitArray(wm) {
                const res = [];
                const dismounting = wm.productInfo && wm.productInfo.dismounting;
                const pieceUnit = wm.productInfo && wm.productInfo.pieceUnit;
                const packageUnit = wm.productInfo && wm.productInfo.packageUnit;
                if (dismounting) {
                    if (pieceUnit) {
                        res.push({ 'name': pieceUnit });
                    }
                    if (packageUnit && packageUnit !== pieceUnit) {
                        res.push({ 'name': packageUnit });
                    }
                } else {
                    res.push({ 'name': packageUnit });
                }
                return res;
            },
            isEditedTotalPrice(item) {
                return item.expectedTotalPrice && +item.totalPrice !== +item.sourceTotalPrice;
            },
            changeTotalPrice(item) {
                this.showLoading = 'unitPrice';
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPrice', item.totalPrice);
                this.$emit('change');
                this.timeOutId = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },

            focusInput(index) {
                const keyId = this.customTrKey(this.forms?.[0]?.chargeFormItems?.[index]);

                const $tableTr = this.$refs.tableRef.$el.querySelector(`.abc-table-tr--${keyId}`);
                $tableTr && $tableTr.querySelector('.focus-input input').focus();
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.ophthalmology-exam-table {
    .warning-input {
        .abc-input__inner {
            color: var(--abc-color-Y2);
        }
    }
}
</style>
