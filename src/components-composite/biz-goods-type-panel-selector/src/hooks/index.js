import { ref } from 'vue';

export default function useGoodsTypePanel() {

    const goodsTypeList = ref([]);
    const goodsTypeFetchLoading = ref(false);
    const isInited = ref(false);

    const generateGoodsIdKey = (list = [], fn) => {
        return list.map((item) => {
            if (!item.goodsIdKey) {
                if (typeof fn === 'function') {
                    item.goodsIdKey = fn(item);
                } else {
                    item.goodsIdKey = item.id;
                }
            }

            if (Array.isArray(item?.children) && item?.children?.length) {
                item.children = generateGoodsIdKey(item.children, fn);
            }

            return {
                ...item,
                goodsIdKey: item.goodsIdKey,
            };
        });
    };

    const fetchGoodsTypeList = async (fetchFn, generateGoodsIdKeyFn) => {
        if (isInited.value) return;
        goodsTypeFetchLoading.value = true;
        const data = await fetchFn();
        goodsTypeList.value = generateGoodsIdKey(data || [], generateGoodsIdKeyFn);
        goodsTypeFetchLoading.value = false;
        isInited.value = true;
    };

    return {
        goodsTypeFetchLoading,
        goodsTypeList,
        fetchGoodsTypeList,
    };
}
