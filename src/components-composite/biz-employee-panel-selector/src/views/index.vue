<template>
    <div class="biz-employee-panel-selector-wrapper">
        <biz-panel-selector
            v-model="selectedEmployees"
            icon="s-user-color"
            :add-text="addText"
            @add="selectEmployeeDialog = true"
        >
        </biz-panel-selector>
        <abc-dialog
            v-if="selectEmployeeDialog"
            v-model="selectEmployeeDialog"
            append-to-body
            content-styles="padding: 0;"
        >
            <abc-transfer-v2
                :default-checked-keys="defaultCheckedKeys"
                :result-width="280"
                :props="{
                    label: 'name',
                }"
                leaf-icon="s-user-color"
                node-key="id"
                show-check-all
                show-search
                :result-title="resultTitle"
                :search-placeholder="searchPlaceholder"
                :data="employees"
                :search-node-method="handleSearch"
                @confirm="confirmSelect"
                @cancel="selectEmployeeDialog = false"
            >
            </abc-transfer-v2>
        </abc-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import BizPanelSelector from '@/components-composite/biz-panel-selector/index.js';
    export default {
        name: 'BizEmployeePanelSelector',
        components: {
            BizPanelSelector,
        },

        props: {
            /**
             * 选中的员工列表，支持 v-model 双向绑定
             * @type {Array<Employee>} Employee: { id: string, name: string, namePy: string, namePyFirst: string }
             */
            value: {
                type: Array,
                default: () => [],
            },
            /**
             * 添加按钮的文本
             * @type {string}
             * @default '添加'
             */
            addText: {
                type: String,
                default: '添加',
            },
            /**
             * 搜索框的占位文本
             * @type {string}
             * @default '搜索姓名'
             */
            searchPlaceholder: {
                type: String,
                default: '搜索姓名',
            },
            /**
             * 结果面板的标题
             * @type {string}
             * @default '请选择'
             */
            resultTitle: {
                type: String,
                default: '请选择',
            },
            /**
             * 员工数据列表
             * @type {Array<Employee>} Employee: { id: string, name: string, namePy: string, namePyFirst: string }
             * @description 用于展示在穿梭框左侧的员工列表，支持搜索
             */
            employees: {
                type: Array,
                default: () => [],
            },
            /**
             * 默认选中的员工ID列表
             * @type {Array<string>}
             * @description 初始化时默认选中的员工ID列表
             */
            defaultCheckedKeys: {
                type: Array,
                default: () => [],
            },
        },

        data() {
            return {
                selectEmployeeDialog: false,
            };
        },

        computed: {
            selectedEmployees: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
        },

        methods: {
            /**
             * 搜索员工
             * @param {string} val - 搜索关键词
             * @returns {Object} 搜索结果对象
             */
            handleSearch(val) {
                return {
                    key: val,
                    list: this.employees.filter((item) => {
                        return item.name.includes(val) ||
                            item.namePy.includes(val) ||
                            item.namePyFirst.includes(val.toUpperCase());
                    }),
                };
            },

            /**
             * 确认选择员工
             * @param {Array} payload - 选中的员工列表
             * @emits confirm
             */
            confirmSelect(payload) {
                this.selectedEmployees = payload;
                this.selectEmployeeDialog = false;
                this.$emit('confirm', payload);
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/mixin.scss";

.biz-employee-panel-selector-wrapper {
    .select-employee-list-wrapper {
        position: relative;
        min-height: 92px;
        max-height: 210px;
        padding: 10px 0 10px 10px;
        overflow-y: scroll;

        @include scrollBar(true);

        &.has-config-item {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
    }
}
</style>
