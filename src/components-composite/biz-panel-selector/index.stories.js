import BizPanelSelector from './src/views/index.vue';

export default {
    title: 'Components/选择器组件/BizPanelSelector',
    component: BizPanelSelector,
};

export const base = {
    render: () => ({
        components: {
            BizPanelSelector,
        },

        data() {
            return {
                data: [
                    {
                        id: 1,
                        name: '面包',
                    },
                    {
                        id: 2,
                        name: '馒头',
                    },
                ],
            };
        },

        methods: {
            onClickAdd() {
                this.data.push({
                    id: 3,
                    name: '包子',
                });
            },
        },

        template: `
            <biz-panel-selector v-model="data" @add="onClickAdd"></biz-panel-selector>
        `,
    }),

    name: '基础用法',
};
