# Tag Selector 标签选择器组件

## 组件介绍

Tag Selector是一个用于管理和展示患者标签的业务组件。它提供了标签的展示、添加、删除等功能，主要用于替换原有的患者标签管理模块。

## 功能特性

- 标签展示：以标签组的形式展示当前患者的所有标签
- 标签添加：通过弹出层选择并添加新的标签
- 标签删除：支持直接点击标签上的关闭按钮删除标签
- 自定义插槽：支持在标签组前插入自定义内容
- 数据同步：自动同步患者标签数据，支持实时更新

## 使用方法

### 基础用法

```vue
<template>
  <biz-tag-selector
    :patient-id="patientId"
    :crm-a-p-i="CrmAPI"
    :patient-info="patientInfo"
    :origin-labels="originLabels"
    :view-distribute-config="viewDistributeConfig"
    :is-single-store="isSingleStore"
    :current-clinic="currentClinic"
  />
</template>
```

### Props 说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| patientId | String/Number | 否 | '' | 患者ID |
| patientInfo | Object | 否 | {} | 患者信息对象 |
| addPatient | Boolean | 否 | false | 是否为新增患者场景 |
| originLabels | Array | 否 | [] | 原始标签数据 |
| viewDistributeConfig | Object | 否 | {} | 视图配置信息 |
| isSingleStore | Boolean | 否 | false | 是否为单店 |
| currentClinic | Object | 否 | {} | 当前诊所信息 |
| crmAPI | Function | 否 | () => ({}) | CRM API接口类 |

### Events 说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change-patient-tags | { tags } | 当患者标签发生变化时触发 |

## 替换规则

当需要在现有系统中使用Tag Selector替换原有标签管理模块时，需要遵循以下规则：

1. 组件引入和注册
```js
// 引入组件
import BizTagSelector from '@/components-composite/biz-tag-selector';
// 引入CrmAPI
import CrmAPI from 'api/crm';
// 引入Vuex actions
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  components: {
      BizTagSelector,
  },
  data() {
    return {
      // CrmAPI需要在data中声明后才能在template中使用
      CrmAPI,
    };
  }
}
```

2. Vuex相关配置
```js
export default {
  computed: {
    ...mapState('crm', ['originLabels']),
    ...mapState('viewDistribute', ['viewDistributeConfig']),
    ...mapGetters('clinic', ['isSingleStore', 'currentClinic']),
  },
  methods: {
    // 必须引入updateTagsByFetch action用于更新标签
    ...mapActions('crm', ['updateTagsByFetch']),
  }
}
```

3. 模板配置
```vue
<template>
  <biz-tag-selector
    :patient-id="patientId"
    :crm-a-p-i="CrmAPI"  <!-- CrmAPI必须在data中声明 -->
    :patient-info="patientInfo"
    :origin-labels="originLabels"
    :view-distribute-config="viewDistributeConfig"
    :is-single-store="isSingleStore"
    :current-clinic="currentClinic"
    @change-patient-tags="handlePatientTagsChange"
    @updateTagsByFetch="updateTagsByFetch"  <!-- 必须处理updateTagsByFetch事件 -->
  />
</template>
```

4. 事件处理
```js
methods: {
  // 处理标签变化事件
  handlePatientTagsChange(tagsInfo) {
    // 使用Object.assign更新患者信息
    Object.assign(this.patientInfo, tagsInfo);
  }
}
```

## 导入路径说明

1. 组件导入
```js
// ✅ 正确的导入方式
import BizTagSelector from '@/components-composite/biz-tag-selector';

// ❌ 错误的导入方式
import BizTagSelector from '@/components-composite/biz-tag-selector/src/views/index.vue';
import BizTagSelector from 'views/crm/component/biz-tag-selector.vue';
```

2. 导入规范
   - 使用`@/components-composite/biz-tag-selector`路径
   - 不要直接导入具体的vue文件
   - 保持导入路径的一致性
   - 遵循项目的模块化规范

## 注意事项

1. CrmAPI处理
   - CrmAPI不能直接在template中使用
   - 必须在data中声明后通过props传入
   - 确保CrmAPI包含所有必要的标签相关接口

2. Vuex状态管理
   - 必须引入并注册updateTagsByFetch action
   - 确保正确映射所需的state和getters
   - 标签更新后需要同步Vuex状态

3. 事件处理
   - 必须处理change-patient-tags事件
   - 必须处理updateTagsByFetch事件
   - 使用Object.assign而不是$set来更新患者信息

4. Props传递
   - 确保所有必要的props都正确传入
   - props的类型和默认值需要符合规范
   - 注意props的命名规范（驼峰式）

## 常见问题

1. CrmAPI未定义错误
   - 原因：未在data中声明CrmAPI
   - 解决：在data中添加CrmAPI声明

2. updateTagsByFetch未定义错误
   - 原因：未引入或注册updateTagsByFetch action
   - 解决：使用mapActions引入并注册

3. 标签更新后未同步
   - 原因：未正确处理change-patient-tags事件
   - 解决：在handlePatientTagsChange中使用Object.assign更新数据

## 最佳实践

1. 在使用组件时，建议先进行功能测试，确保所有功能正常运行
2. 保持原有的数据结构和接口规范
3. 遵循组件的生命周期和事件机制
4. 注意处理异常情况和边界条件
