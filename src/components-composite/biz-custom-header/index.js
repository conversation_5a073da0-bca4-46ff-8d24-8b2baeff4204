import { FunctionalDialog } from '@/views/common/functional-dialog.js';
import component from '@/components-composite/biz-custom-header/src/views/index.vue';

component.install = function (Vue) {
    Vue.component(component.name, component);
};

class BizCustomHeader extends FunctionalDialog {
    constructor(props, id = 'biz-goods-select-dialog') {
        super(props, component, id);
    }
}

export {
    BizCustomHeader,
};

export default component;
