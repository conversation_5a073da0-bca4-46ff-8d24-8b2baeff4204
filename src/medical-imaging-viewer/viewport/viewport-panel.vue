<template>
    <div
        :class="{
            'abc-viewer-viewport-grid__panel': true,
            'abc-viewer-viewport-grid__panel--is-active': active,
            'abc-viewer-viewport-grid__panel--no-border': noBorder
        }"
        v-on="$listeners"
    >
        <cornerstone-viewport
            class-name="abc-viewer-viewport-grid__cornerstone-viewport"
            :image-ids="viewportPanelData.stack && viewportPanelData.stack.imageIds"
            :image-id-index="0"
            :on-element-enabled="onElementEnabled"
            is-stack-prefetch-enabled
        >
        </cornerstone-viewport>
    </div>
</template>
<script>
    import cornerstone from '@/medical-imaging-viewer/cornerstone-core/src/index';
    import getTools from '../abc-viewer/extensions/cornerstone/utils/getTools.js';
    import setActiveAndPassiveToolsForElement
        from '../abc-viewer/extensions/cornerstone/utils/setActiveAndPassiveToolsForElement.js';
    import { StackManager } from '@ohif-core/utils/index.js';
    import CornerstoneViewport from '@/medical-imaging-viewer/viewport/cornerstone-viewport.vue';
    import { studyService } from '@/medical-imaging-viewer/store/study';
    import csTools from '@abc/cornerstone-tools';


    function _getCornerstoneStack(displaySet, dataSource) {
        // Get stack from Stack Manager
        const storedStack = StackManager.findOrCreateStack(displaySet, dataSource);

        // Clone the stack here so we don't mutate it
        const stack = { ...storedStack };

        return stack;
    }

    function _getViewportData(dataSource, displaySet) {
        const stack = _getCornerstoneStack(displaySet, dataSource);

        return {
            StudyInstanceUID: displaySet.StudyInstanceUID,
            displaySetInstanceUID: displaySet.displaySetInstanceUID,
            SeriesDate: displaySet.SeriesDate,
            stack,
        };
    }

    export default {
        components: {
            CornerstoneViewport,
        },
        inject: ['$abcViewer'],
        props: {
            dataSource: {
                type: Object,
                required: true,
            },
            displaySet: {
                type: Object,
                required: true,
            },
            viewportIndex: {
                type: Number,
                default: 0,
            },
            active: {
                type: Boolean,
                default: false,
            },
            noBorder: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                viewportPanelData: {},
            };
        },

        watch: {
            displaySet: {
                async handler(displaySet) {
                    const {
                        StudyInstanceUID,
                        displaySetInstanceUID,
                        sopClassUids,
                    } = displaySet;
                    if (!StudyInstanceUID || !displaySetInstanceUID) {
                        return;
                    }
                    if (sopClassUids && sopClassUids.length > 1) {
                        console.warn(
                            'More than one SOPClassUID in the same series is not yet supported.',
                        );
                    }
                    const data = await _getViewportData(this.dataSource, displaySet);
                    this.viewportPanelData = data;
                },
                deep: true,
                immediate: true,
            },
        },

        beforeDestroy() {
            StackManager.clearStacks();
        },

        methods: {
            onElementEnabled(evt) {
                const eventData = evt.detail;
                const targetElement = eventData.element;

                const tools = getTools();
                setActiveAndPassiveToolsForElement(targetElement, tools);
                // 激活 Zoom 工具
                csTools.setToolActiveForElement(targetElement, 'Zoom', { mouseButtonMask: 2 });
                targetElement.style.cursor = 'initial';

                // Update image after setting tool config
                const enabledElement = cornerstone.getEnabledElement(targetElement);

                if (enabledElement.image) {
                    cornerstone.updateImage(targetElement);
                }

                const OHIFCornerstoneEnabledElementEvent = new CustomEvent(
                    'ohif-cornerstone-enabled-element-event',
                    {
                        detail: {
                            context: 'ACTIVE_VIEWPORT::CORNERSTONE',
                            enabledElement: targetElement,
                            viewportIndex: this.viewportIndex,
                        },
                    },
                );

                document.dispatchEvent(OHIFCornerstoneEnabledElementEvent);
                this.loadMeasurements();
            },
            loadMeasurements() {
                const {
                    MeasurementService,
                } = this.$abcViewer.servicesManager.services;
                const source = MeasurementService.getSource('CornerstoneTools', '4');
                const measurements = studyService.getMeasurementDataList();

                function toMeasurementSchema(data) {
                    return {
                        ...data,
                    };
                }

                measurements.forEach((measurement) => {
                    MeasurementService.addRawMeasurement(source, source, toMeasurementSchema(measurement), (data) => data, this.dataSource);
                });
            },
        },
    };
</script>

<style lang="scss">
.abc-viewer-viewport-grid__panel {
    &.abc-viewer-viewport-grid__panel--is-active {
        &:not(.abc-viewer-viewport-grid__panel--no-border) {
            &::after {
                top: -3px;
                left: -3px;
                z-index: 1;
                width: calc(100% + 3px);
                height: calc(100% + 3px);
                pointer-events: none;
                border: 3px solid #5195ca !important;
            }
        }
    }

    &:not(.abc-viewer-viewport-grid__panel--no-border) {
        &:hover {
            &::after {
                top: -3px;
                left: -3px;
                width: calc(100% + 3px);
                height: calc(100% + 3px);
                border: 3px solid #254259;
            }
        }

        &::after {
            position: absolute;
            top: 0;
            left: 0;
            display: block;
            width: 100%;
            height: 100%;
            content: '';
            border-right: 3px solid #252525;
            border-bottom: 3px solid #252525;
        }
    }

    position: relative;

    .abc-viewer-viewport-grid__cornerstone-viewport {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;

        .viewport-element {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
    }

    &.abc-viewer-viewport-grid__is-drag-over {
        position: relative;

        &::after {
            top: 0;
            left: 0;
            z-index: 1;
            display: block;
            width: 100%;
            height: 100%;
            content: '';
            background: rgba(255, 255, 255, 0.1);
        }
    }
}
</style>
