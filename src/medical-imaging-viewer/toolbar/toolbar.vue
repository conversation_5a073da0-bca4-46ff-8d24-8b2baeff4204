<template>
    <div class="viewer-toolbar">
        <section v-for="(toolGroup, index) in toolGroups" :key="index" class="viewer-toolbar__group-section">
            <template v-for="tool in toolGroup.tools">
                <abc-popover
                    v-if="tool.type === 'custom'"
                    :key="tool.name"
                    placement="top"
                    width="auto"
                    trigger="hover"
                    :close-delay="0"
                    popper-class="viewer-tooltip-popper"
                >
                    <span slot="reference" class="viewer-toolbar__tool-item" @click="handleCustomToolClick(tool.key)">
                        <abc-icon :icon="tool.icon" size="18"></abc-icon>
                    </span>
                    <span>
                        {{ tool.name }}
                    </span>
                </abc-popover>

                <abc-popover
                    v-else-if="tool.type === 'layout'"
                    :key="tool.name"
                    v-model="visibleLayoutSelector"
                    placement="top"
                    width="auto"
                    trigger="manual"
                    @hide="handleLayoutPopperHide"
                >
                    <span slot="reference" class="viewer-toolbar__tool-item" @click="visibleLayoutSelector = !visibleLayoutSelector">
                        <abc-icon icon="grid" size="18"></abc-icon>
                    </span>
                    <layout-selector
                        ref="layoutSelector"
                        :layout-info="layoutInfo"
                        @select="handleLayoutSelect"
                    ></layout-selector>
                </abc-popover>
                <abc-popover
                    v-else
                    :key="tool.name"
                    placement="top"
                    width="auto"
                    trigger="hover"
                    :close-delay="0"
                    popper-class="viewer-tooltip-popper"
                >
                    <span
                        slot="reference"
                        :key="tool.name"
                        :class="['viewer-toolbar__tool-item', isToolActive(tool) ? 'is-active' : '']"
                        @click="handleToolClick(tool)"
                    >
                        <abc-icon :icon="tool.icon" size="18"></abc-icon>
                    </span>
                    <span>
                        {{ tool.name }}
                    </span>
                    <span v-if="tool.shortcut" class="viewer-toolbar__shortcut">
                        {{ tool.shortcut }}
                    </span>
                </abc-popover>
            </template>
        </section>
    </div>
</template>

<script>
    import LayoutSelector from '../components/layout-selector.vue';
    import {
        viewportGridService, state as viewportGridStore,
    } from '@/medical-imaging-viewer/store/viewport-grid.js';
    const KEY_CODE_SPACE = 32;
    const KEY_CODE_A = 65;
    const KEY_CODE_R = 82;
    const KEY_CODE_E = 69;
    const KEY_CODE_T = 84;
    const KEY_CODE_V = 86;

    export default {
        components: {
            LayoutSelector,
        },
        inject: ['$abcViewer'],
        data() {
            return {
                toolGroups: [
                    {
                        tools: [
                            {
                                name: '箭头',
                                toolName: 'ArrowAnnotate',
                                icon: 'arrow',
                                shortcut: 'A',
                                keyCode: KEY_CODE_A,
                            },
                            {
                                name: '矩形',
                                toolName: 'Rectangle',
                                icon: 'square',
                                shortcut: 'R',
                                keyCode: KEY_CODE_R,
                            },
                            {
                                name: '椭圆',
                                toolName: 'Elliptical',
                                icon: 'round',
                                shortcut: 'E',
                                keyCode: KEY_CODE_E,
                            },
                            {
                                name: '文本',
                                toolName: 'TextMarker',
                                icon: 'text',
                                shortcut: 'T',
                                keyCode: KEY_CODE_T,
                            },
                            {
                                name: '角度',
                                toolName: 'Angle',
                                icon: 'angle',
                                shortcut: 'V',
                                keyCode: KEY_CODE_V,
                            },
                        ],
                    },
                    {
                        tools: [
                            {
                                name: '调窗',
                                toolName: 'Wwwc',
                                icon: 'wl',
                            },
                            {
                                type: 'action',
                                name: '左右',
                                commandName: 'flipViewportHorizontal',
                                icon: 'r-l',
                            },
                            {
                                type: 'action',
                                name: '旋转',
                                commandName: 'rotateViewportCW',
                                icon: 'rotate',
                            },
                            {
                                type: 'action',
                                name: '复原',
                                commandName: 'resetViewport',
                                icon: 'reset',
                            },
                        ],
                    },
                    {
                        tools: [
                            {
                                name: '布局',
                                icon: 'grid',
                                type: 'layout',
                            },
                            {
                                name: '打印',
                                icon: 's-print-line1',
                                type: 'custom',
                                key: 'print',
                            },
                        ],
                    },
                ],
                activeTool: null,
                visibleLayoutSelector: false,
            };
        },
        computed: {
            layoutInfo() {
                return {
                    numCols: viewportGridStore.numCols,
                    numRows: viewportGridStore.numRows,
                };
            },
        },
        mounted() {
            window.addEventListener('keydown', this.handleKeyDown);
            window.addEventListener('keyup', this.handleKeyUp);
        },
        beforeDestroy() {
            window.removeEventListener('keydown', this.handleKeyDown);
            window.removeEventListener('keyup', this.handleKeyUp);
        },
        methods: {
            handleLayoutSelect({
                numCols, numRows,
            }) {
                console.log('numCols', numCols, 'numRows', numRows);
                viewportGridService.setLayout({
                    numCols, numRows,
                });
                this.visibleLayoutSelector = false;
            },
            handleLayoutPopperHide() {
                this.$refs.layoutSelector?.[0]?.reset();
            },
            handleToolClick(tool) {
                if (this.isToolActive(tool)) {
                    this.resetActiveTool();
                    return;
                }

                const {
                    toolName, type, commandName,
                } = tool;
                const {
                    commandsManager,
                } = this.$abcViewer;
                switch (type) {
                    case 'action':
                        commandsManager.runCommand(commandName);
                        break;
                    default:
                        this.activeTool = tool;
                        commandsManager.runCommand('setToolActive', { toolName });
                }
            },
            isToolActive(tool) {
                return this.activeTool?.name === tool.name;
            },
            resetActiveTool() {
                if (!this.activeTool) {
                    return;
                }
                const {
                    commandsManager,
                } = this.$abcViewer;
                // 是个 hack 手段，先激活 Pan，再使其 Passive，达到取消当前选中 Tool 的目的
                commandsManager.runCommand('setToolActive', { toolName: 'Pan' });
                commandsManager.runCommand('setToolPassive', { toolName: 'Pan' });
                commandsManager.runCommand('resetElementPointer');
                this.activeTool = null;
            },
            handleKeyDown(e) {
                const { keyCode } = e;
                const {
                    commandsManager,
                } = this.$abcViewer;
                if (keyCode === KEY_CODE_SPACE) {
                    // 按下空格，取消当前激活的工具
                    this.activeTool = null;
                    commandsManager.runCommand('setToolActive', { toolName: 'Pan' });
                }

                let keyCodeTriggerTool = null;
                this.toolGroups.find(({ tools }) => {
                    tools.forEach((tool) => {
                        if (tool.keyCode === keyCode) {
                            keyCodeTriggerTool = tool;
                        }
                    });
                });
                if (keyCodeTriggerTool) {
                    this.handleToolClick(keyCodeTriggerTool);
                }
            },
            handleKeyUp(e) {
                const { keyCode } = e;
                const {
                    commandsManager,
                } = this.$abcViewer;
                if (keyCode === KEY_CODE_SPACE) {
                    // 恢复鼠标指针
                    commandsManager.runCommand('resetElementPointer');
                    commandsManager.runCommand('setToolPassive', { toolName: 'Pan' });
                }
            },

            handleCustomToolClick(key) {
                this.$emit('custom-tool-click', key);
            },
        },
    };
</script>

<style lang="scss">
.viewer-toolbar {
    display: flex;
    justify-content: center;

    .viewer-toolbar__group-section {
        position: relative;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        div {
            margin-right: 10px;

            &:last-child {
                margin-right: 0;
            }
        }

        // 分割线
        &::after {
            display: inline-block;
            width: 1px;
            height: 12px;
            margin: 0 16px;
            content: " ";
            background: rgba(216, 216, 216, 0.3);
        }

        // 最后一组不显示小圆点
        &:last-child {
            &::after {
                display: none;
            }
        }
    }

    .viewer-toolbar__tool-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        margin-right: 10px;
        color: #ffffff;
        user-select: none;

        &:last-child {
            margin-right: 0;
        }

        &:hover {
            cursor: pointer;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
        }

        &:focus-visible {
            outline: 0;
        }

        &.is-active {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
        }
    }
}

.abc-popover.viewer-tooltip-popper {
    --popover-popper-arrow-size-w: 12px;
    --popover-popper-arrow-size-h: 6px;
    --popover-popper-fill-color: #212427;
    --popover-popper-border-color: var(--popover-popper-fill-color);

    display: flex;
    align-items: center;
    padding: 6px 8px;
    margin-top: 9px;
    color: rgba(255, 255, 255, 0.6);
    background: var(--popover-popper-fill-color);
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.91);

    .viewer-toolbar__container {
        display: flex;
        align-items: center;
    }

    .viewer-toolbar__shortcut {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 18px;
        height: 18px;
        padding: 0 3px;
        margin-left: 8px;
        font-size: 12px;
        line-height: 1;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--abc-border-radius-small);
    }
}
</style>
