import Vue from 'vue';
import { state as viewportGridData, viewportGridService } from '@/medical-imaging-viewer/store/viewport-grid.js';

export const imageListStore = Vue.observable({
    displaySet: [],
    currentIndex: 0,
    // 分组列表
    imageGroupList: [
        // {
        //     id: '',
        //     groupName: '2020-01-01',
        //     // 分组的图片列表
        //     imageList: [],
        // },
    ],
});

function addImageToGroup(image) {
    let group = imageListStore.imageGroupList.find((group) => group.id === image.groupId);
    if (!group) {
        group = {
            id: image.groupId,
            groupName: image.groupName,
            imageList: [],
        };
        imageListStore.imageGroupList.push(group);
    }
    group.imageList.push(image);
}

function addImage(image) {
    // 分配 index
    image.index = imageListStore.displaySet.length;
    imageListStore.displaySet.push(image);
    addImageToGroup(image);
}

function setCurrentIndex(currentIndex) {
    if (currentIndex + 1 > imageListStore.displaySet.length) {
        return;
    }
    imageListStore.currentIndex = currentIndex;
    const { displaySetInstanceUID } = imageListStore.displaySet[currentIndex];
    viewportGridService.setDisplaySetForViewport({
        viewportIndex: viewportGridData.activeViewportIndex,
        displaySetInstanceUID,
    });
}

function prevIndex() {
    if (imageListStore.currentIndex - 1 < 0) {
        return;
    }
    setCurrentIndex(imageListStore.currentIndex - 1);
}

function nextIndex() {
    if (imageListStore.currentIndex + 1 > imageListStore.displaySet.length) {
        return;
    }
    setCurrentIndex(imageListStore.currentIndex + 1);
}

function reset() {
    imageListStore.displaySet = [];
    imageListStore.imageGroupList = [];
    imageListStore.currentIndex = 0;
}

export const imageListService = {
    addImage,
    setCurrentIndex,
    prevIndex,
    nextIndex,
    reset,
};
