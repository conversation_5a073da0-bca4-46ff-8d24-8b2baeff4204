export const physicalRecordConfig = [
    {
        'key': 'temperature',
        'label': '体温',
        'unit': '℃',
        'list': [
            {
                'label': '',
                'value': ['36.0', 36.5, '37.0', 37.3, 37.4, 37.5, 37.6, 37.7, 37.8, 37.9 ],
            },
            {
                'label': '',
                'value': ['38.0', 38.1, 38.2, 38.3, 38.4, 38.5, 38.6, 38.7, 38.8, 38.9],
            },
            {
                'label': '',
                'value': ['39.0', 39.1, 39.2, 39.3, 39.4, 39.5, 39.6, 39.7, 39.8, 39.9],
            },
            {
                'label': '',
                'value': ['40.0', 40.1, 40.2, 40.3, 40.4, 40.5, 40.6, 40.7, 40.8, 40.9],
            },
            {
                'label': '',
                'value': ['41.0', 41.1, 41.2, 41.3, 41.4, 41.5, 41.6, 41.7, 41.8, 41.9],
            },
        ],
    },

    {
        'key': 'pulse',
        'label': '脉搏',
        'unit': 'bpm',
        'list': [
            {
                'label': '',
                'value': [30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80],
            },
            {
                'label': '',
                'value': [85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 135],
            },
            {
                'label': '',
                'value': [140, 145, 150, 155, 160, 165, 170, 175, 180],
            },
        ],
    },

    {
        'key': 'breathe',
        'label': '呼吸',
        'unit': '次/分',
        'list': [
            {
                'label': '',
                'value': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
            },
            {
                'label': '',
                'value': [20, 21, 22, 23, 24, 25, 26, 27, 28, 29],
            },
            {
                'label': '',
                'value': [30, 31, 32, 33, 34, 35, 36, 37, 38, 39],
            },
        ],
    },

    {
        'key': 'bloodPressure',
        'label': '血压',
        'unit': 'mmHg',
        'list': [
            {
                'label': '收缩压',
                'value': [
                    82, 84, 86, 88, 90, 92, 94, 96, 98, 100,
                    102, 104, 106, 108, 110, 112, 114, 116, 118, 120,
                    122, 124, 126, 128, 130, 132, 134, 136, 138, 140,
                    142, 144, 146, 148, 150, 152, 154, 156, 158, 160,
                    162, 164, 166, 168, 170, 172, 174, 176, 178, 180,
                ],
            },
            {
                'label': '舒张压',
                'value': [
                    52, 54, 56, 58, 60, 62, 64, 66, 68, 70,
                    72, 74, 76, 78, 80, 82, 84, 86, 88, 90,
                    92, 94, 96, 98, 100, 102, 104, 106, 108, 110,
                ],
            },
        ],
    },

    {
        'key': 'weight',
        'label': '体重',
        'unit': 'kg',
        isTab: true,
        selectedTab: 1,
        tabs: [
            {
                id: 1,
                name: '婴儿童(2~32)',
                value: [
                    '2.0', '2.5', '3.0', '3.5', '4.0', '4.5', '5.0', '5.5', '6.0', '6.5',
                    '7.0', '7.5', '8.0', '8.5', '9.0', '9.5', '10.0', '10.5', '11.0', '11.5',
                    '12.0', '12.5', '13.0', '13.5', '14.0', '14.5', '15.0', '15.5', '16.0', '16.5',
                    '17.0', '17.5', '18.0', '18.5', '19.0', '19.5', '20.0', '20.5', '21.0', '21.5',
                    '22.0', '22.5', '23.0', '23.5', '24.0', '24.5', '25.0', '25.5', '26.0', '26.5',
                    '27.0', '27.5', '28.0', '28.5', '29.0', '29.5', '30.0', '30.5', '31.0', '31.5', '32.0',
                ],
            },
            {
                id: 2,
                name: '青少年(30~60)',
                value: [
                    '30.0', '30.5', '31.0', '31.5', '32.0', '32.5', '33.0', '33.5', '34.0', '34.5',
                    '35.0', '35.5', '36.0', '36.5', '37.0', '37.5', '38.0', '38.5', '39.0', '39.5',
                    '40.0', '40.5', '41.0', '41.5', '42.0', '42.5', '43.0', '43.5', '44.0', '44.5',
                    '45.0', '45.5', '46.0', '46.5', '47.0', '47.5', '48.0', '48.5', '49.0', '49.5',
                    '50.0', '50.5', '51.0', '51.5', '52.0', '52.5', '53.0', '53.5', '54.0', '54.5',
                    '55.0', '55.5', '56.0', '56.5', '57.0', '57.5', '58.0', '58.5', '59.0', '59.5', '60.0',
                ],
            },
            {
                id: 3,
                name: '成年人(45~100)',
                value: [
                    '45', '46', '47', '48', '49', '50', '51', '52', '53', '54',
                    '55', '56', '57', '58', '59', '60', '61', '62', '63', '64',
                    '65', '66', '67', '68', '69', '70', '71', '72', '73', '74',
                    '75', '76', '77', '78', '79', '80', '81', '82', '83', '84',
                    '85', '86', '87', '88', '89', '90', '91', '92', '93', '94',
                    '95', '96', '97', '98', '99', '100',
                ],
            },
        ],
        'list': [{
            value: [
                '2.0', '2.5', '3.0', '3.5', '4.0', '4.5', '5.0', '5.5', '6.0', '6.5',
                '7.0', '7.5', '8.0', '8.5', '9.0', '9.5', '10.0', '10.5', '11.0', '11.5',
                '12.0', '12.5', '13.0', '13.5', '14.0', '14.5', '15.0', '15.5', '16.0', '16.5',
                '17.0', '17.5', '18.0', '18.5', '19.0', '19.5', '20.0', '20.5', '21.0', '21.5',
                '22.0', '22.5', '23.0', '23.5', '24.0', '24.5', '25.0', '25.5', '26.0', '26.5',
                '27.0', '27.5', '28.0', '28.5', '29.0', '29.5', '30.0', '30.5', '31.0', '31.5', '32.0',
            ],
        }],
    },

    {
        'key': 'height',
        'label': '身高',
        'unit': 'cm',
        isTab: true,
        selectedTab: 1,
        tabs: [
            {
                id: 1,
                name: '婴儿童(45~100)',
                value: [
                    '45', '46', '47', '48', '49', '50', '51', '52', '53', '54',
                    '55', '56', '57', '58', '59', '60', '61', '62', '63', '64',
                    '65', '66', '67', '68', '69', '70', '71', '72', '73', '74',
                    '75', '76', '77', '78', '79', '80', '81', '82', '83', '84',
                    '85', '86', '87', '88', '89', '90', '91', '92', '93', '94',
                    '95', '96', '97', '98', '99', '100',
                ],
            },
            {
                id: 2,
                name: '青少年(100~150)',
                value: [
                    '100', '101', '102', '103', '104', '105', '106', '107', '108', '109',
                    '110', '111', '112', '113', '114', '115', '116', '117', '118', '119',
                    '120', '121', '122', '123', '124', '125', '126', '127', '128', '129',
                    '130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                    '140', '141', '142', '143', '144', '145', '146', '147', '148', '149',
                    '150',
                ],
            },
            {
                id: 3,
                name: '成年人(150~200)',
                value: [
                    '150', '151', '152', '153', '154', '155', '156', '157', '158', '159',
                    '160', '161', '162', '163', '164', '165', '166', '167', '168', '169',
                    '170', '171', '172', '173', '174', '175', '176', '177', '178', '179',
                    '180', '181', '182', '183', '184', '185', '186', '187', '188', '189',
                    '190', '191', '192', '193', '194', '195', '196', '197', '198', '199',
                    '200',
                ],
            },
        ],
        'list': [{
            value: [
                '45', '46', '47', '48', '49', '50', '51', '52', '53', '54',
                '55', '56', '57', '58', '59', '60', '61', '62', '63', '64',
                '65', '66', '67', '68', '69', '70', '71', '72', '73', '74',
                '75', '76', '77', '78', '79', '80', '81', '82', '83', '84',
                '85', '86', '87', '88', '89', '90', '91', '92', '93', '94',
                '95', '96', '97', '98', '99', '100',
            ],
        }],
    },

];

export const temperatureRecord = {
    'key': 'temperature',
    'label': '体温',
    'unit': '℃',
    'list': [
        {
            'label': '',
            'value': ['36.0', 36.5, '37.0', 37.3, 37.4, 37.5, 37.6, 37.7, 37.8, 37.9 ],
        },
        {
            'label': '',
            'value': ['38.0', 38.1, 38.2, 38.3, 38.4, 38.5, 38.6, 38.7, 38.8, 38.9],
        },
        {
            'label': '',
            'value': ['39.0', 39.1, 39.2, 39.3, 39.4, 39.5, 39.6, 39.7, 39.8, 39.9],
        },
        {
            'label': '',
            'value': ['40.0', 40.1, 40.2, 40.3, 40.4, 40.5, 40.6, 40.7, 40.8, 40.9],
        },
        {
            'label': '',
            'value': ['41.0', 41.1, 41.2, 41.3, 41.4, 41.5, 41.6, 41.7, 41.8, 41.9],
        },
    ],
};


export const pulseRecord = {
    'key': 'pulse',
    'label': '脉搏',
    'unit': 'bpm',
    'list': [
        {
            'label': '',
            'value': [40, 42, 44, 46, 48, 50, 52, 54, 56, 58],
        },
        {
            'label': '',
            'value': [60, 62, 64, 66, 68, 70, 72, 74, 76, 78],
        },
        {
            'label': '',
            'value': [80, 82, 84, 86, 88, 90, 92, 94, 96, 98],
        },
        {
            'label': '',
            'value': [100, 102, 104, 106, 108, 110, 112, 114, 116, 118],
        },
        {
            'label': '',
            'value': [120, 122, 124, 126, 128, 130, 132, 134, 136, 138],
        },
    ],
};

export const heartRateRecord = {
    'key': 'heartRate',
    'label': '心率',
    'unit': '次',
    'list': [
        {
            'label': '',
            'value': [40, 42, 44, 46, 48, 50, 52, 54, 56, 58],
        },
        {
            'label': '',
            'value': [60, 62, 64, 66, 68, 70, 72, 74, 76, 78],
        },
        {
            'label': '',
            'value': [80, 82, 84, 86, 88, 90, 92, 94, 96, 98],
        },
        {
            'label': '',
            'value': [100, 102, 104, 106, 108, 110, 112, 114, 116, 118],
        },
        {
            'label': '',
            'value': [120, 122, 124, 126, 128, 130, 132, 134, 136, 138],
        },
    ],
};

export const breatheRecord = {
    'key': 'breathe',
    'label': '呼吸',
    'unit': '次/分',
    'list': [
        {
            'label': '',
            'value': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
        },
        {
            'label': '',
            'value': [20, 21, 22, 23, 24, 25, 26, 27, 28, 29],
        },
        {
            'label': '',
            'value': [30, 31, 32, 33, 34, 35, 36, 37, 38, 39],
        },
    ],
};
export const weightRecord = {
    'key': 'weight',
    'label': '体重',
    'unit': 'kg',
    isTab: true,
    selectedTab: 1,
    tabs: [
        {
            id: 1,
            name: '婴儿童(2~32)',
            value: [
                '2.0', '2.5', '3.0', '3.5', '4.0', '4.5', '5.0', '5.5', '6.0', '6.5',
                '7.0', '7.5', '8.0', '8.5', '9.0', '9.5', '10.0', '10.5', '11.0', '11.5',
                '12.0', '12.5', '13.0', '13.5', '14.0', '14.5', '15.0', '15.5', '16.0', '16.5',
                '17.0', '17.5', '18.0', '18.5', '19.0', '19.5', '20.0', '20.5', '21.0', '21.5',
                '22.0', '22.5', '23.0', '23.5', '24.0', '24.5', '25.0', '25.5', '26.0', '26.5',
                '27.0', '27.5', '28.0', '28.5', '29.0', '29.5', '30.0', '30.5', '31.0', '31.5', '32.0',
            ],
        },
        {
            id: 2,
            name: '青少年(30~60)',
            value: [
                '30.0', '30.5', '31.0', '31.5', '32.0', '32.5', '33.0', '33.5', '34.0', '34.5',
                '35.0', '35.5', '36.0', '36.5', '37.0', '37.5', '38.0', '38.5', '39.0', '39.5',
                '40.0', '40.5', '41.0', '41.5', '42.0', '42.5', '43.0', '43.5', '44.0', '44.5',
                '45.0', '45.5', '46.0', '46.5', '47.0', '47.5', '48.0', '48.5', '49.0', '49.5',
                '50.0', '50.5', '51.0', '51.5', '52.0', '52.5', '53.0', '53.5', '54.0', '54.5',
                '55.0', '55.5', '56.0', '56.5', '57.0', '57.5', '58.0', '58.5', '59.0', '59.5', '60.0',
            ],
        },
        {
            id: 3,
            name: '成年人(45~100)',
            value: [
                '45', '46', '47', '48', '49', '50', '51', '52', '53', '54',
                '55', '56', '57', '58', '59', '60', '61', '62', '63', '64',
                '65', '66', '67', '68', '69', '70', '71', '72', '73', '74',
                '75', '76', '77', '78', '79', '80', '81', '82', '83', '84',
                '85', '86', '87', '88', '89', '90', '91', '92', '93', '94',
                '95', '96', '97', '98', '99', '100',
            ],
        },
        {
            id: 4,
            name: '特殊情况',
            value: [
                '平车','轮椅','卧床','其他',
            ],
        },
    ],
    list: [ '2.0', '2.5', '3.0', '3.5', '4.0', '4.5', '5.0', '5.5', '6.0', '6.5',
        '7.0', '7.5', '8.0', '8.5', '9.0', '9.5', '10.0', '10.5', '11.0', '11.5',
        '12.0', '12.5', '13.0', '13.5', '14.0', '14.5', '15.0', '15.5', '16.0', '16.5',
        '17.0', '17.5', '18.0', '18.5', '19.0', '19.5', '20.0', '20.5', '21.0', '21.5',
        '22.0', '22.5', '23.0', '23.5', '24.0', '24.5', '25.0', '25.5', '26.0', '26.5',
        '27.0', '27.5', '28.0', '28.5', '29.0', '29.5', '30.0', '30.5', '31.0', '31.5', '32.0'],
};

export const heightRecord = {
    'key': 'height',
    'label': '身高',
    'unit': 'cm',
    isTab: true,
    selectedTab: 1,
    tabs: [
        {
            id: 1,
            name: '婴儿童(45~100)',
            value: [
                '45', '46', '47', '48', '49', '50', '51', '52', '53', '54',
                '55', '56', '57', '58', '59', '60', '61', '62', '63', '64',
                '65', '66', '67', '68', '69', '70', '71', '72', '73', '74',
                '75', '76', '77', '78', '79', '80', '81', '82', '83', '84',
                '85', '86', '87', '88', '89', '90', '91', '92', '93', '94',
                '95', '96', '97', '98', '99', '100',
            ],
        },
        {
            id: 2,
            name: '青少年(100~150)',
            value: [
                '100', '101', '102', '103', '104', '105', '106', '107', '108', '109',
                '110', '111', '112', '113', '114', '115', '116', '117', '118', '119',
                '120', '121', '122', '123', '124', '125', '126', '127', '128', '129',
                '130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                '140', '141', '142', '143', '144', '145', '146', '147', '148', '149',
                '150',
            ],
        },
        {
            id: 3,
            name: '成年人(150~200)',
            value: [
                '150', '151', '152', '153', '154', '155', '156', '157', '158', '159',
                '160', '161', '162', '163', '164', '165', '166', '167', '168', '169',
                '170', '171', '172', '173', '174', '175', '176', '177', '178', '179',
                '180', '181', '182', '183', '184', '185', '186', '187', '188', '189',
                '190', '191', '192', '193', '194', '195', '196', '197', '198', '199',
                '200',
            ],
        },
        {
            id: 4,
            name: '特殊情况',
            value: [
                '平车','轮椅','卧床','其他',
            ],
        },
    ],
    'list': [ '45', '46', '47', '48', '49', '50', '51', '52', '53', '54',
        '55', '56', '57', '58', '59', '60', '61', '62', '63', '64',
        '65', '66', '67', '68', '69', '70', '71', '72', '73', '74',
        '75', '76', '77', '78', '79', '80', '81', '82', '83', '84',
        '85', '86', '87', '88', '89', '90', '91', '92', '93', '94',
        '95', '96', '97', '98', '99', '100'],
};
export const urineOutputRecord = {
    'key': 'urineOut',
    'label': '小便',
    'unit': 'ml',
    isTab: true,
    selectedTab: 1,
    tabs: [
        {
            id: 1,
            name: '婴儿童(400~1000)',
            value: [
                '400', '450', '500', '550', '600',
                '650','700', '750','800', '850','900',
                '950', '1000',
            ],
        },
        {
            id: 2,
            name: '青少年(800~1400)',
            value: [
                '800', '850','900', '950', '1000',
                '1050', '1100', '1150','1200',
                '1250', '1300','1350', '1400',
            ],
        },
        {
            id: 3,
            name: '成年人(1500~3000)',
            value: [
                '1500','1550', '1600','1650', '1700','1750','1800','1850',
                '1900','1950', '2000','2050', '2100','2150','2200','2250',
                '2300','2350', '2400','2450', '2500','2550','3000',
            ],
        },
    ],
    'list': [
        '400', '450', '500', '550', '600',
        '650','700', '750','800', '850','900',
        '950', '1000',
    ],
};
export const outputsRecord = {
    'key': 'outputs',
    'label': '总出量',
    'unit': 'ml',
    'list': [
        {
            'label': '',
            'value': ['100','200', '300','400', '500','600','700','800', '900', '1000'],
        },
        {
            'label': '',
            'value': ['1100','1200', '1300','1400', '1500','1600','1700','1800', '1900', '2000'],
        },
        {
            'label': '',
            'value': ['2100','2200', '2300','2400', '2500','2600','2700','2800', '2900', '3000'],
        },
        {
            'label': '',
            'value': ['3100','3200', '3300','3400', '3500','3600','3700','3800', '3900', '4000'],
        },
        {
            'label': '',
            'value': ['4100','4200', '4300','4400', '4500','4600','4700','4800', '4900', '5000'],
        },
        {
            'label': '',
            'value': ['5100','5200', '5300','5400', '5500','5600','5700','5800', '5900', '6000'],
        },
    ],
};

export const systolicPressurePmRecord = {
    'key': 'systolicPressurePm',
    'label': '收缩压',
    'unit': 'mmHg',
    'list': [
        {
            'label': '',
            'value': [ 100, 102, 104, 106, 108, 110, 112, 114, 116, 118],
        },
        {
            'label': '',
            'value': [ 120, 124, 126, 128, 130, 132, 134, 136, 138, 140],
        },
        {
            'label': '',
            'value': [ 142, 144, 146, 148, 150, 152, 154, 156, 158, 160],
        },
        {
            'label': '',
            'value': [162, 164, 166, 168, 170, 172, 174, 176, 178],
        },
    ],
};
export const diastolicPressurePmRecord = {
    'key': 'diastolicPressurePm',
    'label': '舒张压',
    'unit': 'mmHg',
    'list': [
        {
            'label': '',
            'value': [ 30, 32, 34, 36, 38, 40, 42, 44, 46, 48],
        },
        {
            'label': '',
            'value': [50, 52, 54, 56, 58, 60, 62, 64, 66, 68],
        },
        {
            'label': '',
            'value': [ 70, 72, 74, 76, 78, 80,82, 84, 86, 88],
        },
        {
            'label': '',
            'value': [90, 92, 94, 96, 98, 100, 102, 104, 106, 108],
        },
    ],
};
export const painScoreRecord = {
    'key': 'painScore',
    'label': '疼痛',
    'unit': '',
    'list': [
        {
            'label': '',
            'value': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        },
    ],
};
export const urinateRecord = {
    'key': 'urinate',
    'label': '小便次数',
    'unit': '',
    'list': [
        {
            'label': '',
            'value': [1, 2, 3, 4, 5],
        },
        {
            'label': '',
            'value': [6, 7, 8, 9, 10],
        },
        {
            'label': '',
            'value': [11, 12, 13, 14, 15],
        },
        {
            'label': '',
            'value': [16, 17, 18, 19, 20],
        },
        {
            'label': '',
            'value': ['C', 'C+', '※'],
        },
    ],
    'splitIndexGroup': [3],
};

export const patientEventList = [
    {
        name: '入院',
        id: 1,
    },
    {
        name: '转入',
        id: 2,
    },
    {
        name: '手术',
        id: 3,
    },
    {
        name: '出院',
        id: 4,
    },
    {
        name: '死亡',
        id: 5,
    },
    {
        name: '分娩',
        id: 6,
    },
    {
        name: '外出',
        id: 7,
    },
    {
        name: '返回',
        id: 8,
    },
];

export const stoolsTypeList = [
    {
        name: '自行排便',
        id: 1,
    },
    {
        name: '灌肠排便',
        id: 2,
    },
    {
        name: '大便失禁',
        id: 3,
    },
    {
        name: '人工肛门',
        id: 4,
    },
];

export const bloodOxygenSaturation = {
    'key': 'bloodOxygenSaturation',
    'label': '血氧饱和度',
    'unit': '',
    'list': [
        {
            'label': '',
            'value': [70, 71, 72, 73, 74, 75, 76, 77, 78, 79],
        },
        {
            'label': '',
            'value': [80, 81, 82, 83, 84, 85, 86, 87, 88, 89],
        },
        {
            'label': '',
            'value': [90, 91, 92, 93, 94, 95, 96, 97, 98, 99],
        },
    ],
};

export const bloodSugar = {
    'key': 'bloodSugar',
    'label': '血糖',
    'unit': '',
    'list': [
        {
            'label': '',
            'value': ['4.0', 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 4.8, 4.9],
        },
        {
            'label': '',
            'value': ['5.0', 5.1, 5.2, 5.3, 5.4, 5.5, 5.6, 5.7, 5.8, 5.9],
        },
        {
            'label': '',
            'value': ['6.0', 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 6.8, 6.9],
        },
        {
            'label': '',
            'value': ['7.0', 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8, 7.9],
        },
        {
            'label': '',
            'value': ['8.0', 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7, 8.8, 8.9],
        },
        {
            'label': '',
            'value': ['9.0', 9.1, 9.2, 9.3, 9.4, 9.5, 9.6, 9.7, 9.8, 9.9],
        },
        {
            'label': '',
            'value': ['10.0', 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9],
        },
    ],
};

export const oxygenInhalation = {
    'key': 'oxygenInhalation',
    'label': '吸氧',
    'unit': '',
    'list': [
        {
            'label': '',
            'value': ['1.0', 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9],
        },
        {
            'label': '',
            'value': ['2.0', 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9],
        },
        {
            'label': '',
            'value': ['3.0', 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9],
        },
        {
            'label': '',
            'value': ['4.0', 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 4.8, 4.9],
        },
        {
            'label': '',
            'value': ['5.0', 5.1, 5.2, 5.3, 5.4, 5.5, 5.6, 5.7, 5.8, 5.9],
        },
    ],
};

export const pupilDiameter = {
    'key': 'pupilDiameter',
    'label': '瞳孔直径',
    'unit': '',
    'list': [
        {
            'label': '',
            'value': ['1.0', 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9],
        },
        {
            'label': '',
            'value': ['2.0', 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9],
        },
        {
            'label': '',
            'value': ['3.0', 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9],
        },
        {
            'label': '',
            'value': ['4.0', 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 4.8, 4.9],
        },
        {
            'label': '',
            'value': ['5.0', 5.1, 5.2, 5.3, 5.4, 5.5, 5.6, 5.7, 5.8, 5.9],
        },
        {
            'label': '',
            'value': ['6.0', 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 6.8, 6.9],
        },
    ],
};
