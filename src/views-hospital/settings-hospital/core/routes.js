import Index from '../index.vue';

import {
    MODULE_ID_MAP, RouterScope, 
} from 'utils/constants.js';



// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

export default {
    path: 'hospital-settings',
    name: '@hospital-settings',
    component: Index,
    meta: {
        name: '住院',
        moduleId: MODULE_ID_MAP.hospitalSetting,
        needAuth: true,
        pageAsyncClass: PageAsync,
        scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
    },
    redirect: {
        name: '@inpatient-area-setting',
    },
    children: [
    ],

};

