export const ListTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    'list': [{
        'label': '患者姓名',
        'key': 'patientName',
        'pinned': false,
        'position': 1,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '手机号',
        'key': 'patientMobile',
        'pinned': false,
        'position': 2,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '累计积分',
        'key': 'totalPoints',
        'pinned': false,
        'position': 3,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '抵扣积分总数',
        'key': 'deductPoints',
        'pinned': false,
        'position': 4,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '当前积分',
        'key': 'points',
        'pinned': false,
        'position': 5,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '可抵扣金额',
        'key': 'deductibleAmount',
        'pinned': false,
        'position': 6,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'colType': 'money',
        'headerStyle': {
            'textAlign': 'center',
        },
    }],
});
