export const ListTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    list: [
        {
            'label': '持卡人姓名',
            'key': 'patientName',
            'pinned': false,
            'position': 1,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },
        {
            'label': '开卡费',
            'key': 'cardPatientCardFee',
            'pinned': false,
            'position': 2,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
        },
        {
            'label': '手机号',
            'key': 'patientMobile',
            'pinned': false,
            'position': 3,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },
        {
            'label': '卡名称',
            'key': 'cardName',
            'pinned': false,
            'position': 4,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },
        {
            'label': '来源',
            'key': 'source',
            'pinned': false,
            'position': 5,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },
        {
            'label': '开卡人',
            'key': 'cardCreatedName',
            'pinned': false,
            'position': 6,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },
        {
            'label': '开卡时间',
            'key': 'cardPatientCreated',
            'pinned': false,
            'position': 7,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },
        {
            'label': '生效时间',
            'key': 'cardPatientBeginDate',
            'pinned': false,
            'position': 8,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },
        {
            'label': '有效期',
            'key': 'cardValidityPeriod',
            'pinned': false,
            'position': 9,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },
        {
            'label': '到期时间',
            'key': 'cardPatientEndDate',
            'pinned': false,
            'position': 10,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },
        {
            'label': '状态',
            'key': 'cardDudectIsCompleteStr',
            'pinned': false,
            'position': 11,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },
        {
            'label': '卡内剩余服务项目',
            'key': 'cardSurplusService',
            'pinned': false,
            'position': 12,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },
        {
            'label': '期初余额',
            'key': 'beginBalance',
            'pinned': false,
            'position': 13,
            'sortable': false,
            'style': {
                'flex': 1,'width': '300px','maxWidth': '300px','minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
            'children': [
                {
                    'label': '合计',
                    'key': 'beginBalanceTotal',
                    'pinned': false,
                    'position': 13,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
                {
                    'label': '本金',
                    'key': 'beginBalancePrincipal',
                    'pinned': false,
                    'position': 14,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
                {
                    'label': '赠金',
                    'key': 'beginBalancePresent',
                    'pinned': false,
                    'position': 15,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
            ],
        },
        {
            'label': '充值',
            'key': 'recharge',
            'pinned': false,
            'position': 16,
            'sortable': false,
            'style': {
                'flex': 1,'width': '300px','maxWidth': '300px','minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
            'children': [
                {
                    'label': '合计',
                    'key': 'rechargeTotal',
                    'pinned': false,
                    'position': 16,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
                {
                    'label': '本金',
                    'key': 'rechargePrincipal',
                    'pinned': false,
                    'position': 17,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
                {
                    'label': '赠金',
                    'key': 'rechargePresent',
                    'pinned': false,
                    'position': 18,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
            ],
        },
        {
            'label': '消费',
            'key': 'consume',
            'pinned': false,
            'position': 19,
            'sortable': false,
            'style': {
                'flex': 1,'width': '300px','maxWidth': '300px','minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
            'children': [
                {
                    'label': '合计',
                    'key': 'consumeTotal',
                    'pinned': false,
                    'position': 19,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
                {
                    'label': '本金',
                    'key': 'consumePrincipal',
                    'pinned': false,
                    'position': 20,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
                {
                    'label': '赠金',
                    'key': 'consumePresent',
                    'pinned': false,
                    'position': 21,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
            ],
        },
        {
            'label': '期末余额',
            'key': 'endBalance',
            'pinned': false,
            'position': 22,
            'sortable': false,
            'style': {
                'flex': 1,'width': '300px','maxWidth': '300px','minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
            'children': [
                {
                    'label': '合计',
                    'key': 'endBalanceTotal',
                    'pinned': false,
                    'position': 22,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
                {
                    'label': '本金',
                    'key': 'endBalancePrincipal',
                    'pinned': false,
                    'position': 23,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
                {
                    'label': '赠金',
                    'key': 'endBalancePresent',
                    'pinned': false,
                    'position': 24,
                    'sortable': false,
                    'style': {
                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                    },
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'colType': 'money',
                },
            ],
        },
    ],
});
