export const PersonnelTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    customPropConfig: {
        colType: 'money',
        'headerStyle': { 'textAlign': 'center' },
        style: {
            'flex': '1','width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },
    'list': [{
        'label': '开单人',
        'key': 'personnelName',
        'pinned': true,
        'sortable': false,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
        'rowSpanStyle': {
            'alignItems': 'center',
        },
    },{
        'label': '开单科室',
        'key': 'departmentName',
        'pinned': true,
        'sortable': false,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
        'rowSpanStyle': {
            'alignItems': 'center',
        },
    },{
        'label': '分类',
        'key': 'classifyName',
        'pinned': true,
        'sortable': false,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '130px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '合计',
        'key': 'fee-total',
        'pinned': false,
        'sortable': false,
        'colType': 'money',
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    }],
});
