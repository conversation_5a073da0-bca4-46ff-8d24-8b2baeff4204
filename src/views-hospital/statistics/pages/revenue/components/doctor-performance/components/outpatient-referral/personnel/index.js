export const PersonnelTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    'list': [{
        'label': '门店',
        'key': 'clinicName',
        'pinned': false,
        'position': 1,
        'sortable': false,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '86px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'left',
        },
    }, {
        'label': '转出医生',
        'key': 'referralDoctor',
        'pinned': false,
        'position': 2,
        'sortable': false,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '86px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'left',
        },
    }, {
        'label': '接诊次数',
        'key': 'diagnoseCount',
        'pinned': false,
        'position': 3,
        'sortable': true,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'right',
        },
    }, {
        'label': '转出次数',
        'key': 'referralCount',
        'pinned': false,
        'position': 4,
        'sortable': true,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'right',
        },
    }, {
        'label': '转出率',
        'key': 'referralRate',
        'pinned': false,
        'position': 5,
        'sortable': true,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'right',
        },
    }, {
        'label': '转出成交次数',
        'key': 'referralDealCount',
        'pinned': false,
        'position': 6,
        'sortable': true,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'right',
        },
    }, {
        'label': '转出成交金额',
        'key': 'referralDealAmount',
        'colType': 'money',
        'pinned': false,
        'position': 7,
        'sortable': true,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'right',
        },
    }, {
        'label': '计提金额',
        'key': 'commissionAmt',
        'colType': 'money',
        'pinned': false,
        'position': 8,
        'sortable': true,
        'headerStyle': { 'textAlign': 'center' },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'right',
        },
    }],
});

export const PersonnelDescriptionMap = Object.freeze({
    referralRate: 'referralRateDescRender',
    referralDealAmount: 'referralDealAmountDescRender',
});
