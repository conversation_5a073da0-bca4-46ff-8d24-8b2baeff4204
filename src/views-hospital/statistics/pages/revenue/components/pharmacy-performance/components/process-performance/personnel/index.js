
export const PersonnelTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    'list': [
        {
            'label': '门店',
            'key': 'clinicName',
            'pinned': false,
            'position': 1,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': '2',
                'width': '',
                'maxWidth': '',
                'minWidth': '86px',
                'paddingLeft': '',
                'paddingRight': '',
                'textAlign': 'left',
            },
        },
        {
            'label': '加工人',
            'key': 'operatorName',
            'pinned': false,
            'position': 2,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 2,
                'width': '',
                'maxWidth': '',
                'minWidth': '86px',
                'paddingLeft': '',
                'paddingRight': '',
                'textAlign': 'left',
            },
        },
        {
            'label': '加工服务',
            'key': 'processTypeText',
            'pinned': false,
            'position': 3,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': '3',
                'width': '',
                'maxWidth': '',
                'minWidth': '100px',
                'paddingLeft': '',
                'paddingRight': '',
                'textAlign': 'left',
            },
        },
        {
            'label': '中药饮片',
            'key': 'slicesPrescription',
            'position': 4,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': '',
                'width': '',
                'maxWidth': '',
                'minWidth': '',
                'paddingLeft': '',
                'paddingRight': '',
                'textAlign': 'center',
            },
            'children': [
                {
                    'label': '处方总数',
                    'key': 'slicesPrescriptionNum',
                    'position': 1,
                    'sortable': false,
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'style': {
                        'flex': 'none',
                        'width': '',
                        'maxWidth': '',
                        'minWidth': '120px',
                        'paddingLeft': '',
                        'paddingRight': '',
                        'textAlign': 'right',
                    },
                    'pinned': false,
                },
                {
                    'label': '剂数',
                    'key': 'slicesDoseCount',
                    'position': 2,
                    'sortable': false,
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'style': {
                        'flex': 'none',
                        'width': '',
                        'maxWidth': '',
                        'minWidth': '120px',
                        'paddingLeft': '',
                        'paddingRight': '',
                        'textAlign': 'right',
                    },
                    'pinned': false,
                },
            ],
            'pinned': false,
        },
        {
            'label': '中药颗粒',
            'key': 'granularPrescription',
            'position': 5,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': '',
                'width': '',
                'maxWidth': '',
                'minWidth': '',
                'paddingLeft': '',
                'paddingRight': '',
                'textAlign': 'right',
            },
            'children': [
                {
                    'label': '处方总数',
                    'key': 'granularPrescriptionNum',
                    'position': 1,
                    'sortable': false,
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'style': {
                        'flex': 'none',
                        'width': '',
                        'maxWidth': '',
                        'minWidth': '120px',
                        'paddingLeft': '',
                        'paddingRight': '',
                        'textAlign': 'right',
                    },
                    'pinned': false,
                },
                {
                    'label': '剂数',
                    'key': 'granularDoseCount',
                    'position': 2,
                    'sortable': false,
                    'headerStyle': {
                        'textAlign': 'center',
                    },
                    'style': {
                        'flex': 'none',
                        'width': '',
                        'maxWidth': '',
                        'minWidth': '120px',
                        'paddingLeft': '',
                        'paddingRight': '',
                        'textAlign': 'right',
                    },
                    'pinned': false,
                },
            ],
            'pinned': false,
        },
    ],
});
