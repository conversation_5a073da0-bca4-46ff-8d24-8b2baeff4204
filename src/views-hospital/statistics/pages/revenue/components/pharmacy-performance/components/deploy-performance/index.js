import { isEqual } from 'utils/lodash';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import ExportService from 'views/statistics/core/services/export/export-service';
import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import {
    DIMENSION_ENUM, dimensionLabelEnum,dimensionApiEnum,
} from '@/views-hospital/statistics/pages/revenue/components/pharmacy-performance/components/deploy-performance/constant';
import {
    PersonnelTableConfig,
} from '@/views-hospital/statistics/pages/revenue/components/pharmacy-performance/components/deploy-performance/personnel';
import {
    OrderTableConfig,
} from '@/views-hospital/statistics/pages/revenue/components/pharmacy-performance/components/deploy-performance/order';
import PharmacyAPI from 'views/statistics/core/api/pharmacy';

// 调配业绩
export default class DeployPerformanceTable extends StatBaseTable {
    constructor(view) {
        super(view);
        this.dimension = DIMENSION_ENUM.PERSONNEL;

        this.employeeList = [];
        this.dispensingStatusOptions = [];
        this.prescriptionTypeOptions = [];
        // key与renderType的映射关系
        this.renderTypeMap = {

        };
        // key与headerRenderType的映射关系
        this.headerRenderTypeMap = {

        };
        // key与headerAppendRenderType的映射关系
        this.headerAppendRenderTypeMap = {

        };
        this.descriptionMap = {
            typeNumber: 'typeNumberDescRender',
            slicesDoseAndUnitCount: 'slicesDoseAndUnitCountDescRender',
        };
    }

    static renderTypeList = {
        // eslint-disable-next-line no-unused-vars
        'typeNumberDescRender': (h) => {
            return (
                <div>
                    <p>
                        发药单中的项目种类合计。用来评估发药人员的工作量。
                    </p>
                    <p>例：</p>
                    <p>
                        * 发药单A中 三七 30g，阿莫西林
                        3盒，发药种数统计为 2。
                    </p>
                    <p>
                        * 发药单B中 三七 50g，阿莫西林
                        1盒，发药种数统计为 2。
                    </p>
                    <p>* 2笔发药单发药种数累计为 4</p>
                </div>
            );
        },
        // eslint-disable-next-line no-unused-vars
        'slicesDoseAndUnitCountDescRender': (h) => {
            return (
                <div>
                    <p>
                        发药单中项目总数量合计。用来评估发药人员的工作量。
                    </p>
                    <p>例：</p>
                    <p>
                        * 发药单A中 三七 30g，当归 20g，共3剂，味数x剂数为 6。
                    </p>
                    <p>
                        * 发药单B中 三七 50g，当归 30g，共2剂，味数x剂数为 4。
                    </p>
                    <p>* 2笔发药单味数x剂数累计为 10</p>
                </div>
            );
        },
    };

    async init() {
        this.initFeatureConfig();
    }

    initFeatureConfig() {
        this.view.setTableCustomConfig(this.createFeatureConfigs(),true);
        this.initFilterOptions();
    }
    async initFilterOptions() {
        this.refreshFeatureConfig();
    }

    async refreshFeatureConfig() {
        const fetchEmployeeSelection = this.fetchEmployeeSelection();
        const getDispensingStatusSelection = this.getDispensingStatusSelection();
        const getPrescriptionTypeSelection = this.getPrescriptionTypeSelection();
        const mapping = {
            [DIMENSION_ENUM.PERSONNEL]: [fetchEmployeeSelection,getDispensingStatusSelection],
            [DIMENSION_ENUM.ORDER]: [fetchEmployeeSelection,getDispensingStatusSelection,getPrescriptionTypeSelection],
        };
        await Promise.all(mapping[this.dimension]);
        this.view.setTableCustomConfig(this.createFeatureConfigs());
    }

    createStaticConfig() {
        const mapping = {
            [DIMENSION_ENUM.PERSONNEL]: PersonnelTableConfig,
            [DIMENSION_ENUM.ORDER]: OrderTableConfig,
        };
        return mapping[this.dimension];
    }

    createFeatureConfigs() {
        const EnablePaginationMapping = {
            [DIMENSION_ENUM.PERSONNEL]: false,
            [DIMENSION_ENUM.ORDER]: true,
        };
        const { permission } = usePermission('isChainAdmin','subClinics');
        const { subClinics } = permission;
        return {
            exportTaskType: 'achievement-dispensing',
            tableKey: 'pharmacy-deploy-performance',
            isEnablePagination: EnablePaginationMapping[this.dimension],
            filterTools: [
                {
                    type: 'datePicker',
                    valueKey: 'dateFilter',
                    initData: this.view.comDateFilter,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                },
                {
                    type: 'select',
                    valueKey: 'employeeId',
                    initData: '',
                    clearable: true,
                    placeholder: '调配人',
                    width: 120,
                    options: this.employeeList,
                },
                {
                    type: 'select',
                    valueKey: 'dispensingStatus',
                    initData: '',
                    clearable: true,
                    placeholder: '发药状态',
                    width: 120,
                    options: this.dispensingStatusOptions,
                },
                {
                    type: 'select',
                    valueKey: 'prescriptionType',
                    clearable: true,
                    placeholder: '处方类型',
                    width: 120,
                    options: this.prescriptionTypeOptions,
                    isHidden: this.dimension !== DIMENSION_ENUM.ORDER,
                },
            ].filter((item) => !item.isHidden),
            dimensions: {
                options: [
                    {
                        label: dimensionLabelEnum[DIMENSION_ENUM.PERSONNEL],
                        value: DIMENSION_ENUM.PERSONNEL,
                    },
                    {
                        label: dimensionLabelEnum[DIMENSION_ENUM.ORDER],
                        value: DIMENSION_ENUM.ORDER,
                    },
                ],
                curDimension: this.dimension,
            },
        };
    }

    createFetchParams() {
        const {
            begin, end,
        } = this.view.filterParams.dateFilter;
        const {
            employeeId,prescriptionType,dispensingStatus,
        } = this.view.filterParams;
        const { permission: PermissionParams } = usePermission('enablePatientMobile');
        const baseParams = {
            beginDate: begin,
            endDate: end,
            clinicId: this.queryClinicId(),
            operationType: 9, //调配业绩
            employeeId,
            dispensingStatus,
        };
        if (this.dimension === DIMENSION_ENUM.ORDER) {
            return {
                ...baseParams,
                ...PermissionParams,
                prescriptionType,
                offset: this.view.pageParams.offset,
                size: this.view.pageParams.limit,
            };
        }
        return baseParams;
    }

    async loadTableData() {
        const beforeParams = this.createFetchParams();
        this.loading = true;
        try {
            const { data } = await PharmacyAPI[dimensionApiEnum[this.dimension]]({
                ...beforeParams,
            });
            const afterParams = this.createFetchParams();
            if (isEqual(beforeParams, afterParams)) {
                this.setTableInfo({
                    tableInfo: data,
                });
            }
        } catch (e) {
            console.log(e);
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];
        const staticConfig = this.createStaticConfig();
        const { renderTypeList } = DeployPerformanceTable;
        this.tableRenderConfig = resolveRenderConfig(header,renderTypeList,staticConfig,
            this.renderTypeMap,this.headerRenderTypeMap,this.headerAppendRenderTypeMap,this.descriptionMap);
    }

    async fetchEmployeeSelection() {
        const params = this.createFetchParams();
        try {
            const { data } = await PharmacyAPI.getOperationSelection({
                ...params,
            });
            this.employeeList = (data ?? []).map((item) => ({
                label: item.name,
                value: item.id,
            }));
        } catch (error) {
            this.employeeList = [];
            console.error(error);
        }
    }

    async getDispensingStatusSelection() {
        const params = this.createFetchParams();
        try {
            const { data } = await PharmacyAPI.getDispensingStatus({
                ...params,
            });
            if (data) {
                this.dispensingStatusOptions = (data ?? []).map((item) => {
                    return {
                        label: item.name,
                        value: `${item.id}`,
                    };
                });
            }
        } catch (error) {
            this.dispensingStatusOptions = [];
            console.error(error);
        }
    }

    async getPrescriptionTypeSelection() {
        const params = this.createFetchParams();
        try {
            const { data } = await PharmacyAPI.getPrescriptionType({
                ...params,
            });
            this.prescriptionTypeOptions = (data ?? []).map((item) => ({
                label: item.name,
                value: item.id,
            }));
        } catch (error) {
            this.prescriptionTypeOptions = [];
            console.error(error);
        }
    }

    async export() {
        this.exportService = new ExportService();
        const params = this.createFetchParams();
        try {
            await this.exportService.startExport(this.view.tableCustomConfig?.exportTaskType, {
                ...params,
            });
        } catch (e) {
            console.error(e);
            return false;
        }
        return true;
    }

    setDimension(val) {
        this.dimension = val;
        this.view.setTableCustomConfig(this.createFeatureConfigs(),true);
    }
}
