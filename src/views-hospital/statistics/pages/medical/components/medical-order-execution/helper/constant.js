import MedicalOrderExecutionAnalysisTable
    from '@/views-hospital/statistics/pages/medical/components/medical-order-execution/components';

export const TABLE_CATEGORY_ENUM = Object.freeze({
    EXECUTION: 1,
});

export const tableInstanceConfigEnum = Object.freeze({
    [TABLE_CATEGORY_ENUM.EXECUTION]: MedicalOrderExecutionAnalysisTable,
});

//报表类table的scope 已和后端确认
export const ReportScopeEnum = Object.freeze({
    OUTPATIENT_HOSPITAL: 0, //门诊+住院
    OUTPATIENT: 1, //门诊
    HOSPITAL: 2, //住院
    OUTPATIENT_HOSPITAL_PE: 3, // 门诊+住院+体检
    PHYSICAL_EXAMINATION: 4, //体检
});

export const REPORT_TABLE_SCOPE = Object.freeze({
    [TABLE_CATEGORY_ENUM.EXECUTION]: ReportScopeEnum.OUTPATIENT,
});
