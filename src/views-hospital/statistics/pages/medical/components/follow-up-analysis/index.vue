<template>
    <stat-table-layout
        :table-key="tableKey"
        :table-layout-config="tableLayoutConfig"
        :is-enable-pagination="isEnablePagination"
        :handle-table-mounted="handleTableMounted"
        @table-prepared="handleTablePrepared"
        @page-changed="handlePageChanged"
    >
        <template #header>
            <stat-toolbar
                :feature-configs="featureConfigs"
                :print-disabled="printBtnDisabled"
                :filter-params="{
                    tableCategory,
                    ...filterParams
                }"
                :table-key="tableKey"
                :handle-export="handleExport"
                @folded-filter="handleFoldedFilter"
                @dimension-change="handleDimensionChange"
            ></stat-toolbar>
        </template>
        <template #content>
            <component
                :is="curComponent"
                ref="table"
                :loading="tablePresenter?.loading ?? false"
                :render-config="tablePresenter?.tableRenderConfig"
                :data-list="curDataList"
                :filter-params="filterParams"
                :total-info="tablePresenter?.totalInfo"
                :summary-data="tablePresenter?.summaryData ?? {}"
                :page-params="pageParams"
                @sortChange="handleTableSortChange"
            >
            </component>
        </template>
    </stat-table-layout>
</template>

<script>
    import StatToolbar from '@/views-hospital/statistics/components/stat-toolbar/index.vue';
    import StatNormalTable from '@/views-hospital/statistics/components/stat-normal-table.vue';
    import StatTableLayout from '@/views-hospital/statistics/components/stat-table-layout.vue';

    import {
        REPORT_TABLE_SCOPE,
        TABLE_CATEGORY_ENUM,
        tableInstanceConfigEnum,
    } from '@/views-hospital/statistics/pages/medical/components/follow-up-analysis/helper/constant';
    import { resolveToFilterParams } from '@/views-hospital/statistics/helper/utils';
    import usePagination from '@/hooks/abc-ui/use-table-pagination';
    import { mapGetters } from 'vuex';

    export default {
        name: 'FollowUpAnalysisReport',
        components: {
            StatToolbar,
            StatNormalTable,
            StatTableLayout,
        },
        props: {
            dateFilter: {
                type: Object,
                default: () => ({}),
            },
        },
        setup() {
            const {
                pageParams,
                setPageSize,
                changePageIndex,
                resetPageIndex,
                setPageTotal,
            } = usePagination();
            return {
                pageParams,
                setPageSize,
                changePageIndex,
                resetPageIndex,
                setPageTotal,
            };
        },
        data() {
            return {
                tableCategory: '',
                tablePresenter: null, //table实例
                isTablePrepared: false, //表格是否准备好 即有header后重新计算paginationLimit
                filterParams: {}, //请求筛选参数
                tableCustomConfig: {}, //table配置 包含filter工具
            };
        },
        computed: {
            ...mapGetters(['currentClinic']),
            clinicId() {
                return this.currentClinic ? this.currentClinic.clinicId : '';
            },
            comDateFilter: {
                get() {
                    return this.dateFilter;
                },
                set(val) {
                    this.$emit('update:dateFilter', val);
                },
            },
            curComponent() {
                return StatNormalTable;
            },
            tableKey() {
                return this.tableCustomConfig?.tableKey ;
            },
            isEnablePagination() {
                return this.tableCustomConfig?.isEnablePagination ?? false;
            },
            printBtnDisabled() {
                const data = this.tablePresenter?.tableData;
                return !(Array.isArray(data) && data.length !== 0);
            },
            curDataList() {
                const tableData = this.tablePresenter?.table?.tableData ?? [];
                return !this.isEnablePagination ? tableData : (this.isTablePrepared ? tableData.slice(0,this.pageParams.limit) : []);
            },
            featureConfigs() {
                const filterTools = this.tableCustomConfig?.filterTools ?? [];
                const customConfig = filterTools.map((item) => ({
                    ...item,
                    changeMethod: this.handleFilterChange,
                }));
                return {
                    ...this.tableCustomConfig,
                    filterTools: customConfig,
                };
            },
            tableLayoutConfig() {
                return {
                    pageParams: this.pageParams,
                    table: this.tablePresenter?.table ?? {},
                };
            },
        },
        created() {
            this.initTable(TABLE_CATEGORY_ENUM.FOLLOWUP);
        },
        methods: {
            async handleTableMounted(data = {}) {
                if (this.isEnablePagination) {
                    this.isTablePrepared = false;
                    await this.setPageSize(data.paginationLimit);
                    if (this.tablePresenter) {
                        await this.loadTableData();
                        const { table } = this.tablePresenter;
                        return new Promise((resolve) => {
                            resolve(table);
                        });
                    }
                } else {
                    this.isTablePrepared = true;
                    await this.loadTableData();
                }
            },
            handleTablePrepared(data) {
                this.setPageSize(data.paginationLimit);
                this.isTablePrepared = true;
            },

            //初始化table
            async initTable(val) {
                //获取table实例
                this.tablePresenter = tableInstanceConfigEnum[val] && new tableInstanceConfigEnum[val](this, REPORT_TABLE_SCOPE[val]);
                if (!this.tablePresenter) return;
                await this.tablePresenter.init();
                this.tableCategory = val;
            },
            setTableCustomConfig(config, isInit = false) {
                this.tableCustomConfig = config;
                if (isInit) {
                    //根据filterTools处理成真正的请求参数
                    this.filterParams = resolveToFilterParams(this.tableCustomConfig?.filterTools ?? []);
                }
            },
            async loadTableData(isRestPage = true) {
                if (isRestPage) {
                    this.resetPageIndex();
                }
                await this.tablePresenter.loadTableData();
                //获取table-renderConfig
                this.tablePresenter.createRenderConfig();
            },
            async handleFilterChange(filterValueKey, val, needReset = true) {
                if (filterValueKey === 'tableCategory') {
                    if (val === this.tableCategory) return;
                    await this.initTable(val);
                } else {
                    this.filterParams[filterValueKey] = val;
                    if (filterValueKey === 'dateFilter') {
                        this.comDateFilter = val;
                    }
                    this.tablePresenter.refreshFeatureConfig && this.tablePresenter.refreshFeatureConfig();
                    if (needReset) {
                        this.resetPageIndex();
                        this.loadTableData();
                    }
                }
            },
            handleFoldedFilter(foldedFilterParams) {
                this.filterParams = {
                    ...this.filterParams,
                    ...foldedFilterParams,
                };
                this.resetPageIndex();
                this.loadTableData();
            },
            handlePageChanged(data) {
                this.changePageIndex(data);
                this.loadTableData(false);
            },
            async handleExport() {
                if (this.tablePresenter.export) {
                    return this.tablePresenter.export();
                }
            },
            handleDimensionChange(val) {
                this.tablePresenter.setDimension && this.tablePresenter.setDimension(val);
                this.tablePresenter.refreshFeatureConfig && this.tablePresenter.refreshFeatureConfig();
            },
            handleTableSortChange(val) {
                this.filterParams.sortConfig = {
                    orderBy: val.orderBy,
                    orderType: val.orderType,
                };
                this.loadTableData();
            },
        },
    };
</script>
