import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { AbcDatePickerBar } from '@abc/ui-pc';
const { DatePickerBarOptions } = AbcDatePickerBar;
import {
    formatDate, parseTime, prevDate,
} from '@abc/utils-date';
import { dateRangeFormat } from 'views/statistics/common/util';
import OperationApi from 'views/statistics/core/api/business-manage';

export default class OutpatientOperationSituationReportTable extends StatBaseTable {
    constructor(...args) {
        super(...args);

        this.summaryData = {};

        this.departmentVisitData = [];
        this.departmentVisitCategory = [];
        this.doctorVisitData = [];
        this.doctorVisitCategory = [];
        this.doctorRevisitCountRankingData = [];
        this.doctorRevisitCountRankingCategory = [];

        this.loading = {
            visitCount: false,
            newVsOld: false,
            memberComparison: false,
            doctorRevisitCountRanking: false,
        };
    }

    initFeatureConfig() {
        this.view.setChartCustomConfig(this.createChartList());
        super.initFeatureConfig();
    }

    async refreshFeatureConfig() {
        await Promise.all([
            this.fetchSummaryData(),
            this.fetchDepartmentVisitData(),
            this.fetchDoctorVisitData(),
        ]);
        this.view.setChartCustomConfig(this.createChartList(), true);
        await super.refreshFeatureConfig();
    }

    async updateFeatureConfig() {
        this.view.setChartCustomConfig(this.createChartList(), true);
        await super.refreshFeatureConfig();
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin','subClinics');
        const { subClinics } = permission;
        const date = formatDate(new Date());
        return {
            isEnableExport: false,
            filterTools: [
                {
                    type: 'datePickerBar',
                    valueKey: 'dateType',
                    dateFilterKey: 'dateFilter',
                    options: [
                        {
                            label: DatePickerBarOptions.DAY.label,
                            name: DatePickerBarOptions.DAY.name,
                            getValue() {
                                return [new Date(), new Date()];
                            },
                        },
                        DatePickerBarOptions.WEEK,
                        DatePickerBarOptions.MONTH,
                        DatePickerBarOptions.YEAR,
                    ],
                    pickerOptions: DatePickerBarOptions,
                    initData: DatePickerBarOptions.DAY.label,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '医院',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                },
                {
                    type: '',
                    valueKey: 'dateFilter',
                    initData: {
                        begin: date,
                        end: date,
                    },
                },
                {
                    type: '',
                    valueKey: 'trendDateType',
                    initData: DatePickerBarOptions.LATEST_WEEK.label,
                },
                {
                    type: '',
                    valueKey: 'trendDateTypeRange',
                    initData: {
                        begin: parseTime(prevDate(new Date(), 7), 'y-m-d', true),
                        end: date,
                    },
                },
            ].filter((item) => !item.isHidden),
        };
    }
    createChartList() {
        const {
            dateFilter: {
                begin, end,
            } = {},
            trendDateTypeRange = {},
            clinicId,
        } = this.view.filterParams;
        const dateText = dateRangeFormat(begin, end);
        const { permission } = usePermission('isChainAdmin','subClinics');
        const {
            subClinics, isChainAdmin,
        } = permission;
        const status = subClinics.some((clinic) => (clinic.id === clinicId && clinic.name === '总部') || !clinicId);
        const isHeadOffice = isChainAdmin && status;
        return [
            {
                flex: '0 0 100%',
                type: 'statistic-card-group',
                minWidth: 360,
                cardItem: [
                    {
                        chartCategory: 'visitCount',
                        topTitle: '到院人次',
                        topContent: dateText,
                        labelTips: '完诊人次+零售人次',
                        title: this.summaryData?.arrivalPeopleCount || 0,
                    },
                    {
                        chartCategory: 'newVsOld',
                        topTitle: isHeadOffice ? '连锁新客/老客' : '医院新客/老客',
                        topContent: dateText,
                        labelTips: isHeadOffice ? '首次到连锁就诊/消费则为连锁新客' : '首次到医院就诊/消费则为医院新客',
                        title: isHeadOffice ?
                            `${this.summaryData?.chainClinicNewCustomers || 0}/${this.summaryData?.chainClinicOldCustomers || 0}` :
                            `${this.summaryData?.clinicNewCustomers || 0}/${this.summaryData?.clinicOldCustomers || 0}`,
                    },
                    {
                        chartCategory: 'retailVisits',
                        topTitle: '零售人次',
                        topContent: dateText,
                        title: this.summaryData?.retailTraffic || 0,
                    },
                    {
                        chartCategory: 'completePatientCount',
                        topTitle: '完诊付费人次/完诊人次',
                        topContent: dateText,
                        title: `${this.summaryData?.patientCompletePayCount || 0}/${this.summaryData?.patientCompleteCount || 0}`,
                    },
                    {
                        chartCategory: 'newVsReturningPatients',
                        topTitle: '初诊患者/复诊患者',
                        topContent: dateText,
                        labelTips: '首次就诊某医生则为该医生的初诊患者',
                        title: `${this.summaryData?.firstVisitPatientCount || 0}/${this.summaryData?.returnVisitPatientCount || 0}`,
                    },
                ],
            },
            {
                flex: '0 0 100%',
                type: 'statistic-card-group',
                minWidth: 720,
                cardItem: [
                    {
                        type: 'bar-chart',
                        chartCategory: 'departmentVisits',
                        label: '科室接诊',
                        data: this.departmentVisitData,
                        category: this.departmentVisitCategory,
                        chartProps: {
                            nameList: ['初诊人次', '复诊人次'],
                            xAxisData: this.departmentVisitCategory,
                            aAxisType: 'category',
                            yAxisType: 'value',
                            stack: true,
                            width: '100%',
                            height: '250px',
                            barWidth: 32,
                            trigger: 'axis',
                            legend: {
                                data: ['初诊人次', '复诊人次'],
                                bottom: '5%',
                            },
                            barColors: ['#2680F7', '#40C6C2'],
                            grid: {
                                left: '1%',
                                right: '0',
                                top: '10%',
                                bottom: '20%',
                                containLabel: true,
                                width: '100%',
                            },
                            tooltipFormatter: (params) => {
                                let str = `${params[0].name}<br/>`;
                                params.forEach((item) => {
                                    str += `<div style="display: flex;justify-content: space-between"><span>${item.marker} ${item.seriesName}</span><span style="text-align: right;min-width: 40px;display: inline-block">${item.value}</span></div>`;
                                });
                                str += `<div style="display: flex;justify-content: space-between"><span>合计</span><span style="text-align: right;min-width: 40px;display: inline-block">${params.reduce((acc, item) => acc + Number(item.value), 0)}</span></div>`;
                                return str;
                            },
                            yAxis: {
                                type: 'value',
                                axisLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                splitLine: {
                                    lineStyle: {
                                        color: ['#E6EAEE'],
                                    },
                                },
                            },
                        },
                        dateConfig: {
                            showDate: true,
                            dateText,
                        },
                    },
                    {
                        type: 'bar-chart',
                        chartCategory: 'doctorVisits',
                        label: '医生接诊',
                        data: this.doctorVisitData,
                        category: this.doctorVisitCategory,
                        chartProps: {
                            nameList: ['初诊人次', '复诊人次'],
                            xAxisData: this.doctorVisitCategory,
                            aAxisType: 'category',
                            yAxisType: 'value',
                            stack: true,
                            width: '100%',
                            height: '250px',
                            barWidth: 32,
                            trigger: 'axis',
                            legend: {
                                data: ['初诊人次', '复诊人次'],
                                bottom: '5%',
                            },
                            colors: ['#2680F7', '#40C6C2'],
                            grid: {
                                left: '1%',
                                right: '0',
                                top: '10%',
                                bottom: '20%',
                                containLabel: true,
                                width: '100%',
                            },
                        },
                        dateConfig: {
                            showDate: true,
                            dateText,
                        },
                    },
                ],
            },
            {
                flex: '1',
                type: 'line-chart',
                chartCategory: 'visitorsTrend',
                label: '客流趋势',
                data: this.newCustomersTrendData,
                date: this.newCustomersTrendDates,
                chartProps: {
                    width: '100%',
                    height: '218px',
                    seriesName: '到院人数,新客',
                    lineColor: '#2680F7',
                    itemColor: '#2680F7',
                    xAxisLabelCount: 10,
                    xAxisLabelRotate: 0,
                    dataZoomConf: null,
                    legend: {
                        data: ['到院人数', '新客'],
                        bottom: -3,
                        left: '45%',
                    },
                    grid: {
                        show: true,
                        borderWidth: 0,
                        borderColor: '#E6E9EC',
                        containLabel: true,
                        top: '3%',
                        left: '3%',
                        bottom: '16%',
                        right: '4%',
                    },
                },
                dateConfig: {
                    showDate: true,
                    dateText: dateRangeFormat(trendDateTypeRange.begin, trendDateTypeRange.end),
                },
                filterConfig: [
                    {
                        type: 'datePickerBar',
                        valueKey: 'trendDateType',
                        value: this.view.filterParams.trendDateType,
                        width: 160,
                        options: [
                            DatePickerBarOptions.LATEST_WEEK,
                            DatePickerBarOptions.LATEST_MONTH,
                            DatePickerBarOptions.LATEST_THREE_MONTH,
                            DatePickerBarOptions.LATEST_HALF_YEAR,
                            DatePickerBarOptions.MONTHLY,
                        ],
                    },
                ],
            },
        ];
    }

    createFetchParams() {
        const {
            dateFilter: {
                begin, end,
            } = {},
            clinicId,
        } = this.view.filterParams;
        return {
            clinicId,
            beginDate: begin,
            endDate: end,
        };
    }

    async fetchSummaryData() {
        try {
            const {
                beginDate, endDate, clinicId,
            } = this.createFetchParams();
            const { data } = await OperationApi.overview.summary(beginDate, endDate, clinicId);
            this.summaryData = data;
        } catch (e) {
            this.summaryData = {};
        }
    }

    async fetchDepartmentVisitData() {
        try {
            const {
                beginDate, endDate, clinicId,
            } = this.createFetchParams();
            const { data } = await OperationApi.overview.visitDepartment(beginDate, endDate, clinicId);
            this.departmentVisitCategory = data.map((item) => item.name);
            this.departmentVisitData = data.length ? [data.map((item) => item.firstCount ?? 0), data.map((item) => item.followCount ?? 0)] : [];
        } catch (e) {
            this.departmentVisitData = [];
            this.departmentVisitCategory = [];
        }
    }

    async fetchDoctorVisitData() {
        try {
            const {
                beginDate, endDate, clinicId,
            } = this.createFetchParams();
            const { data } = await OperationApi.overview.visitDoctor(beginDate, endDate, clinicId);
            this.doctorVisitCategory = data.map((item) => item.name);
            this.doctorVisitData = data.length ? [data.map((item) => item.firstCount ?? 0), data.map((item) => item.followCount ?? 0)] : [];
        } catch (e) {
            this.doctorVisitData = [];
            this.doctorVisitCategory = [];
        }
    }

    async loadTableData() {
        let newCustomers = {};
        let arriveCustomers = {};
        let newCustomersTrendDates = [];
        try {
            const {
                trendDateType,
                trendDateTypeRange: {
                    begin: beginDate,
                    end: endDate,
                } = {},
                clinicId,
            } = this.view.filterParams;
            const groupBy = trendDateType === DatePickerBarOptions.MONTHLY.label ? 'month' : 'day';
            const { data } = await OperationApi.overview.dailyPatient(beginDate, endDate, groupBy, clinicId);
            newCustomers = data.newCustomers;
            arriveCustomers = data.arriveCustomers;
            newCustomersTrendDates = newCustomers.dates;
        } catch (e) {
            console.log(e);
        } finally {
            this.newCustomersTrendDates = newCustomersTrendDates;
            this.newCustomersTrendData = [
                {
                    name: '新客',
                    type: 'line',
                    data: newCustomers.counts ?? [],
                    itemStyle: {
                        normal: {
                            color: '#55BFC0',
                        },
                    },
                    lineStyle: {
                        normal: {
                            color: '#55BFC0',
                            width: 1,
                        },
                    },
                },
                {
                    name: '到院人数',
                    type: 'line',
                    data: arriveCustomers.counts ?? [],
                },
            ];
        }
    }
}
