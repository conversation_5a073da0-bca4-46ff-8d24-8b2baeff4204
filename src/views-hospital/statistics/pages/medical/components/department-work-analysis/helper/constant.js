import InPatientWordAnalysisReportTable
    from '@/views-hospital/statistics/pages/medical/components/department-work-analysis/components/inpatient';
import WardPatientWordAnalysisReportTable
    from '@/views-hospital/statistics/pages/medical/components/department-work-analysis/components/ward';

export const TABLE_CATEGORY_ENUM = Object.freeze({
    OUTPATIENT: 1,
    INPATIENT: 2,
    WARD: 3,
});

export const tableCategoryLabelEnum = Object.freeze({
    [TABLE_CATEGORY_ENUM.OUTPATIENT]: '门诊',
    [TABLE_CATEGORY_ENUM.INPATIENT]: '住院',
    [TABLE_CATEGORY_ENUM.WARD]: '病区',
});

export const tableCategoryOptions = Object.freeze([
    {
        label: `${tableCategoryLabelEnum[TABLE_CATEGORY_ENUM.OUTPATIENT]}(即将上线)`, value: TABLE_CATEGORY_ENUM.OUTPATIENT, disabled: true,
    },
    {
        label: tableCategoryLabelEnum[TABLE_CATEGORY_ENUM.INPATIENT], value: TABLE_CATEGORY_ENUM.INPATIENT,
    },
    {
        label: tableCategoryLabelEnum[TABLE_CATEGORY_ENUM.WARD], value: TABLE_CATEGORY_ENUM.WARD,
    },
]);

export const tableInstanceConfigEnum = Object.freeze({
    [TABLE_CATEGORY_ENUM.OUTPATIENT]: InPatientWordAnalysisReportTable,
    [TABLE_CATEGORY_ENUM.INPATIENT]: InPatientWordAnalysisReportTable,
    [TABLE_CATEGORY_ENUM.WARD]: WardPatientWordAnalysisReportTable,
});

//报表类table的scope 已和后端确认
export const ReportScopeEnum = Object.freeze({
    OUTPATIENT_HOSPITAL: 0, //门诊+住院
    OUTPATIENT: 1, //门诊
    HOSPITAL: 2, //住院
    OUTPATIENT_HOSPITAL_PE: 3, // 门诊+住院+体检
    PHYSICAL_EXAMINATION: 4, //体检
});

export const REPORT_TABLE_SCOPE = Object.freeze({
    [TABLE_CATEGORY_ENUM.OUTPATIENT]: ReportScopeEnum.OUTPATIENT,
    [TABLE_CATEGORY_ENUM.INPATIENT]: ReportScopeEnum.HOSPITAL,
    [TABLE_CATEGORY_ENUM.WARD]: ReportScopeEnum.HOSPITAL,
});
