import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { isEqual } from 'utils/lodash';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import OperationStatAPI from 'views/statistics/core/api/operation-stat';
import ChronicCareAPI from 'views/statistics/core/api/chronic-care';

export default class ChronicCoreEffectAnalysisStatementReportTable extends StatBaseTable {
    // 由产品.设计提供的静态配置, 开发只能修改key、renderType
    static staticConfig = {
        'hasInnerBorder': true,
        'hasHeaderBorder': true,
        'list': [{
            'label': '患者姓名',
            'key': 'patientName',
            'pinned': false,
            'position': 1,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '性别',
            'key': 'sex',
            'pinned': false,
            'position': 2,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '48px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '年龄',
            'key': 'age',
            'pinned': false,
            'position': 3,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '56px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '手机号',
            'key': 'mobile',
            'pinned': false,
            'position': 4,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '慢病管理项目',
            'key': 'templateName',
            'pinned': false,
            'position': 5,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '建档日期',
            'key': 'created',
            'pinned': false,
            'position': 6,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '最近疗效评估',
            'key': 'lastEvaluation',
            'pinned': false,
            'position': 7,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '疗效评估日期',
            'key': 'lastEvaluationCreated',
            'pinned': false,
            'position': 8,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '最近就诊日期',
            'key': 'lastDiagCreated',
            'pinned': false,
            'position': 9,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '最近就诊医生',
            'key': 'lastDiagCreatedByName',
            'pinned': false,
            'position': 10,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '建档人',
            'key': 'createdByName',
            'pinned': false,
            'position': 12,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        }],
    };

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin','subClinics');
        const { subClinics } = permission;
        return {
            exportTaskType: 'patient-stat',
            tableKey: 'chronic-core-effect-analysis-statement',
            isEnablePagination: true,
            filterTools: [
                {
                    label: '建档日期',
                    type: 'datePicker',
                    valueKey: 'dateFilter',
                    initData: this.view.comDateFilter,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                },
                {
                    type: 'patientSelector',
                    valueKey: 'patientId',
                    placeholder: '搜索患者',
                    clinicId: '',
                    width: 120,
                },
                {
                    type: 'select',
                    valueKey: 'projectId',
                    placeholder: '选择项目',
                    width: 120,
                    options: [],
                },
            ].filter((item) => !item.isHidden),
        };
    }

    createFetchParams() {
        const {
            dateFilter: {
                begin, end,
            } = {},
            projectId,
            patientId,
        } = this.view.filterParams;
        return {
            clinicId: this.queryClinicId(),
            beginDate: begin,
            endDate: end,
            offset: this.view.pageParams.offset,
            limit: this.view.pageParams.limit,
            projectId,
            patientId,
        };
    }

    async loadTableData() {
        const params = this.createFetchParams();
        this.loading = true;
        try {
            const { data } = await OperationStatAPI.chronicCare.effect(params);
            if (isEqual(params, this.createFetchParams())) {
                this.setTableInfo({
                    tableInfo: data,
                });
            }
        } catch (err) {
            console.log(err);
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];
        const {
            renderTypeList,staticConfig,
        } = ChronicCoreEffectAnalysisStatementReportTable;
        this.tableRenderConfig = resolveRenderConfig(
            header,
            renderTypeList,
            staticConfig,
            this.renderTypeMap,
            this.headerRenderTypeMap,
            this.headerAppendRenderTypeMap,
        );
    }
    async export() {
        const {
            clinicId = '', beginDate, endDate, projectId = '', patientId = '', order, sort,
        } = this.createFetchParams();
        const { permission } = usePermission('enablePatientMobile');
        const query = {
            clinicId,
            beginDate,
            endDate,
            projectId,
            patientId,
            order,
            sort,
            enablePatientMobile: permission.enablePatientMobile,
        };
        return ChronicCareAPI.effect.exportList(query);
    }
}
