<template>
    <abc-container has-left-container class="abc-hospital-nurse-prescription-container">
        <abc-container-left>
            <quick-list
                ref="quick-list"
            ></quick-list>
        </abc-container-left>

        <abc-container-center class="content-container">
            <router-view ref="content"></router-view>
        </abc-container-center>
    </abc-container>
</template>

<script>
    import * as core from '@/views-hospital/nurse-prescription/core/index.js';
    import { createAbcPage } from '@/core/page/factory.js';
    // 自定义组件
    import QuickList from './components/quick-list';

    export default {
        name: 'HospitalNursePrescription',
        components: {
            QuickList,
        },
        mixins: [
            createAbcPage(core),
        ],
    };
</script>

<style lang="scss">
    @import './_index.scss';
</style>


