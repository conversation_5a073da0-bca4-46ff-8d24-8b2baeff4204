import Index from '../index.vue';
import { MODULE_ID_MAP } from 'utils/constants.js';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

const Main = () => import('../frames/main.vue');

// 外部跳转都应该用该字段
export const RouterNameKeys = {
    hospitalNursePrescription: '@HospitalNursePrescription',
    hospitalNursePrescriptionMain: '@HospitalNursePrescriptionMain',
};

export default {
    path: 'nurse-prescription',
    name: RouterNameKeys.hospitalNursePrescription,
    component: Index,
    meta: {
        name: '医嘱',
        moduleId: MODULE_ID_MAP.hospitalNursePrescription,
        needAuth: true,
        pageAsyncClass: PageAsync,
    },
    children: [
        {
            path: ':id?', //path: ':id(\\d+)?',
            component: Main,
            name: RouterNameKeys.hospitalNursePrescriptionMain,
            meta: {
                name: '医嘱主页',
                moduleId: MODULE_ID_MAP.hospitalNursePrescription,
                needAuth: true,
            },
        },
    ],
};
