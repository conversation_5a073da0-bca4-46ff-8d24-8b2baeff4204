<template>
    <abc-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="`${title}申请`"
        custom-class="single-examination_medical-prescription--dialog"
        :append-to-body="true"
        content-styles="width: 816px; padding:16px 24px 24px 24px; height: 496px;"
    >
        <div v-abc-loading="loading" class="single-examination_medical-prescription--dialog-body">
            <abc-form ref="form">
                <div class="single-examination_medical-prescription--dialog-body-title">
                    <span class="single-examination_medical-prescription--dialog-body-title-patient">
                        <span class="single-examination_medical-prescription--dialog-body-title-patient-name" :title="patient.name">{{ patient.name }}</span>
                        <span>{{ handleAge(patient.age) }}</span>
                        <span>{{ patient.sex }}</span>
                    </span>
                    <span class="single-examination_medical-prescription--dialog-body-title-other">
                        住院号：{{ no }}
                    </span>
                    <span class="single-examination_medical-prescription--dialog-body-title-other">
                        科室：{{ patientBasicInfo.departmentName }}
                    </span>
                    <span class="single-examination_medical-prescription--dialog-body-title-other">
                        医生：{{ userInfo?.name || patientBasicInfo.doctorName || '' }}
                    </span>
                </div>
                <div class="single-examination_medical-prescription--dialog-body-project-title">
                    {{ title }}项目
                </div>
                <div :title="examinationProject" class="single-examination_medical-prescription--dialog-body-project-info-box">
                    {{ examinationProject }}
                </div>

                <div class="single-examination_medical-prescription--dialog-body-project-title">
                    病历
                </div>
                <div class="single-examination_medical-prescription--dialog-body-project-basic">
                    <div class="medical-record-item">
                        <label>主诉</label>
                        <abc-form-item>
                            <chief-complaint v-model="chiefComplaint" :fixed="fixed"></chief-complaint>
                        </abc-form-item>
                    </div>
                    <div class="medical-record-item">
                        <label>现病史</label>
                        <abc-form-item>
                            <abc-edit-div
                                v-model="presentHistory"
                                :fixed="fixed"
                                spellcheck="false"
                                :maxlength="2000"
                            ></abc-edit-div>
                        </abc-form-item>
                    </div>
                    <div class="medical-record-item">
                        <label>体征</label>
                        <abc-form-item>
                            <physical-examination
                                key="physical-examination"
                                v-model="physicalExamination"
                                :is-childcare="isChildcare"
                                :fixed="fixed"
                                :medical-record-type="mr.type"
                                :type="0"
                            ></physical-examination>
                        </abc-form-item>
                    </div>
                </div>
                <div class="single-examination_medical-prescription--dialog-body-project-title">
                    诊断<abc-button style="margin-left: auto; font-weight: normal;" type="text" @click="handleDiagnosisBtnClick">
                        下诊断
                    </abc-button>
                </div>
                <div class="single-examination_medical-prescription--dialog-body-project-diagnosis">
                    <abc-checkbox-group v-model="diagnosisValue" style="margin-top: 4px;">
                        <abc-checkbox v-for="(item, key) in diagnosisList" :key="key" :label="item.id">
                            {{ item.diseaseName }}
                        </abc-checkbox>
                    </abc-checkbox-group>
                </div>
                <div class="single-examination_medical-prescription--dialog-body-project-title">
                    {{ title }}目的
                </div>
                <div class="single-examination_medical-prescription--dialog-body-project-info">
                    <abc-edit-div
                        v-model="purpose"
                        :fixed="fixed"
                        :responsive="false"
                        spellcheck="false"
                        :maxlength="2000"
                    ></abc-edit-div>
                </div>
            </abc-form>
        </div>
        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleSave">
                确定
            </abc-button>
            <abc-button type="blank" @click="handleCancel">
                取消
            </abc-button>
        </div>
        <template>
            <add-medical-diagnosis-dialog
                v-if="addDiagnosisDialogVisible"
                v-model="addDiagnosisDialogVisible"
                :patient-order-id="patientOrderId"
                :patient="patient"
                :selected-diagnosis="selectedDiagnosis"
                :doctor="doctor"
                :existed-diagnosis-list="diagnosisTableData"
                @refresh="getDiagnosisList"
            ></add-medical-diagnosis-dialog>
        </template>
    </abc-dialog>
</template>

<script>
    import ChiefComplaint from '@/views/outpatient/common/medical-record/chief-complaint';
    import { loosePlainText } from 'utils/xss-filter';
    import PhysicalExamination from 'views/outpatient/common/medical-record/physical-examination';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
    import { AdviceRuleType } from '@/views-hospital/medical-prescription/utils/constants';
    import { formatDate } from '@abc/utils-date';
    import { mapGetters } from 'vuex';
    import { AdviceExaminationExtendSpec } from '@/views-hospital/medical-prescription/utils/constants';
    export default {
        name: 'SingleExaminationMedicalPrescription',
        components: {
            ChiefComplaint,
            PhysicalExamination,
            AddMedicalDiagnosisDialog: () => import('@/views-hospital/medical-prescription/components/add-medial-diagnosis-dialog'),
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            examItem: {
                type: Object,
            },
            advice: {
                type: Object,
            },
            deviceType: {
                type: [String, Number],
                default: '',
            },
            extendSpec: {
                type: [String, Number],
                default: '',
            },
            patient: {
                type: Object,
                required: true,
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            outpatientOrderId: {
                type: String,
                default: '',
            },
            no: {
                type: Number,
                default: 0,
            },
            isChildcare: {
                type: Boolean,
                default: false,
            },
            fixed: {
                type: Boolean,
                default: true,
            },
            examAdviceGroups: {
                type: Array,
            },
            doctor: {
                type: Object,
                default: () => ({}),
            },
            departmentName: {
                type: String,
                default: '',
            },
            curStartTime: String,
            operateDepartmentId: String,
        },
        data() {
            return {
                diagnosisValue: [],
                id: '',
                examinationList: [],
                mr: {
                    chiefComplaint: '',
                    presentHistory: '',
                    physicalExamination: '',
                    purpose: '',
                },
                addDiagnosisDialogVisible: false,
                loading: false,
                diagnosisTableData: [],//患者诊断列表
                selectedDiagnosis: [],
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
            ]),
            title() {
                if (this.isInspect) {
                    return '检查';
                }
                return '检验';
            },
            patientBasicInfo() {
                return {
                    departmentName: this?.departmentName || '',
                    doctorName: this?.doctor?.doctorName || '',
                };
            },
            examinationProject() {
                let examinationListHandle = this.examinationList.map((item) => {
                    return item?.adviceName || '';
                });
                if (examinationListHandle.length) {
                    examinationListHandle = Array.from(new Set(examinationListHandle));
                }
                return examinationListHandle.join(' , ');
            },
            diagnosisList() {
                return this.diagnosisTableData?.map((item) => {
                    return {
                        diseaseCode: item?.diseaseCode || '',
                        diseaseName: item?.diseaseName || '',
                        id: item?.id || '',
                    };
                }) || [];
            },
            // 现病史
            presentHistory: {
                get() {
                    return this.mr.presentHistory || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.presentHistory = val;
                },
            },
            purpose: {
                get() {
                    return this.mr.purpose || '';
                },
                set(val) {
                    this.mr.purpose = val;
                },
            },
            // 体格检查
            physicalExamination: {
                get() {
                    return this.mr.physicalExamination || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.physicalExamination = val;
                },
            },
            // 主诉
            chiefComplaint: {
                get() {
                    return this.mr?.chiefComplaint || '';
                },
                set(val) {
                    val = loosePlainText(val);
                    this.mr.chiefComplaint = val;
                },
            },
            dialogVisible: {
                set(val) {
                    this.$emit('input', val);
                },
                get() {
                    return this.value;
                },
            },
            isInspect() {
                return this.advice.type === AdviceRuleType.INSPECTION;
            },
            examType() {
                return this.isInspect ? 2 : 1;
            },
        },
        async created() {
            this.loading = true;
            await this.getDiagnosisList();
            await this.fetchExamApplyInfoByPatientOrderId();
        },
        methods: {
            async getDiagnosisList() {
                try {
                    const {
                        patientOrderId, outpatientOrderId,
                    } = this;
                    const list = await MedicalPrescriptionAPI.getDiagnosisList({
                        patientOrderId,
                        outpatientOrderId,
                    });
                    if (list?.length) {
                        this.diagnosisTableData = list;
                    }

                } catch (err) {
                    console.log(err);
                }
            },
            handleDiagnosisBtnClick() {
                this.addDiagnosisDialogVisible = true;
            },
            getExamApplySheetReqs() {
                const diagnosisInfos = this.diagnosisList?.filter((item) => {
                    return this.diagnosisValue.includes(item?.id);
                }) || []; // 获取选中的诊断值
                const params = {
                    chiefComplaint: this.mr?.chiefComplaint,
                    deviceType: this.isInspect ? this.deviceType : '', // 检查才传检验不要
                    extendSpec: this.extendSpec,
                    diagnosisInfos: diagnosisInfos.map((item) => {
                        return {
                            diseaseCode: item.diseaseCode,
                            diseaseName: item.diseaseName,
                            id: item.id,
                        };
                    }),
                    id: this.id,
                    physicalExamination: this.mr?.physicalExamination,
                    presentHistory: this.mr?.presentHistory,
                    purpose: this.mr?.purpose,
                    type: this.examType,
                    planExecuteDate: formatDate(this.curStartTime, 'YYYY-MM-DD'),
                };
                return params;
            },
            async fetchExamApplyInfoByPatientOrderId() {
                let examinationList = this.examAdviceGroups?.filter((item) => {
                    const { adviceRule } = item.advices[0];
                    if (this.isInspect && this.extendSpec === AdviceExaminationExtendSpec.DEFAULT) { // 如果是目前类型是检查需要类型相同
                        return item?.deviceType === this?.deviceType && this.extendSpec === item?.extendSpec && adviceRule.type === AdviceRuleType.INSPECTION;
                    }
                    if (this.isInspect && this.extendSpec !== AdviceExaminationExtendSpec.DEFAULT) { // 如果是目前类型是检查需要类型相同
                        return this.extendSpec === item?.extendSpec && adviceRule.type === AdviceRuleType.INSPECTION;
                    }
                    // 如果是检验则合并为一个单子
                    return adviceRule.type === AdviceRuleType.ASSAY;
                }) || [];
                examinationList = examinationList.map((item) => {
                    const { adviceRule } = item.advices[0];
                    return {
                        adviceName: adviceRule.items?.[0].name,
                        adviceId: adviceRule.items?.[0]?.goodsId,
                    };
                });
                const params = {
                    patientOrderId: this.patientOrderId,
                    examDeviceType: this.deviceType,
                    extendSpec: this.extendSpec,
                    examSubType: this.examType,
                    startDate: formatDate(this.curStartTime, 'YYYY-MM-DD'),
                    departmentId: this.operateDepartmentId,
                };
                try {
                    const { data } = await MedicalPrescriptionAPI.fetchExamApplyInfoByPatientOrderId(params);
                    // 优先展示已经选中的内容 然后展示查询到的关联内容
                    this.mr.chiefComplaint = this.examItem?.chiefComplaint || data?.chiefComplaint || '';
                    this.mr.presentHistory = this.examItem?.presentHistory || data?.presentHistory || '';
                    this.mr.physicalExamination = this.examItem?.physicalExamination || data?.physicalExamination || '';
                    this.mr.purpose = this.examItem?.purpose || data?.purpose || '';
                    this.id = data?.id || '';

                    const diagnosisInfos = this.examItem?.diagnosisInfos?.length ? this.examItem.diagnosisInfos : data.diagnosisInfos || [];
                    this.diagnosisValue = diagnosisInfos?.map((item) => {
                        return item?.id;
                    });

                    if (data?.existRelatedAdvices) {
                        data?.existRelatedAdvices.forEach((i) => {
                            if (!examinationList.find((it) => {return it.adviceId === i.adviceId;})) {
                                examinationList.push(i);
                            }
                        });
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.examinationList = examinationList;
                    this.loading = false;
                }

            },
            handleCancel() {
                this.dialogVisible = false;
            },
            handleAge(age) {
                const {
                    year, month, day,
                } = age;
                if (year) {
                    return `${year}岁`;
                }
                if (month) {
                    return `${year}月`;
                }
                return `${day}天`;
            },
            handleSave() {
                this.$emit('createExamApplySheetId',{
                    examApplySheetReqs: this.getExamApplySheetReqs(),
                });
                this.dialogVisible = false;
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.single-examination_medical-prescription--dialog {
    &-body {
        width: 100%;

        &-project-title {
            display: flex;
            align-content: center;
            width: 100%;
            height: 32px;
            padding: 0 12px;
            margin-top: 16px;
            font-size: 14px;
            font-weight: bold;
            line-height: 30px;
            color: $S1;
            background: #f9fafc;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;
        }

        &-project-diagnosis {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            width: 100%;
            min-height: 36px;
            padding: 0 12px;
            line-height: 36px;
            background: $S2;
            border: 1px solid $P1;
            border-top: none !important;
            border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);
        }

        &-project-basic {
            height: auto;
            background-color: #fffdec;
            border-right: 1px solid $P1;
            border-bottom: 1px solid $P1;
            border-left: 1px solid $P1;
            border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);

            .medical-record-item {
                display: flex;
                border-bottom: 1px solid $P6;

                &:last-child {
                    border-bottom: none;
                }

                label:not(.abc-checkbox-wrapper) {
                    width: 98px;
                    line-height: 36px;
                    color: #626d77;
                    text-indent: 12px;
                    border-right: 1px solid $P6;
                }

                .abc-form-item {
                    flex: 1;
                    margin: 0;
                }

                .abc-input__inner {
                    height: auto;
                    min-height: 36px;
                    padding: 7px 10px;
                    font-size: 14px;
                    line-height: 20px;
                    word-break: break-word;
                    border-color: transparent;
                    border-radius: 0;

                    &:focus {
                        position: relative;
                        z-index: 2;
                        border-color: $theme1;
                    }
                }
            }
        }

        &-project-info {
            min-height: 36px;
            padding: 0 0;
            color: $S1;
            background: $S2;
            border: 1px solid $P1;
            border-top: none;
            border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);

            &-box {
                height: 36px;
                padding: 0 12px;
                line-height: 36px;
                color: $S1;
                background: $S2;
                border: 1px solid $P1;
                border-top: none;
                border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);

                @include ellipsis;
            }

            .abc-form-item {
                flex: 1;
                margin: 0;
            }

            .abc-input__inner {
                height: auto;
                min-height: 36px;
                padding: 7px 10px;
                font-size: 14px;
                line-height: 20px;
                word-break: break-word;
                border-color: transparent;
                border-radius: 0;

                &:focus {
                    position: relative;
                    z-index: 2;
                    border-color: $theme1;
                }
            }
        }

        &-title {
            display: flex;
            align-content: center;
            height: 20px;
            font-size: 14px;
            font-weight: bold;
            line-height: 20px;
            letter-spacing: 0;

            &-patient {
                display: inline-flex !important;
                flex-wrap: nowrap;
                justify-content: space-between;
                min-width: 106px;
                max-width: 126px;
                color: $S1;

                &-name {
                    max-width: 67px;

                    @include ellipsis;
                }

                span {
                    display: inline-block;
                    margin-right: auto !important;
                }
            }

            &-other {
                font-weight: 400 !important;
                color: $T2;
            }

            span {
                display: inline-block;
                margin-right: 32px;
            }
        }
    }
}
</style>
