import { maritalOptions } from 'views/crm/data/options.js';
import {
    formatEpidemiologicalHistory2Str,
    formatOralExamination2Html,
} from 'views/outpatient/common/medical-record/utils';
import { formatAge } from 'utils';

export default class RefKeyService {
    data = {};

    transformRules = {
        'patient.name': (data) => {
            return data.patient?.name;
        },
        'patient.sex': (data) => {
            return data.patient?.sex;
        },
        'patient.age.year': (data) => {
            return formatAge(data.patient?.age, {
                monthYear: 12,
                dayYear: 1,
            });
        },
        'patient.mobile': (data) => {
            return data.patient?.mobile;
        },
        'patient.birthday': (data) => {
            return data.patient?.birthday;
        },
        'patient.idCard': (data) => {
            return data.patient?.idCard;
        },
        'patient.shebaoCardInfo.cardNo': (data) => {
            return data.patient?.shebaoCardInfo?.cardNo;
        },
        'patient.sn': (data) => {
            return data.patient?.sn;
        },
        'patient.marital': (data) => {
            return maritalOptions.find((item) => item.value === data.patient?.marital)?.label;
        },
        'patient.profession': (data) => {
            return data.patient?.profession;
        },
        'patient.address.addressDetail': (data) => {
            return data.patient?.address?.addressDetail;
        },
        'patient.company': (data) => {
            return data.patient?.company;
        },
        'patient.ethnicity': (data) => {
            return data.patient?.ethnicity;
        },
        'patient.pastHistory': (data) => {
            return data.medicalRecord?.pastHistory;
        },
        'medical.chiefComplaint': (data) => {
            return data.medicalRecord?.chiefComplaint;
        },
        'medical.presentHistory': (data) => {
            return data.medicalRecord?.presentHistory;
        },
        'medical.familyHistory': (data) => {
            return data.medicalRecord?.familyHistory;
        },
        'medical.personalHistory': (data) => {
            return data.medicalRecord?.personalHistory;
        },
        'medical.epidemiologicalHistory': (data) => {
            return formatEpidemiologicalHistory2Str(data.medicalRecord?.epidemiologicalHistory);
        },
        'medical.physicalExamination': (data) => {
            return data.medicalRecord?.physicalExamination;
        },
        'medical.chineseExamination': (data) => {
            return data.medicalRecord?.chineseExamination;
        },
        'medical.oralExamination': (data) => {
            return formatOralExamination2Html(data.medicalRecord?.oralExamination, true);
        },
        'medical.therapy': (data) => {
            return data.medicalRecord?.therapy;
        },
        'medical.algsHis': (data) => {
            return data.medicalRecord?.allergicHistory;
        },
        'medical.syndromeTreatment': (data) => {
            return data.medicalRecord?.syndromeTreatment;
        },
    };

    constructor(data) {
        this.data = data;
    }

    replaceLineBreak(val) {
        /**
         * 替换换行符
         */
        if (val && typeof val === 'string') {
            return val.replace(/<br\s*?\/?\s*>/g, '\n');
        }
        return val;
    }

    async transform(keys) {
        const refDatas = keys.reduce((total, refKey) => {
            const transformRule = this.transformRules[refKey];
            if (!transformRule) {
                console.error(`RefKeyService: ${refKey} is not supported`);
                return total;
            }
            try {
                total[refKey] = this.replaceLineBreak(transformRule(this.data));
            } catch (e) {
                console.error(`RefKeyService: ${refKey} transform failed`, e);
            }
            return total;
        }, {});
        return {
            data: {
                refDatas,
            },
        };
    }
}
