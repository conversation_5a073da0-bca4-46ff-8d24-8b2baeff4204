<template>
    <div>
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            append-to-body
            custom-class="hospital-local-decoction-dialog-wrapper"
            content-styles="width: 400px"
            :auto-focus="false"
            title="代加工信息"
        >
            <div v-abc-loading="loading" class="dialog-content clearfix">
                <abc-form ref="decoctionForm">
                    <div v-if="usageOptions && usageOptions.length" class="decoction-item-wrapper">
                        <div class="decoction-item">
                            <label class="decoction-title">制法</label>
                            <abc-form-item>
                                <abc-cascader
                                    v-model="processUsage"
                                    :clearable="false"
                                    :options="usageOptions"
                                    :width="292"
                                    :props="{
                                        label: 'name',
                                        value: 'id',
                                        children: 'children'
                                    }"
                                    :value-props="{
                                        _label: 'name',
                                        _value: 'id'
                                    }"
                                    :only-show-leaf-label="true"
                                    @change="handleChangeUsage"
                                >
                                </abc-cascader>
                            </abc-form-item>
                        </div>
                    </div>
                    <div v-if="currentUsageType === 1" class="decoction-item-wrapper">
                        <div class="decoction-item">
                            <label class="decoction-title">袋数</label>
                            <div class="decoction-item">
                                <label>1剂煎</label>
                                <abc-form-item required>
                                    <abc-input
                                        v-model="currentProcessBagUnitCount"
                                        type="number"
                                        :config="{
                                            max: 1000,
                                            formatLength: 1,
                                        }"
                                        :input-custom-style="{
                                            'text-align': 'center', margin: '0 10px'
                                        }"
                                        :width="64"
                                        @change="onChangeBagUnit"
                                    ></abc-input>
                                </abc-form-item>
                                <template>
                                    <label>袋，</label>
                                    <label>共煎</label>
                                    <abc-form-item required>
                                        <abc-input
                                            v-model="currentTotalProcessCount"
                                            type="number"
                                            :config="{
                                                max: 1000,
                                            }"
                                            :input-custom-style="{
                                                'text-align': 'center', margin: '0 10px'
                                            }"
                                            :width="64"
                                            @change="onChangeProcessCount"
                                        ></abc-input>
                                    </abc-form-item>
                                    <label>袋</label>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="decoction-item-wrapper">
                        <div class="decoction-item">
                            <label class="decoction-title">加工费</label>
                            <abc-form-item>
                                <div class="total-fee">
                                    <span>{{ $t('currencySymbol') }} </span>
                                    {{ processTotalFee | formatMoney }}
                                </div>
                            </abc-form-item>
                        </div>
                    </div>
                    <div class="decoction-item-wrapper">
                        <div class="decoction-item">
                            <label class="decoction-title">备注</label>
                            <abc-form-item>
                                <abc-input
                                    v-model="currentProcessRemark"
                                    :width="292"
                                    :max-length="100"
                                    placeholder="备注"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </div>
                </abc-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <abc-button type="primary" @click="confirmDecoction">
                    确定
                </abc-button>
                <abc-button type="blank" :plain="true" @click="cancel">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import ChargeAPI from 'api/charge.js';
    import { isNumber } from 'utils/index.js';

    export default {
        name: 'PrescriptionProcessDialog',
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            processRemark: {
                type: [String, Number],
                default: '',
            },
            feeTypeId: {
                type: [Number, String],
                default: '',
            },
            feeCategoryId: {
                default: '',
                validator: (prop) => typeof prop === 'string' || typeof prop === 'number' || prop === null,

            },
            usageType: {
                type: [Number, String],
                default: '',
            },
            usageSubType: {
                type: [Number, String],
                default: '',
            },
            processBagUnitCount: {
                type: [Number, String],
                default: '',
            },
            totalProcessCount: {
                type: [Number, String],
                default: '',
            },
            processPrice: {
                type: [Number, String],
                default: '',
            },
            dosageCount: {
                type: [Number, String],
                default: '',
            },
            groupItem: {
                type: Object,
                required: true,
            },
            adviceItem: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                currentProcessRemark: this.processRemark,
                currentFeeTypeId: this.feeTypeId,
                currentFeeCategoryId: this.feeCategoryId,
                currentUsageType: this.usageType,
                currentUsageSubType: this.usageSubType,
                currentProcessBagUnitCount: this.processBagUnitCount,
                currentTotalProcessCount: this.totalProcessCount,
                currentProcessPrice: this.processPrice,
                processUsage: [],
                loading: false,
            };
        },
        computed: {
            ...mapGetters(['availableProcessUsages']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            usageOptions() {
                return this.availableProcessUsages || [];
            },
            processTotalFee() {
                return this.currentProcessPrice;
            },
            displayName() {
                const nameArr = this.processUsage.map((item) => {
                    return item.name;
                });
                return nameArr.join('-');
            },
        },
        created() {
            this.initProcessUsage();
        },
        mounted() {
            this.onCalcFee();
        },
        methods: {
            initProcessUsage() {
                if (!this.usageOptions || this.usageOptions.length === 0) return;

                const usageTypeInfo = this.usageOptions.find((item) => {
                    return item.type === this.usageType;
                });
                if (!usageTypeInfo) {
                    // 默认选中第一个
                    const usageOption = this.usageOptions[0];
                    this.processUsage.push(usageOption);
                    if (usageOption.children && usageOption.children.length) {
                        this.processUsage.push(usageOption.children[0]);
                    }
                    this.handleChangeUsage(this.processUsage);
                    return;
                }

                this.processUsage.push(usageTypeInfo);
                const { children } = usageTypeInfo;
                if (!children || children.length === 0) return;

                let usageSubTypeInfo = children.find((item) => {
                    return item.subType === this.usageSubType;
                });

                if (!usageSubTypeInfo) {
                    usageSubTypeInfo = usageTypeInfo.children[0];
                }
                this.processUsage.push(usageSubTypeInfo);
            },

            handleChangeUsage(val) {
                if (!Array.isArray(val)) return;
                const [usageTypeObj, usageSubTypeObj] = val;

                const usageTypeInfo = this.usageOptions.find((item) => {
                    return item.id === usageTypeObj.id;
                });
                if (!usageTypeInfo) return;
                this.currentUsageType = usageTypeInfo.type;
                this.currentFeeTypeId = usageTypeInfo.feeTypeId;
                this.currentFeeCategoryId = usageTypeInfo.feeCategoryId;
                this.currentUsageSubType = 0;

                const { children } = usageTypeInfo;
                if (usageSubTypeObj && children && children.length > 0) {
                    const usageSubTypeInfo = children.find((item) => {
                        return item.id === usageSubTypeObj.id;
                    });
                    if (usageSubTypeInfo) {
                        this.currentUsageSubType = usageSubTypeInfo.subType;
                        this.currentFeeTypeId = usageSubTypeInfo.feeTypeId;
                        this.currentFeeCategoryId = usageSubTypeInfo.feeCategoryId;
                    }
                }
                this.onCalcFee();
            },

            confirmDecoction() {
                this.$refs.decoctionForm.validate((val) => {
                    if (val) {
                        const data = {
                            feeTypeId: this.currentFeeTypeId,
                            feeCategoryId: this.currentFeeCategoryId,
                            usageType: this.currentUsageType,
                            usageSubType: this.currentUsageSubType,
                            processRemark: this.currentProcessRemark,
                            processBagUnitCount: this.currentProcessBagUnitCount,
                            totalProcessCount: this.currentTotalProcessCount,
                            price: this.processTotalFee,
                            displayName: this.displayName,
                        };

                        this.$emit('confirm', data);
                        this.showDialog = false;
                    }
                });
            },
            cancel() {
                this.showDialog = false;
            },

            onChangeBagUnit(val) {
                if (!isNumber(val)) return;
                if (Number(val) === 0) return;
                if (!this.dosageCount) return;
                this.$nextTick(() => {
                    this.currentTotalProcessCount = Math.ceil(val * this.dosageCount);
                    this.onCalcFee();
                });
            },

            onChangeProcessCount(val) {
                if (!isNumber(val)) return;
                if (Number(val) === 0) return;
                if (!this.dosageCount) return;
                this.$nextTick(() => {
                    this.currentProcessBagUnitCount = String(val / this.dosageCount).length > 3 ?
                        (val / this.dosageCount).toFixed(1) : val / this.dosageCount;

                    this.onCalcFee();
                });
            },
            /**
             * @desc 计算加工费
             * <AUTHOR>
             * @date 2023-03-07 09:55:30
             */
            onCalcFee() {
                const data = {
                    feeTypeId: this.currentFeeTypeId,
                    usageType: this.currentUsageType,
                    usageSubType: this.currentUsageSubType,
                    processRemark: this.currentProcessRemark,
                    processBagUnitCount: this.currentProcessBagUnitCount,
                    totalProcessCount: this.currentTotalProcessCount,
                };
                this.formCalcFee(data);
            },
            async formCalcFee(currentData) {
                const postData = {
                    doseCount: this.dosageCount,
                    items: this.adviceItem?.items.filter((item) => {
                        return item.medicineCadn && item.unitCount;
                    }).map((item) => {
                        return {
                            name: item.medicineCadn,
                            productId: item.goodsId,
                            productSubType: item.subType,
                            productType: item.type,
                            unit: item.unit,
                            unitCount: item.unitCount,
                        };
                    }) || [],
                    keyId: this.groupItem.keyId,
                    processInfo: {
                        processBagUnitCount: currentData.processBagUnitCount,
                        subType: currentData.usageSubType,
                        type: currentData.usageType,
                        feeTypeId: currentData.currentFeeTypeId,
                    },
                };
                const res = await ChargeAPI.localPharmacyCalcProcessFee({
                    forms: [postData],
                });
                if (!res) return;
                const { data } = res;
                const { forms } = data || {};
                if (!forms) return;
                const form = forms[0];
                this.currentProcessPrice = form.processPrice || '';
            },
        },
    };
</script>
<style lang="scss">
    @import "src/styles/theme.scss";

    .hospital-local-decoction-dialog-wrapper {
        .decoction-item-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .decoction-item {
                display: flex;
                align-items: center;

                > label {
                    height: 32px;
                    font-size: 14px;
                    line-height: 32px;
                }

                .decoction-title {
                    width: 60px;
                    margin-right: 0;
                    margin-left: 0;
                    color: var(--abc-color-T2);
                }

                .total-fee {
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                }

                .abc-form-item {
                    margin-right: 0;
                    margin-bottom: 0;
                }
            }

            & + .decoction-item-wrapper {
                margin-top: 24px;
            }
        }
    }
</style>
