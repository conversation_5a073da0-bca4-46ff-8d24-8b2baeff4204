<template>
    <div class="dosage-input-select-wrapper" :class="{ 'is-disabled': disabled }">
        <abc-form-item
            :required="required"
            style="margin-right: -1px;"
            :validate-event="validateNumber"
        >
            <div
                v-if="isFraction && !isEditing"
                tabindex="0"
                class="fraction-display abc-input__inner"
                @click="editFractionDosage"
                @focus="editFractionDosage"
                v-html="fractionDosageHtml"
            ></div>
            <abc-input
                v-else
                ref="dosageInput"
                v-model="curSingleDosageCount"
                v-abc-focus-selected
                :disabled="disabled"
                :readonly="readonly"
                type="number"
                class="count-center"
                :placeholder="placeholder"
                :width="inputWidth"
                :input-custom-style="readonly ? {
                    padding: 0, border: 0
                } : {}"
                :config="{
                    max: 999999,
                    formatLength: 4,
                    supportFraction: true
                }"
                @focus="isEditing = true"
                @blur="handleBlur"
                @enter="enterEvent"
                @change="$emit('change')"
            >
            </abc-input>
        </abc-form-item>

        <abc-form-item :required="required">
            <!--诊所药品：只显示剂量单位（ml）和制剂单位（片），默认为剂量单位，
                其次为制剂单位，如果剂量单位为空，则只显示制剂单位-->
            <abc-select
                v-if="hasDosageUnit"
                v-model="curSingleDosageUnit"
                :width="unitWidth"
                custom-class="prescription-select-options"
                :disabled="disabled"
                :tabindex="unitTabindex"
                focus-show-options
                @enter="enterEvent"
                @change="selectDosageUnit"
            >
                <abc-option
                    v-for="it in dosageUnitArray(currentMedicine)"
                    :key="it.name"
                    :label="it.name"
                    :value="it.name"
                >
                </abc-option>
            </abc-select>

            <!--系统药品：显示domain表的所有单位-->
            <select-usage
                v-else
                v-model="curSingleDosageUnit"
                v-abc-focus-selected
                :type="!isTreatmentType ? 'allDosageUnit' : ''"
                :options="!isTreatmentType ? [] : treatmentUnitList"
                focus-show-options
                :disabled="disabled"
                :tabindex="unitTabindex"
                :width="unitWidth"
                @enter="enterEvent"
                @change="selectDosageUnit"
            >
            </select-usage>
        </abc-form-item>
    </div>
</template>

<script type="text/ecmascript-6">
    import {
        validateStock, validateNumber,
    } from 'utils/validate';
    import SelectUsage from '@/views/layout/select-group/index.vue';
    import { unique } from 'utils/lodash';
    import { getSingleDosageUnitType } from '@/views-hospital/medical-prescription/utils/format-advice.js';
    import { mapGetters } from 'vuex';

    export default {
        name: 'SingleDosage',
        components: {
            SelectUsage,
        },
        props: {
            medicine: Object,
            disabled: Boolean,
            placeholder: String,
            singleDosageUnit: String,
            singleDosageCount: [String, Number],
            singleDosageUnitType: [Number],
            unitTabindex: {
                type: Number,
                default: 1,
            },
            inputWidth: {
                type: Number,
                default: 36,
            },
            unitWidth: {
                type: Number,
                default: 40,
            },
            required: {
                type: Boolean,
                default: true,
            },
            isTreatmentType: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                validateNumber,
                validateStock,
                isEditing: false,
                isFraction: false,
            };
        },
        computed: {
            ...mapGetters([
                'treatmentConfig',
            ]),
            treatmentUnitList() {
                return this.treatmentConfig.unit || [];
            },
            currentMedicine: {
                get() {
                    return this.medicine;
                },
                set(val) {
                    this.$emit('update:medicine', val);
                },
            },
            curSingleDosageCount: {
                get() {
                    return this.singleDosageCount;
                },
                set(v) {
                    this.$emit('update:singleDosageCount', v);
                },
            },
            curSingleDosageUnit: {
                get() {
                    return this.singleDosageUnit;
                },
                set(v) {
                    this.$emit('update:singleDosageUnit', v);
                },
            },
            curSingleDosageUnitType: {
                get() {
                    return this.singleDosageUnitType;
                },
                set(v) {
                    this.$emit('update:singleDosageUnitType', v);
                },
            },
            readonly() {
                return this.curSingleDosageUnit === '适量';
            },
            fractionDosageHtml() {
                // 处理 1 1/3 这种形式
                const _dosageArr = this.curSingleDosageCount.split(' ');
                let _arr = [];
                let intStr = '';
                if (_dosageArr.length === 2) {
                    intStr = _dosageArr[0];
                    _arr = _dosageArr[1].split('/');
                } else {
                    _arr = _dosageArr[0].split('/');
                }
                const numerator = _arr[0];
                const denominator = _arr[1];
                return `${intStr}<sup class="numerator">${numerator}</sup>&frasl;<sub class="denominator">${denominator}</sub>`;
            },

            hasDosageUnit() {
                const {
                    goodsId,
                    componentContentUnit,
                    medicineDosageUnit,
                    pieceUnit,
                } = this.currentMedicine;
                if (!goodsId) return false;
                return componentContentUnit || medicineDosageUnit || pieceUnit;
            },
        },
        watch: {
            'curSingleDosageCount': function() {
                this.changeDosage();
                this.getIsFraction();
            },
            curSingleDosageUnit(val, oldVal) {
                this.$emit('single-unit-change', oldVal);
            },
        },
        created() {
            this._filterUsage = [
                '外用',
                '滴入',
                '滴眼',
                '滴鼻',
                '滴耳',
                '喷入',
                '口腔喷入',
                '鼻腔喷入',
                '直肠给药',
                '含漱',
                '涂抹',
                '塞肛用',
                '阴道用' ];
            this.getIsFraction();
        },
        methods: {
            editFractionDosage() {
                if (this.disabled || this.readonly) return false;
                this.isFraction = false;
                this.isEditing = true;
                this.$nextTick(() => {
                    this.$refs.dosageInput.$el.querySelector('input').focus();
                });
            },

            handleBlur() {
                this.isEditing = false;
                this.getIsFraction();
            },

            getIsFraction() {
                const fractionRegExp = new RegExp(/^([1-9]\s)?[1-9][/][1-9]$/);
                // 是分数 1 1/3 这种形式
                if (fractionRegExp.test(this.curSingleDosageCount)) {
                    this.isFraction = true;
                    // 是用户change触发的才触发回车事件跳到下一个输入框
                    if (this.isEditing && this.$refs.dosageInput) {
                        const $input = this.$refs.dosageInput.$el.querySelector('input');
                        this.enterEvent({ target: $input });
                    }
                    this.isEditing = false;
                }
            },

            isSmallUnit(unit) {
                const unitList = ['g', 'mg', 'ml', 'IU'];
                return unit === '' || unit === undefined || unit === null || unitList.indexOf(unit) > -1;
            },

            /**
             * @desc 成份含量单位 剂量单位（ml）和制剂单位（片）特殊用法需要展示 适量
             * <AUTHOR>
             * @date 2018/04/13 10:33:47
             */
            dosageUnitArray(wm) {
                const {
                    componentContentUnit,
                    medicineDosageUnit,
                    pieceUnit,
                    packageUnit,
                } = wm;
                let res = [componentContentUnit,medicineDosageUnit,pieceUnit];
                if (this.isSmallUnit(componentContentUnit) && this.isSmallUnit(medicineDosageUnit) && this.isSmallUnit(pieceUnit)) {
                    res.push(packageUnit);
                }
                res = unique(res.filter((it) => it));
                res = res.map((it) => {
                    return {
                        name: it,
                    };
                });

                if (this._filterUsage.indexOf(wm.usage) > -1) {
                    res.push({ name: '适量' });
                }
                return res;
            },

            enterEvent(e) {
                this.$emit('enter', e);
            },

            /**
             * @desc 改变西药dosage
             * <AUTHOR>
             * @date 2018/04/13 12:39:59
             */
            changeDosage() {
                this.$emit('calCount', this.currentMedicine);
            },

            /**
             * @desc 选择西药计量单位 如果选择"适量"需要清除dosage
             * <AUTHOR>
             * @date 2018/07/10 15:33:10
             */
            selectDosageUnit(dosageUnit) {
                this.curSingleDosageUnit = dosageUnit;
                this.curSingleDosageUnitType = getSingleDosageUnitType(dosageUnit, this.medicine);
                if (dosageUnit === '适量') {
                    this.curSingleDosageCount = '';
                } else {
                    this.$emit('calCount', this.currentMedicine);
                }
                this.$emit('change');
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.dosage-input-select-wrapper {
    display: flex;
    align-items: center;

    &:not(.is-disabled):hover .abc-form-item {
        &:first-child .abc-input__inner {
            z-index: 1;
        }

        &:last-child .abc-input__inner {
            background-color: #e9f2fe;
        }

        .abc-input__inner {
            border-color: $theme3;
        }
    }

    .fraction-display {
        display: flex;
        align-items: center;
        justify-content: center;

        .numerator {
            top: -4px;
            margin-left: 2px;
        }

        .denominator {
            bottom: -4px;
        }
    }

    .abc-form-item {
        .abc-select-wrapper .abc-input__inner {
            padding: 0 6px !important;
        }

        &:first-child {
            .abc-input__inner {
                padding: 3px 5px 3px 3px;
                font-size: 14px;
                text-align: right;

                &::placeholder {
                    font-size: 12px;
                }
            }
        }

        &:last-child {
            .abc-input__inner {
                padding-right: 0;
                padding-left: 8px;
                text-align: left;
            }

            .iconfont {
                display: none;
            }
        }

        &:focus,
        &:active {
            z-index: 2;
        }

        &.is-error {
            .count-center .abc-input__inner {
                border-color: $Y2;
            }
        }
    }
}
</style>
