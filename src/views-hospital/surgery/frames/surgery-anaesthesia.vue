<template>
    <div v-abc-loading:page="loading || isFirstLoading" class="surgery-anaesthesia-wrapper">
        <abc-flex
            vertical
            class="content-wrapper"
            gap="large"
            align="center"
            :justify="isEmpty ? 'center' : 'normal'"
            :style="isEmpty ? { 'min-height': '554px' } : {}"
        >
            <abc-content-empty v-if="isEmpty" value="暂无数据"></abc-content-empty>

            <template v-else>
                <div style="width: 100%;">
                    <abc-tabs
                        v-model="currentTab"
                        :option="tabOptions"
                        type="border-card"
                        style="border-top: none; border-right: none; border-left: none;"
                        @change="handleChangeSurgeryApply"
                    ></abc-tabs>
                </div>

                <abc-flex vertical gap="large" class="surgery-content-wrapper">
                    <abc-flex align="center" justify="space-between">
                        <abc-flex align="center">
                            <span class="advice-title">医嘱：</span>
                            <span class="surgery-title">{{ currentSurgeryApply.name }}</span>
                            <abc-tag-v2
                                v-if="currentSurgeryApply.status === SurgeryApplyStatus.Revocation"
                                theme="danger"
                                size="small"
                                style="margin-left: 6px;"
                            >
                                已撤销
                            </abc-tag-v2>
                        </abc-flex>

                        <abc-space v-if="surgeryApplyList.length && currentSurgeryApply.status === SurgeryApplyStatus.Normal">
                            <template v-if="isEdit">
                                <abc-button variant="fill" @click="handleSubmit">
                                    保存
                                </abc-button>
                                <abc-button variant="ghost" @click="handleCancel">
                                    取消
                                </abc-button>
                            </template>

                            <template v-else>
                                <abc-button variant="fill" @click="handleEdit">
                                    修改
                                </abc-button>
                                <!-- 一期不上[取消手术]功能 -->
                                <!--<abc-button variant="ghost" @click="handleCancelSurgery">-->
                                <!--    取消手术-->
                                <!--</abc-button>-->
                            </template>
                        </abc-space>
                    </abc-flex>

                    <abc-form ref="surgery-apply-form-ref" item-no-margin is-excel>
                        <surgery-apply-form
                            v-if="!isFirstLoading"
                            key="truly-form"
                            ref="surgery-apply-form"
                            :surgery-req.sync="currentSurgeryApply"
                            :is-add-new="false"
                            :departments="departments"
                            :patient-order-id="patientOrderId"
                            :is-edit="isEdit"
                            :inpatient-time="inpatientTime"
                        ></surgery-apply-form>

                        <surgery-apply-form
                            v-else
                            key="fake-form"
                            ref="surgery-apply-form"
                            :surgery-req.sync="initSurgeryReq"
                            :is-add-new="false"
                            :departments="departments"
                            :patient-order-id="patientOrderId"
                            :inpatient-time="inpatientTime"
                        ></surgery-apply-form>
                    </abc-form>
                </abc-flex>
            </template>
        </abc-flex>
    </div>
</template>

<script>
    import SurgeryApi from 'api/hospital/surgery';
    import {
        createGUID,
    } from '@/utils';
    import SurgeryApplyForm
        from '@/views-hospital/medical-prescription/components/surgery/components/surgery-apply-form.vue';
    import SettingAPI from 'api/settings';
    import clone from 'utils/clone';
    import {
        getInitSurgeryReq,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import { SurgeryApplyStatus } from '@/views-hospital/surgery/constants';

    export default {
        name: 'SurgeryAnaesthesia',
        components: { SurgeryApplyForm },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            patientOrderId: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                SurgeryApplyStatus,
                initSurgeryReq: getInitSurgeryReq(),
                currentTab: '',
                currentSurgeryApply: {}, // 选中的手术申请单
                cacheSurgeryApply: {}, // 暂存
                surgeryApplyList: [], // 已开出的手术申请单列表
                departments: [], // 科室列表
                isFirstLoading: true,
                loading: false,
                isEdit: false,
            };
        },
        computed: {
            selectedQuickItem() {
                return this.$abcPage.$store.selectedQuickItem;
            },
            tabOptions() {
                return this.surgeryApplyList.map((it) => ({
                    label: it.name,
                    value: it.id,
                }));
            },
            isEmpty() {
                return !this.surgeryApplyList || !this.surgeryApplyList.length;
            },
            /**
             * 入院时间
             * @return {string}
             */
            inpatientTime() {
                return this.selectedQuickItem?.inpatientTime;
            },
        },
        watch: {
            patientOrderId() {
                this.fetchSurgeryApplyList();
            },
        },
        async created() {
            this.isFirstLoading = true;
            await this.fetchSurgeryApplyList();
            this.isFirstLoading = false;

            this.fetchDepartments();
        },
        methods: {
            async fetchDepartments() {
                try {
                    const res = await SettingAPI.clinic.fetchClinicDepartments();
                    const { data } = res.data;
                    this.departments = data?.rows || [];
                } catch (e) {
                    console.log('科室信息获取错误', e);
                }
            },
            async fetchSurgeryApplyList(id) {
                try {
                    this.loading = true;
                    this.isEdit = false;
                    const { data } = await SurgeryApi.fetchSurgeryApplyList(this.patientOrderId);
                    const existApplyList = data.rows || [];
                    // 已存在的手术单创建临时 sourceId
                    existApplyList.forEach((it) => {
                        it.sourceId = createGUID();
                    });
                    this.surgeryApplyList = existApplyList;

                    if (existApplyList && existApplyList.length) {
                        if (id) {
                            const cacheSurgeryApply = existApplyList.find((it) => it.id === id);
                            if (cacheSurgeryApply) {
                                this.currentSurgeryApply = clone(cacheSurgeryApply);
                                this.currentTab = id;
                                return;
                            }
                        }
                        this.currentSurgeryApply = clone(existApplyList[0] || {});
                        this.currentTab = existApplyList[0].id;
                    } else {
                        this.currentSurgeryApply = getInitSurgeryReq();
                    }
                } catch (e) {
                    console.error(e);
                    this.currentSurgeryApply = getInitSurgeryReq();
                } finally {
                    this.loading = false;
                }
            },
            handleChangeSurgeryApply(selectedSurgeryApplyId) {
                this.isEdit = false;
                this.currentSurgeryApply = clone(this.surgeryApplyList.find((it) => it.id === selectedSurgeryApplyId) || {});
                this.currentTab = selectedSurgeryApplyId;

                this.$nextTick(() => {
                    this.$refs['surgery-apply-form'].handeChangeDepartment(this.currentSurgeryApply.surgeryDepartmentId);
                });
            },
            handleEdit() {
                this.cacheSurgeryApply = clone(this.currentSurgeryApply);
                this.isEdit = true;
            },
            handleCancel() {
                this.currentSurgeryApply = clone(this.cacheSurgeryApply);
                this.isEdit = false;
            },
            handleSubmit() {
                this.$refs['surgery-apply-form-ref'].validate(async (valid) => {
                    if (valid) {
                        try {
                            this.loading = true;
                            await SurgeryApi.updateSurgeryApply(this.currentSurgeryApply.id, clone(this.currentSurgeryApply));
                            this.fetchSurgeryApplyList(this.currentSurgeryApply.id);
                            this.$Toast.success('修改成功');
                        } catch (e) {
                            console.error(e);
                            this.currentSurgeryApply = clone(this.cacheSurgeryApply);
                        } finally {
                            this.isEdit = false;
                            this.loading = false;
                        }
                    }
                });
            },
            async handleCancelSurgery() {
                try {
                    this.loading = true;
                    await SurgeryApi.updateSurgeryApplyStatus(this.currentSurgeryApply.id, {
                        reason: '', status: 99,
                    });
                    this.fetchSurgeryApplyList(this.currentSurgeryApply.id);
                    this.$Toast.success('取消成功');
                } catch (e) {
                    console.error(e);
                    this.loading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
.surgery-anaesthesia-wrapper {
    .content-wrapper {
        padding-bottom: 24px;
        background-color: var(--abc-color-cp-white);
        border: 1px solid var(--abc-color-P7);
        border-radius: var(--abc-border-radius-small);
    }

    .advice-title {
        font-size: 14px;
        line-height: 22px;
        color: var(--abc-color-T2);
    }

    .surgery-title {
        font-size: 14px;
        line-height: 22px;
        color: var(--abc-color-T1);
    }

    .surgery-content-wrapper {
        width: 100%;
        padding: 0 24px;
    }
}
</style>
