<template>
    <abc-popover
        popper-class="bed-transfer-record-popover"
        width="268px"
        :placement="placement"
        trigger="hover"
        :open-delay="200"
        theme="yellow"
        :z-index="zIndex"
    >
        <div slot="reference">
            <slot name="default"></slot>
        </div>
        <div class="bed-transfer-record-content">
            <div v-for="item in bedTransferRecordList" :key="item.id" class="item">
                <div class="content time">
                    {{ item.created | parseTime('y-m-d h:i') }}
                </div>
                <div class="content action-name">
                    {{ HospitalActionName[item.action] }}
                </div>
                <div class="content name ellipsis">
                    {{ item.operatorName }}
                </div>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import { HospitalActionName } from '@/views-hospital/register/utils/constants';

    export default {
        name: 'InpatientHistoryPopper',
        props: {
            placement: {
                type: String,
                default: 'top-end',
            },
            bedTransferRecordList: {
                type: Array,
                default: () => [],
            },
            zIndex: {
                type: [String,Number],
                default: 1992,
            },
        },
        data() {
            return {
                HospitalActionName,
            };
        },
    };
</script>
<style lang="scss">
@import 'src/styles/theme';

.bed-transfer-record-popover {
    padding: 0;

    .bed-transfer-record-content {
        max-height: 200px;
        overflow-y: auto;
        overflow-y: overlay;
        font-size: 12px;

        .item {
            display: flex;
            padding: 8px 10px;
            line-height: 16px;

            & + .item {
                border-top: 1px solid $P4;
            }

            .content {
                & + .content {
                    margin-left: 12px;
                }
            }

            .time {
                width: 98px;
            }

            .action-name {
                width: 72px;
            }

            .name {
                flex: 1;
            }
        }
    }
}
</style>
