/**
 * 日期范围工具类
 */

export class DateRangeUtil {
    static getCurrentWeek() {
        //获取当前时间
        const currentDate = new Date();
        //返回date是一周中的某一天
        const week = currentDate.getDay();


        //一天的毫秒数
        const millisecond = 1000 * 60 * 60 * 24;
        //减去的天数
        const minusDay = week !== 0 ? week - 1 : 6;
        //alert(minusDay);
        //本周 周一
        const monday = new Date(currentDate.getTime() - (minusDay * millisecond));
        //本周 周日
        const sunday = new Date(monday.getTime() + (6 * millisecond));

        return [monday, sunday];
    }

    static getCurrentMonth() {
        //获取当前时间
        const currentDate = new Date();
        //获得当前月份0-11
        let currentMonth = currentDate.getMonth();
        //获得当前年份4位年
        let currentYear = currentDate.getFullYear();
        //求出本月第一天
        const firstDay = new Date(currentYear, currentMonth, 1);

        //当为12月的时候年份需要加1
        //月份需要更新为0 也就是下一年的第一个月
        if (currentMonth === 11) {
            currentYear++;
            currentMonth = 0; //
        } else {
            //否则只是月份增加,以便求的下一月的第一天
            currentMonth++;
        }

        //一天的毫秒数
        const millisecond = 1000 * 60 * 60 * 24;
        //下月的第一天
        const nextMonthDayOne = new Date(currentYear, currentMonth, 1);
        //求出上月的最后一天
        const lastDay = new Date(nextMonthDayOne.getTime() - millisecond);

        return [ firstDay, lastDay ];
    }

    static getCurrentYear() {
        //获取当前时间
        const currentDate = new Date();
        //获得当前年份4位年
        const currentYear = currentDate.getFullYear();

        //本年第一天
        const currentYearFirstDate = new Date(currentYear, 0, 1);
        //本年最后一天
        const currentYearLastDate = new Date(currentYear, 11, 31);

        return [currentYearFirstDate, currentYearLastDate];
    }

    static getNextMonth() {
        const nextMonthStartDate = new Date();
        const nextMonthEndDate = new Date();
        // 下个月的第一天
        nextMonthStartDate.setMonth(nextMonthStartDate.getMonth() + 1);
        nextMonthStartDate.setDate(1);

        nextMonthEndDate.setMonth(nextMonthEndDate.getMonth() + 2);
        nextMonthEndDate.setDate(0);

        return [nextMonthStartDate, nextMonthEndDate];
    }
}

