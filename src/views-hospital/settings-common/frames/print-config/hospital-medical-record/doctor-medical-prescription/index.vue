<template>
    <biz-setting-layout>
        <biz-setting-content>
            <div class="hospital-doctor-medical-prescription-wrapper">
                <hospital-medical-record-tab style="margin-bottom: 16px;"></hospital-medical-record-tab>

                <div class="print-config-content">
                    <abc-form ref="printForm">
                        <!-- 模板选择 -->
                        <div v-if="isShandong" class="group-item">
                            <div class="group-label middle-group-label">
                                医嘱单格式
                            </div>
                            <div class="group-content">
                                <abc-select v-model="postData.template" :width="288">
                                    <abc-option :value="0" label="通用医嘱单格式"></abc-option>
                                    <abc-option :value="1" label="山东省医嘱单格式"></abc-option>
                                </abc-select>
                            </div>
                        </div>

                        <!-- 前记 -->
                        <div class="print-config-items">
                            <div class="group-title">
                                医嘱单前记
                            </div>
                            <div class="group-item">
                                <div class="group-label middle-group-label">
                                    抬头名称
                                </div>
                                <!-- 山东定制抬头 -->
                                <div v-if="postData.template === 1" class="print-title-wrapper">
                                    <div class="print-title">
                                        <abc-form-item required class="print-form-item" :validate-event="validateName">
                                            <title-setting v-model="postData.shandong.title" :max-length="titleMaxLength * 2"></title-setting>
                                        </abc-form-item>

                                        <div class="group-option" style="padding: 0;">
                                            <!--<abc-button v-if="!isShowSubtitle" type="text" @click="isShowSubtitle = true">-->
                                            <!--    加一行-->
                                            <!--</abc-button>-->
                                            <abc-button type="text" @click="handleSetAllDocuments">
                                                应用至全部
                                            </abc-button>
                                        </div>
                                    </div>
                                    <!--<div v-if="isShowSubtitle" class="print-title">-->
                                    <!--    <abc-form-item class="print-form-item">-->
                                    <!--        <title-setting v-model="postData.shandong.subtitle" :max-length="titleMaxLength * 2"></title-setting>-->
                                    <!--    </abc-form-item>-->

                                    <!--    <div class="group-option" style="padding: 0;">-->
                                    <!--        <abc-button type="text" class="subtitle-delete-button" @click="deleteSubTitle">-->
                                    <!--            删除-->
                                    <!--        </abc-button>-->
                                    <!--    </div>-->
                                    <!--</div>-->
                                </div>
                                <!-- 通用抬头 -->
                                <div v-else class="print-title-wrapper">
                                    <div class="print-title">
                                        <abc-form-item required class="print-form-item" :validate-event="validateName">
                                            <title-setting v-model="postData.header.title" :max-length="titleMaxLength * 2"></title-setting>
                                        </abc-form-item>

                                        <div class="group-option" style="padding: 0;">
                                            <abc-button v-if="!isShowSubtitle" type="text" @click="isShowSubtitle = true">
                                                加一行
                                            </abc-button>
                                            <abc-button type="text" @click="handleSetAllDocuments">
                                                应用至全部
                                            </abc-button>
                                        </div>
                                    </div>
                                    <div v-if="isShowSubtitle" class="print-title">
                                        <abc-form-item class="print-form-item">
                                            <title-setting v-model="postData.header.subtitle" :max-length="titleMaxLength * 2"></title-setting>
                                        </abc-form-item>

                                        <div class="group-option" style="padding: 0;">
                                            <abc-button type="text" class="subtitle-delete-button" @click="deleteSubTitle">
                                                删除
                                            </abc-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 山东医嘱单正文 -->
                        <div v-if="postData.template === 1" class="print-config-items">
                            <div class="group-title">
                                医嘱单正文
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    长嘱停止时间
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.shandong.longTimeStopTime" style="width: 100%;" @change="handleClickTab(0)">
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            打印操作时间
                                        </abc-radio>
                                        <abc-radio :label="0" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    临嘱执行时间
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.shandong.oneTimeExecuteTime" style="width: 100%;" @change="handleClickTab(1)">
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            打印操作时间
                                        </abc-radio>
                                        <abc-radio :label="0" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    起始医生
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.shandong.startDoctor" style="width: 100%;">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    起始护士
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.shandong.startNurse" style="width: 100%;" @change="handleClickTab(0)">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    停止医生
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.shandong.stopDoctor" style="width: 100%;" @change="handleClickTab(0)">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    停止护士
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.shandong.stopNurse" style="width: 100%;" @change="handleClickTab(0)">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    执行护士
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.shandong.executeNurse" style="width: 100%;" @change="handleClickTab(1)">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                        </div>
                        <!-- 通用医嘱单正文 -->
                        <div v-else class="print-config-items">
                            <div class="group-title">
                                医嘱单正文
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    医嘱单格式
                                </div>
                                <div class="group-content" style="margin-bottom: 8px;">
                                    <abc-radio-group v-model="postData.content.executeBeforeMedical">
                                        <abc-radio :label="1">
                                            下达、执行信息在医嘱前
                                        </abc-radio>
                                        <abc-radio :label="2">
                                            下达、执行信息在医嘱后
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    长嘱单内容
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-checkbox
                                        v-model="postData.content.longTimeUsage"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(0)"
                                    >
                                        用法
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.longTimeCheckedTime"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(0)"
                                    >
                                        核对时间
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.longTimeCheckedOperatorName"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(0)"
                                    >
                                        核对护士
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.longTimeStopTime"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(0)"
                                    >
                                        停止时间
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.longTimeStopDoctor"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(0)"
                                    >
                                        停止医生
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.longTimeOperateNurseSignature"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(0)"
                                    >
                                        停止护士
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.longTimeNurseSignature"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(0)"
                                    >
                                        护士签名
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.longTimeRemark"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(0)"
                                    >
                                        医嘱备注
                                    </abc-checkbox>
                                </div>
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    临嘱单内容
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-checkbox
                                        v-model="postData.content.oneTimeUsage"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(1)"
                                    >
                                        用法
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.oneTimeCheckedTime"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(1)"
                                    >
                                        核对时间
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.oneTimeCheckedOperatorName"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(1)"
                                    >
                                        核对护士
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.oneTimeExecuteTime"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(1)"
                                    >
                                        执行时间
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.oneTimeOperateNurseSignature"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(1)"
                                    >
                                        执行护士
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.oneTimeNurseSignature"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(1)"
                                    >
                                        护士签名
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.content.oneTimeRemark"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleClickTab(1)"
                                    >
                                        医嘱备注
                                    </abc-checkbox>
                                </div>
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    医生签名
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.content.doctorSignatureType" style="width: 100%;">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div v-if="postData.content.longTimeCheckedOperatorName || postData.content.oneTimeCheckedOperatorName" class="group-item patient-group-item">
                                <div class="group-label">
                                    核对护士
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.content.checkedNurseSignatureType" style="width: 100%;">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div v-if="postData.content.longTimeStopDoctor" class="group-item patient-group-item">
                                <div class="group-label">
                                    停止医生
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.content.stopDoctorSignatureType" style="width: 100%;" @change="handleClickTab(0)">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div v-if="postData.content.longTimeOperateNurseSignature" class="group-item patient-group-item">
                                <div class="group-label">
                                    停止护士
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.content.stopNurseSignatureType" style="width: 100%;" @change="handleClickTab(0)">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>

                            <div v-if="postData.content.oneTimeOperateNurseSignature" class="group-item patient-group-item">
                                <div class="group-label">
                                    执行护士
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.content.operateNurseSignatureType" style="width: 100%;" @change="handleClickTab(1)">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                        </div>

                        <!-- 通用医嘱单后记 -->
                        <div v-if="!postData.template" class="print-config-items no-margin-bottom">
                            <div class="group-title">
                                医嘱单后记
                            </div>
                            <div class="group-item patient-group-item">
                                <div class="group-label">
                                    页尾签字行
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-checkbox
                                        v-model="postData.footer.footerDoctorSignature"
                                        type="number"
                                        class="checkbox-no-margin"
                                    >
                                        医生签名
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.footer.footerNurseSignature"
                                        type="number"
                                        class="checkbox-no-margin"
                                    >
                                        护士签名
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.footer.printDate"
                                        type="number"
                                        class="checkbox-no-margin"
                                    >
                                        打印时间
                                    </abc-checkbox>
                                </div>
                            </div>
                            <div v-if="postData.footer.footerDoctorSignature" class="group-item patient-group-item">
                                <div class="group-label">
                                    医生签字
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.footer.footerDoctorSignatureType" style="width: 100%;">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                            <div v-if="postData.footer.footerNurseSignature" class="group-item patient-group-item">
                                <div class="group-label">
                                    护士签名
                                </div>
                                <div class="group-content" style="width: 100%;">
                                    <abc-radio-group v-model="postData.footer.footerNurseSignatureType" style="width: 100%;">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            电脑签名
                                        </abc-radio>
                                        <abc-radio :label="3" class="checkbox-no-margin">
                                            手写电子签
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            不打印
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                        </div>
                    </abc-form>
                </div>
            </div>

            <template #footer>
                <biz-setting-footer>
                    <abc-button :disabled="disabled" :loading="btnLoading" @click="handleSave">
                        保存
                    </abc-button>
                    <abc-button type="blank" @click="handleReset">
                        恢复默认
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar>
            <preview-layout class="is-a4-preview-layout" :class="`${!postData.template ? 'is-a4-preview-layout-more-padding' : ''}`">
                <template slot="previewTab">
                    <div
                        v-for="(item, index) in previewTabs"
                        :key="item.label"
                        class="preview-tab-item"
                        :class="{ 'preview-tab-item__active': index === currentPreviewTab }"
                        @click="handleClickTab(index)"
                    >
                        {{ item.label }}
                    </div>
                </template>
                <div slot="previewHtml" ref="previewMountPoint"></div>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script>
    import TitleSetting from 'views/settings/print-config/components/title-setting.vue';
    import clone from 'utils/clone';
    import PreviewLayout from 'views/settings/print-config/components/preview-layout.vue';
    import { validateName } from 'views/inventory/goods/utils';
    import HospitalMixinPrint
        from '@/views-hospital/settings-common/frames/print-config/hospital-medical-record/hospital-mixin-print';
    import store from '@/store';
    import {
        ADVICE_SHANDONG_LONG_TIME_DATA, ADVICE_SHANDONG_ONCE_TIME_DATA,
        DOCTOR_MEDICAL_PRESCRIPTION_LONG_TIME_DATA, DOCTOR_MEDICAL_PRESCRIPTION_ONE_TIME_DATA,
    } from '@/views-hospital/settings-common/frames/print-config/hospital-medical-record/doctor-medical-prescription/constant';
    import { mapGetters } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingSidebar,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import HospitalMedicalRecordTab
        from '@/views-hospital/settings-common/frames/print-config/hospital-medical-record/components/hospital-medical-record-tab/index.vue';
    import { getLengthWithFullCharacter } from 'views/settings/print-config/utils';
    import { AbcPrintOrientation } from '@/printer/constants';

    const ResetData = {
        template: 0, // 模板, 0:通用 1:山东
        // 山东定制医嘱
        shandong: {
            'title': '', // 抬头名称 字符串必填
            'subtitle': '', // 副抬头名称 可选值
            'startDoctor': 1, // 起始医生签名, 2:电脑签名 3:手写签名 1:不打印, 默认 1
            'startNurse': 1, // 起始护士签名, 2:电脑签名 3:手写签名 1:不打印, 默认 1
            'stopDoctor': 1, // 停止医生签名, 2:电脑签名 3:手写签名 1:不打印, 默认 1
            'stopNurse': 1, // 停止护士签名, 2:电脑签名 3:手写签名 1:不打印, 默认 1
            'executeNurse': 1, // 执行护士签名, 2:电脑签名 3:手写签名 1:不打印, 默认 1
            'longTimeStopTime': 1, // 长期医嘱单-停止时间, 0:不打印 1:打印, 默认 1
            'oneTimeExecuteTime': 1, // 临时医嘱执行时间, 0:不打印 1:打印, 默认 1
        },
        // 医嘱单前记
        header: {
            'title': '', // 抬头名称 字符串必填
            'subtitle': '', // 副抬头名称 可选值
        },
        // 医嘱单正文
        content: {
            'executeBeforeMedical': 2, // 下达、执行信息在医嘱前/后, 可选值 1 2, 默认 2
            'longTimeUsage': 1, // 长期医嘱单-用法, 可选值 0 1, 默认 1
            'longTimeCheckedTime': 1, // 长期医嘱单-核对时间, 可选值 0 1, 默认 1
            'longTimeCheckedOperatorName': 1, // 长期医嘱单-核对护士, 可选值 0 1, 默认 1
            'longTimeOperateNurseSignature': 1, // 长期医嘱单-护士签名, 可选值 0 1, 默认 1
            'longTimeStopTime': 1, // 长期医嘱单-停止时间, 可选值 0 1, 默认 1
            'longTimeStopDoctor': 1, // 长期医嘱单-停止医生, 可选值 0 1, 默认 1
            'longTimeRemark': 1, // 长期医嘱单-医嘱备注, 可选值 0 1, 默认 1
            'oneTimeUsage': 1, // 临时医嘱单-用法, 可选值 0 1, 默认 1
            'oneTimeCheckedTime': 1, // 临时医嘱单-核对时间, 可选值 0 1, 默认 1
            'oneTimeCheckedOperatorName': 1, // 临时医嘱单-核对护士, 可选值 0 1, 默认 1
            'oneTimeOperateNurseSignature': 1, // 临时医嘱单-护士签名, 可选值 0 1, 默认 1
            'oneTimeRemark': 1, // 临时医嘱单-医嘱备注, 可选值 0 1, 默认 1
            'doctorSignatureType': 2, // 医生签名, 可选值 1 2 3, 默认 2
            'checkedNurseSignatureType': 2, // 核对护士签名, 可选值 1 2 3, 默认 2
            'stopDoctorSignatureType': 2, // 停止医生签名, 可选值 1 2 3, 默认 2
            'operateNurseSignatureType': 2, // 护士签名, 可选值 1 2 3, 默认 2
            'oneTimeExecuteTime': 0, // 临时医嘱执行时间，可选值 0 1, 默认 0
            'longTimeNurseSignature': 0, // 长期医嘱护士签名 可选值 0 1, 默认 0
            'oneTimeNurseSignature': 0, // 临时医嘱护士签名 可选值 0 1, 默认0
            'stopNurseSignatureType': 2, //  停止护士签名 可选值 1 2 3, 默认 2
        },
        // 医嘱单后记
        footer: {
            'footerDoctorSignature': 1, // 医生签字, 可选值 0 1, 默认 1
            'footerNurseSignature': 1, // 护士签字, 可选值 0 1, 默认 1
            'printDate': 0, // 打印时间, 可选值 0 1, 默认 0
            'footerDoctorSignatureType': 2, // 医生签字, 可选值 1 2 3, 默认 2
            'footerNurseSignatureType': 2, // 护士签字, 可选值 1 2 3, 默认 2
        },
    };

    export default {
        name: 'DoctorMedicalPrescription',
        components: {
            HospitalMedicalRecordTab,
            PreviewLayout,
            TitleSetting,
            BizSettingLayout,
            BizSettingContent,
            BizSettingSidebar,
            BizSettingFooter,
        },
        mixins: [HospitalMixinPrint],
        data() {
            return {
                postData: clone(ResetData),
                titleMaxLength: 15, // 标题最大长度
                isShowSubtitle: false,
                currentPreviewTab: 0,
                btnLoading: false,
            };
        },
        computed: {
            ...mapGetters([
                'printHospitalMedicalDocumentsConfig',
                'clinicBasicConfig',
            ]),
            previewPage() {
                return {
                    size: 'A4',
                    orientation: AbcPrintOrientation.portrait,
                    pageHeightLevel: null,
                    pageSizeReduce: {
                        bottom: 4,
                        left: 4,
                        right: 4,
                        top: 4,
                    },
                };
            },
            isShandong() {
                return this.clinicBasicConfig.addressProvinceId === '370000';
            },
            previewTabs() {
                if (this.postData.template === 1) {
                    return [{
                        label: '长期医嘱单',
                        exampleData: ADVICE_SHANDONG_LONG_TIME_DATA,
                    }, {
                        label: '临时医嘱单',
                        exampleData: ADVICE_SHANDONG_ONCE_TIME_DATA,
                    }];
                }
                return [{
                    label: '长期医嘱单',
                    exampleData: DOCTOR_MEDICAL_PRESCRIPTION_LONG_TIME_DATA,
                }, {
                    label: '临时医嘱单',
                    exampleData: DOCTOR_MEDICAL_PRESCRIPTION_ONE_TIME_DATA,
                }];
            },
            currentExampleData() {
                // if (this.postData.template === 1) {
                //     return {};
                // }
                return this.previewTabs[this.currentPreviewTab].exampleData;
            },
            disabled() {
                return isEqual(this.postData, this._cachePostData);
            },
        },
        watch: {
            'printHospitalMedicalDocumentsConfig.advice': {
                handler(val) {
                    if (val) {
                        this.handleInitialPostData(val, 'advice');
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        created() {
            this._doctorMedicalPrescription = true;
        },
        methods: {
            validateName,
            handleInitialPostData(postData, type = 'advice') {
                const cachePostData = clone(postData);

                // 判断抬头是否超过15个字符(需分区全角/半角),将超过部分拆分到副抬头
                const {
                    fullCharacterLength, splitLength,
                } = getLengthWithFullCharacter(cachePostData.header.title, this.titleMaxLength);
                if (fullCharacterLength > this.titleMaxLength) {
                    const cacheTitle = cachePostData.header.title;
                    cachePostData.header.title = cacheTitle.slice(0, splitLength);
                    cachePostData.header.subtitle = cacheTitle.slice(splitLength);
                    this.updateCustomTitleByPrintHospitalMedicalDocumentsConfig('header', type, cachePostData);
                }

                // 判断山东定制模板抬头是否超过15个字符(需分区全角/半角),将超过部分拆分到副抬头
                const {
                    fullCharacterLengthShandong, splitLengthShandong,
                } = getLengthWithFullCharacter(cachePostData.shandong.title, this.titleMaxLength);
                if (fullCharacterLengthShandong > this.titleMaxLength) {
                    const cacheTitle = cachePostData.shandong.title;
                    cachePostData.shandong.title = cacheTitle.slice(0, splitLengthShandong);
                    cachePostData.shandong.subtitle = cacheTitle.slice(splitLengthShandong);
                    this.updateCustomTitleByPrintHospitalMedicalDocumentsConfig('shandong', type, cachePostData);
                }

                this.postData = cachePostData;
                this._cachePostData = clone(this.postData);

                if (!this.postData.header.title || !this.postData.shandong.title) {
                    const cacheClinicName = this.currentClinic.clinicName;
                    const {
                        fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                    } = getLengthWithFullCharacter(cacheClinicName, this.titleMaxLength);
                    if (fullClinicCharacterLength > this.titleMaxLength) {
                        if (!this.postData.header.title) {
                            this.postData.header.title = cacheClinicName.slice(0, splitClinicLength);
                            this.postData.header.subtitle = cacheClinicName.slice(splitClinicLength);
                        }
                        if (!this.postData.shandong.title) {
                            this.postData.shandong.title = cacheClinicName.slice(0, splitClinicLength);
                            this.postData.shandong.subtitle = cacheClinicName.slice(splitClinicLength);
                        }
                    } else {
                        if (!this.postData.header.title) {
                            this.postData.header.title = cacheClinicName;
                        }
                        if (!this.postData.shandong.title) {
                            this.postData.shandong.title = cacheClinicName;
                        }
                    }
                }

                if (this.postData.template === 1) {
                    this.isShowSubtitle = !!this.postData.shandong.subtitle;
                } else {
                    this.isShowSubtitle = !!this.postData.header.subtitle;
                }
            },
            handleSetAllDocuments() {
                this.$refs.printForm.validate((val) => {
                    if (val) {
                        const title = this.postData.template === 1 ? this.postData.shandong.title : this.postData.header.title;
                        const subtitle = this.postData.template === 1 ? this.postData.shandong.subtitle : this.postData.header.subtitle;
                        let content = `是否确认将全部病历文书抬头名称统一修改为“${title}”？`;
                        if (subtitle) {
                            content = `是否确认将全部病历文书抬头名称统一修改为“${title}”和“${subtitle}”？`;
                        }
                        // 设置全部病例文书抬头
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content,
                            onConfirm: async () => {
                                await this.updateTitle(title, subtitle);
                                this.$Toast({
                                    message: '设置成功',
                                    type: 'success',
                                });
                            },
                        });
                    }
                });
            },
            async handleClickTab(index) {
                if (this.currentPreviewTab === index) return;
                this.currentPreviewTab = index;
                this.printInstance = null;
                await this.mountPrintInstance();
                await this.updateInstanceGlobalConfig(this.postData);
            },
            getCurrentTemplate() {
                if (this.postData.template === 1) {
                    return window.AbcPackages.AbcTemplates.hospitalAdviceShandong;
                }
                return window.AbcPackages.AbcTemplates.hospitalDoctorMedicalPrescription;
            },
            instanceGlobalConfigHandler(newValue) {
                const newInstanceGlobalConfig = clone(store.getters.printGlobalConfig);
                newInstanceGlobalConfig.hospitalMedicalDocuments.advice = newValue;
                return newInstanceGlobalConfig;
            },
            /**
             * 删除副抬头
             */
            deleteSubTitle() {
                this.isShowSubtitle = false;
                if (this.postData.template === 1) {
                    this.postData.shandong.subtitle = '';
                } else {
                    this.postData.header.subtitle = '';
                }
            },
            handleReset() {
                this.handleResetPostData(ResetData);
            },
            handleSave() {
                this.$refs.printForm.validate(async (val) => {
                    if (val) {
                        await this.updatePrintConfig('advice');
                    }
                });
            },
        },
    };
</script>

<style lang="scss">
.hospital-doctor-medical-prescription-wrapper {
    padding: 0;
}
</style>
