<template>
    <abc-dialog
        v-model="dialogVisible"
        title="选择取消采集项目"
        size="xlarge"
    >
        <sample-table :data-source="sampleTableDataSource" scene="cancel"></sample-table>

        <abc-flex slot="footer" justify="end">
            <abc-space>
                <abc-button
                    type="primary"
                    :loading="confirmLoading"
                    :disabled="confirmDisabled"
                    @click="handleConfirm"
                >
                    确定
                </abc-button>
                <abc-button
                    type="blank"
                    @click="dialogVisible = false"
                >
                    取消
                </abc-button>
            </abc-space>
        </abc-flex>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import SampleTable from './sample-table.vue';
    import { SampleExaminationAPI } from '@/api/hospital/examination/sample-examination';
    import {
        CollectionStatus,
    } from '../utils/constant';

    export default {
        components: {
            SampleTable,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            finishExaminationItems: {
                type: Array,
                default: () => [],
            },
        },
        data: () => ({
            confirmLoading: false,
            sampleTableDataSource: [],
        }),
        computed: {
            ...mapGetters([
                'userInfo',
            ]),
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input',val);
                },
            },
            confirmDisabled() {
                return !this.sampleTableDataSource.some((item) => item.checked);
            },
        },
        watch: {
            dialogVisible(val) {
                if (val) {
                    this.finishExaminationItems.forEach((item) => {
                        item.checked = false;
                        item.finished = false;
                    });
                }
            },
            finishExaminationItems: {
                handler(val) {
                    this.sampleTableDataSource = val;
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            // 保存采样记录
            async saveSampleRecord(params) {
                this.confirmLoading = true;
                try {
                    await SampleExaminationAPI.saveSampleRecord(params);
                } catch (error) {
                    console.log(error);
                }
                this.confirmLoading = false;
            },

            async handleConfirm() {
                const params = {
                    list: this.finishExaminationItems.filter((item) => item.checked).map((item) => ({
                        id: item.id,
                        sampleStatus: CollectionStatus.wait,
                    })),
                    operatorId: this.userInfo.id,
                    sampleScene: 0, // 0-样本采集 1-样本核收
                };
                await this.saveSampleRecord(params);
                this.$emit('refresh');
                this.dialogVisible = false;
            },
        },
    };
</script>

<style lang='scss' scoped>
    .dialog-footer {
        text-align: right;
    }
</style>