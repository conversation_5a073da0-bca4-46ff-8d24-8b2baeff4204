<template>
    <abc-container
        class="hospital-dashboard-container"
    >
        <abc-container-center class="hospital-dashboard-center">
            <div class="hospital-dashboard">
                <abc-card class="hospital-dashboard__notice-wrapper">
                    <overview-header></overview-header>
                </abc-card>
                <abc-card padding-size="large">
                    <div class="hospital-dashboard__navigation">
                        <div
                            v-for="item in navigationItems"
                            :key="item.name"
                            class="hospital-dashboard__navigation-item"
                        >
                            <div class="hospital-dashboard__navigation-item-type">
                                <span :style="{ backgroundColor: item.bgColor }"> {{ item.name }}</span>
                            </div>
                            <abc-option-card
                                v-for="navItem in item.list"
                                :key="navItem.name"
                                :title.prop="navItem.canAccess && navItem.visible ? navItem.name : '未开通权限'"
                                :disabled="!navItem.canAccess || !navItem.visible"
                                :show-icon="false"
                                :selectable="false"
                                style="min-width: 148px; margin-bottom: 12px;"
                                width="100%"
                                :height="48"
                                @click="handleNavigationItemClick(navItem)"
                            >
                                <abc-space slot="title" :size="12">
                                    <abc-icon
                                        :icon="navItem.icon"
                                        :size="20"
                                        :color="(!navItem.canAccess || !navItem.visible) ? 'var(--abc-color-T3)' : 'var(--abc-color-theme1)'"
                                    ></abc-icon>
                                    <abc-text style="font-size: 15px;" :theme="(!navItem.canAccess || !navItem.visible) ? 'gray-light' : 'black'" :title="navItem.canAccess ? navItem.name : '未开通权限'">
                                        {{ navItem.name }}
                                    </abc-text>
                                </abc-space>
                            </abc-option-card>
                        </div>
                    </div>
                </abc-card>

                <abc-card v-if="PENavigationItems.length" padding-size="large">
                    <div class="hospital-dashboard__navigation direction-rows">
                        <div
                            v-for="item in PENavigationItems"
                            :key="item.name"
                            class="hospital-dashboard__navigation-item"
                        >
                            <div class="hospital-dashboard__navigation-item-type">
                                <span :style="{ backgroundColor: item.bgColor }"> {{ item.name }}</span>
                            </div>
                            <div class="nav-item-wrapper">
                                <abc-option-card
                                    v-for="navItem in item.list"
                                    :key="navItem.name"
                                    :title.prop="navItem.canAccess && navItem.visible ? navItem.name : '未开通权限'"
                                    :disabled="!navItem.canAccess || !navItem.visible"
                                    :show-icon="false"
                                    :selectable="false"
                                    width="100%"
                                    :height="48"
                                    @click="handleNavigationItemClick(navItem)"
                                >
                                    <abc-space slot="title" :size="12">
                                        <abc-icon
                                            :icon="navItem.icon"
                                            :size="20"
                                            :color="(!navItem.canAccess || !navItem.visible) ? 'var(--abc-color-T3)' : 'var(--abc-color-theme1)'"
                                        ></abc-icon>
                                        <abc-text style="font-size: 15px;" :theme="(!navItem.canAccess || !navItem.visible) ? 'gray-light' : 'black'" :title="navItem.canAccess ? navItem.name : '未开通权限'">
                                            {{ navItem.name }}
                                        </abc-text>
                                    </abc-space>
                                </abc-option-card>
                            </div>
                        </div>
                    </div>
                </abc-card>
            </div>
        </abc-container-center>
        <side-bar :show-transfer-banner="false"></side-bar>
    </abc-container>
</template>

<script>
    import SideBar from 'src/views/layout/dashboard-sidebar.vue';

    import { isClientSupportHospital } from 'utils/electron.js';
    import { MODULE_ID_MAP } from 'utils/constants.js';
    import { sendCreateTab } from '@/core/electron-tab-helper.js';
    import {
        MultiTabSocketServer, AppTabId,
    } from '@/core/index.js';
    import { mapGetters } from 'vuex';
    import AbcAccess from '@/access/utils.js';
    import ModulePermission from 'views/permission/module-permission.js';
    import { AppTabName } from '@/core/constants.js';

    import { AbcSocketMessageManager } from '@/service/message/manager.js';
    import { windowOpen } from '@/core/navigate-helper';
    import AbcUiThemeMixin from 'views/common/abc-ui-theme-mixin';
    import OverviewHeader from 'views/dashboard/overview-header.vue';

    function hasModulePermission(moduleId, modules) {
        const targetModule = modules.find((m) => m.id === moduleId);
        if (!targetModule) {
            return false;
        }
        if (targetModule.checked) {
            return true;
        }
        if (targetModule.children?.some((child) => hasModulePermission(child.id, targetModule.children))) {
            return true;
        }
        return false;
    }

    export default {
        name: 'HospitalDashboard',
        components: {
            OverviewHeader,
            SideBar,
        },
        mixins: [
            ModulePermission,
            AbcUiThemeMixin,
        ],
        computed: {
            ...mapGetters([
                'currentClinic',
                'userInfo',

                'todosCounter',
                'clinicConfig',

                'isAdmin',
                'isChainAdmin',
                'isSingleStore',

                'pharmacy',
                'purchaseTodo',
                'examinationTodo',
                'inspectTodo',

                'pharmacyTodo',
                'mallWaitingPaidOrderCount',

                'currentRegion', // 门店地区标识
                'isEnableChildHealth',

                'isOpenScrm',
            ]),
            ...mapGetters('viewDistribute', ['featureTheme', 'viewDistributeConfig']),

            /**
             * 连锁总部不需要医保
             * @returns {boolean|*|boolean|string}
             */
            hasSocial() {
                return !this.isChainAdmin && this.currentRegion && this.hasSocialModule;
            },

            isOpenB2BMall() {
                if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.B2B_MALL)) return false;
                return this.clinicConfig?.b2bMallConfig?.isEnableB2BMall;
            },

            /**
             * 连锁总部不需要B2B商城
             * @returns {false|(function(): (boolean|*))|*}
             */
            hasMall() {
                if (this.$app.isExpired) {
                    return false;
                }
                return !this.isChainAdmin && this.isOpenB2BMall;
            },
            navigationTypeList() {
                return [
                    {
                        name: '门诊',
                        bgColor: '#E3F2FE',
                        groupBy: 'outpatient',
                    },
                    {
                        name: '住院',
                        bgColor: '#DBF9F9',
                        groupBy: 'hospital',
                    },
                    {
                        name: '医技',
                        bgColor: '#DFF6EC',
                        groupBy: 'medical-tech',
                    },
                    {
                        name: 'HCRM',
                        bgColor: '#FFF0EF',
                        groupBy: 'hCrm',
                    },
                    {
                        name: '供应链',
                        bgColor: '#FFF2E0',
                        groupBy: 'supply-chain',
                    },
                    {
                        name: '运营',
                        bgColor: '#F0F0F0',
                        groupBy: 'operation',
                    },
                ];
            },

            navigationItems() {
                const host = `${location.protocol}//${location.host}`;
                const allNavigationItems = [
                    {
                        id: AppTabId.OUTPATIENT,
                        name: AppTabName[AppTabId.OUTPATIENT],
                        moduleId: MODULE_ID_MAP.hospitalOutpatientStation,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/his-outpatient`,
                        icon: 's-outpatient-workbench',
                        groupBy: 'outpatient',
                    },
                    {
                        id: AppTabId.HOSPITAL_DOCTOR,
                        name: AppTabName[AppTabId.HOSPITAL_DOCTOR],
                        moduleId: MODULE_ID_MAP.hospitalDoctorStation,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/doctor`,
                        icon: 's-doctor-workbench',
                        groupBy: 'hospital',
                    },
                    {
                        id: AppTabId.PHARMACY,
                        name: AppTabName[AppTabId.PHARMACY],
                        moduleId: MODULE_ID_MAP.hospitalPharmacyStation,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/his-pharmacy`,
                        icon: 's-pharmacy-workbench',
                        groupBy: 'medical-tech',
                    },
                    {
                        id: AppTabId.CRM,
                        name: AppTabName[AppTabId.CRM],
                        visible: true,
                        moduleId: MODULE_ID_MAP.patient,
                        url: `${host}/hospital/crm`,
                        icon: 's-patient-workbench',
                        groupBy: 'hCrm',
                    },
                    {
                        id: AppTabId.INVENTORY,
                        name: AppTabName[AppTabId.INVENTORY],
                        moduleId: MODULE_ID_MAP.inventory,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/inventories`,
                        icon: 's-inventory-workbench',
                        groupBy: 'supply-chain',
                    },
                    {
                        id: AppTabId.SOCIAL,
                        name: AppTabName[AppTabId.SOCIAL],
                        moduleId: MODULE_ID_MAP.social,
                        url: `${host}/hospital/social`,
                        icon: 's-chs-workbench',
                        visible: this.hasSocial,
                        groupBy: 'operation',
                        visibleInExpiredPeriod: true, // 过期周期内可访问
                    },
                    {
                        id: AppTabId.MEDICAL_RECORD_MANAGEMENT,
                        name: AppTabName[AppTabId.MEDICAL_RECORD_MANAGEMENT],
                        moduleId: MODULE_ID_MAP.hospitalMedicalRecordManagement,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/medical-record-management`,
                        icon: 's-records-workbench',
                        groupBy: 'operation',
                    },
                    {
                        id: AppTabId.TREATMENT,
                        name: AppTabName[AppTabId.TREATMENT],
                        moduleId: MODULE_ID_MAP.nurse,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/treatment`,
                        icon: 's-inject-workbench',
                        groupBy: 'outpatient',
                    },
                    {
                        id: AppTabId.HOSPITAL_NURSE,
                        name: AppTabName[AppTabId.HOSPITAL_NURSE],
                        moduleId: MODULE_ID_MAP.hospitalNurseStation,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/nurse`,
                        icon: 's-nurse-workbench',
                        groupBy: 'hospital',
                    },
                    {
                        id: AppTabId.EXAMINATION,
                        name: AppTabName[AppTabId.EXAMINATION],
                        moduleId: MODULE_ID_MAP.examination,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/examination`,
                        icon: 's-test-workbench',
                        groupBy: 'medical-tech',
                    },
                    {
                        id: AppTabId.MARKETING,
                        name: AppTabName[AppTabId.MARKETING],
                        moduleId: MODULE_ID_MAP.marketing,
                        visible: this.isChainAdmin || this.isSingleStore,
                        url: `${host}/hospital/marketing`,
                        icon: 's-marketing-workbench',
                        groupBy: 'hCrm',
                    },
                    {
                        id: AppTabId.HOSPITAL_SUPPLY_CENTER,
                        name: AppTabName[AppTabId.HOSPITAL_SUPPLY_CENTER],
                        moduleId: MODULE_ID_MAP.hospitalSupplyCenter,
                        visible: true,
                        url: `${host}/hospital/supply-center`,
                        icon: 's-purchase-workbench',
                        groupBy: 'supply-chain',
                    },
                    {
                        id: AppTabId.GSP,
                        name: AppTabName[AppTabId.GSP],
                        moduleId: MODULE_ID_MAP.bizPharmacyGSP,
                        visible: true,
                        url: `${host}/hospital/gsp`,
                        icon: 's-gsp-workbench',
                        groupBy: 'operation',
                    },
                    {
                        id: AppTabId.STATISTICS,
                        name: AppTabName[AppTabId.STATISTICS],
                        moduleId: MODULE_ID_MAP.statistics,
                        visible: true,
                        url: `${host}/hospital/statistics`,
                        icon: 's-chart-workbench',
                        groupBy: 'operation',
                        visibleInExpiredPeriod: true, // 过期后可访问
                    },
                    {
                        id: AppTabId.WE_CLINIC,
                        name: AppTabName[AppTabId.WE_CLINIC],
                        moduleId: MODULE_ID_MAP.weClinic,
                        visible: true,
                        url: `${host}/hospital/we-clinic`,
                        icon: 's-microclinic-workbench',
                        groupBy: 'operation',
                    },
                    {
                        id: AppTabId.REGISTRATION,
                        name: AppTabName[AppTabId.REGISTRATION],
                        moduleId: MODULE_ID_MAP.registration,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/registration`,
                        icon: 's-reservation-workbench',
                        groupBy: 'outpatient',
                    },
                    {
                        id: AppTabId.CHARGE,
                        name: AppTabName[AppTabId.CHARGE],
                        moduleId: MODULE_ID_MAP.hospitalChargeStation,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/his-charge/charge-hospital`,
                        icon: 's-charge-workbench',
                        groupBy: 'hospital',
                    },

                    {
                        id: AppTabId.INSPECT,
                        name: AppTabName[AppTabId.INSPECT],
                        moduleId: MODULE_ID_MAP.inspect,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/inspect`,
                        icon: 's-scan-workbench',
                        groupBy: 'medical-tech',
                    },
                    {
                        id: AppTabId.SCRM,
                        name: AppTabName[AppTabId.SCRM],
                        visible: this.isOpenScrm,
                        moduleId: MODULE_ID_MAP.scrm,
                        url: process.env.SCRM_URL,
                        icon: 's-wecom-workbench',
                        groupBy: 'hCrm',
                    },
                    {
                        id: AppTabId.MALL,
                        name: AppTabName[AppTabId.MALL],
                        moduleId: MODULE_ID_MAP.mall,
                        visible: this.hasMall,
                        skipAuth: true, // 跳过校验
                        url: `${host}/hospital/mall`,
                        icon: 's-direct-workbench',
                        groupBy: 'supply-chain',
                    },
                    {
                        id: AppTabId.SETTING,
                        name: AppTabName[AppTabId.SETTING],
                        moduleId: MODULE_ID_MAP.setting,
                        visible: true,
                        url: `${host}/hospital/settings`,
                        icon: 's-setting-workbench',
                        groupBy: 'operation',
                    },
                    {
                        id: AppTabId.CHARGE,
                        name: AppTabName[AppTabId.CHARGE],
                        moduleId: MODULE_ID_MAP.hospitalChargeStation,
                        visible: !this.isChainAdmin,
                        url: `${host}/hospital/his-charge/cashier`,
                        icon: 's-charge-workbench',
                        groupBy: 'outpatient',
                    },
                ];

                const allModuleIds = this.userInfo?.moduleIds?.split(',') || [];
                const allModules = this.userInfo?.modules?.children || [];

                allNavigationItems.forEach((navItem) => {
                    if (allModuleIds.includes('0') || navItem.skipAuth) {
                        navItem.canAccess = true;
                    } else if (navItem.id === AppTabId.CHARGE) {
                        // hospital
                        if (navItem.groupBy === 'hospital') {
                            let canAccess = false;
                            if (allModuleIds.some((mId) => [MODULE_ID_MAP.hospitalChargeStation, MODULE_ID_MAP.hospitalChargeHospital].includes(mId))) {
                                canAccess = true;
                            } else if (allModuleIds.some((mId) => [MODULE_ID_MAP.hospitalChargeRegister].includes(mId))) {
                                canAccess = true;
                                navItem.url = `${host}/hospital/his-charge/register`;
                            }
                            navItem.canAccess = canAccess;
                        } else {
                            navItem.canAccess = allModuleIds.some((mId) => [MODULE_ID_MAP.hospitalChargeStation, MODULE_ID_MAP.cashier].includes(mId));
                        }

                    } else {
                        navItem.canAccess = hasModulePermission(+navItem.moduleId, allModules);
                    }

                    // 处理过期
                    if (navItem.canAccess && this.$app.isExpired) {
                        if (!(this.$app.canViewExpiredContent && navItem.visibleInExpiredPeriod)) {
                            navItem.canAccess = false;
                        }
                    }
                });
                return this.groupNavByType(allNavigationItems);
            },
            // 体检系统入口
            PENavigationItems() {
                if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION)) return [];
                const host = `${location.protocol}//${location.host}`;
                const list = [
                    {
                        id: AppTabId.PHYSICAL_EXAMINATION_INTEGRATED,
                        name: AppTabName[AppTabId.PHYSICAL_EXAMINATION_INTEGRATED],
                        moduleId: MODULE_ID_MAP.physicalExaminationIntegrated,
                        url: `${host}/hospital/physical-examination/integrated/check-up`,
                        icon: 's-reception-workbench',
                        visible: !this.isChainAdmin,
                        getUrl: (userModuleIds) => {
                            const _arr = [
                                {
                                    moduleId: MODULE_ID_MAP.physicalExaminationIntegratedCheckUp,
                                    url: `${host}/hospital/physical-examination/integrated/check-up`,
                                },
                                {
                                    moduleId: MODULE_ID_MAP.physicalExaminationIntegratedKanban,
                                    url: `${host}/hospital/physical-examination/integrated/kanban`,
                                },
                                {
                                    moduleId: MODULE_ID_MAP.physicalExaminationIntegratedReportManage,
                                    url: `${host}/hospital/physical-examination/integrated/report-manager`,
                                },
                            ];
                            const _obj = _arr.find((item) => {
                                return userModuleIds.indexOf(item.moduleId) > -1;
                            });
                            if (!_obj) return '';
                            return _obj.url;
                        },
                    },
                    {
                        id: AppTabId.CHARGE,
                        name: AppTabName[AppTabId.PHYSICAL_EXAMINATION_CASHIER],
                        moduleId: MODULE_ID_MAP.physicalExaminationCashier,
                        url: `${host}/hospital/his-charge/physical-examination-cashier`,
                        icon: 's-charge-workbench',
                        visible: !this.isChainAdmin,
                    },
                    {
                        id: AppTabId.EXAMINATION,
                        name: AppTabName[AppTabId.EXAMINATION],
                        moduleId: MODULE_ID_MAP.examination,
                        icon: 's-test-workbench',
                        url: `${host}/hospital/examination`,
                        visible: !this.isChainAdmin,
                    },
                    {
                        id: AppTabId.INSPECT,
                        name: AppTabName[AppTabId.INSPECT],
                        moduleId: MODULE_ID_MAP.inspect,
                        icon: 's-scan-workbench',
                        url: `${host}/hospital/inspect`,
                        visible: !this.isChainAdmin,
                    },
                    {
                        id: AppTabId.PHYSICAL_EXAMINATION_ASSESSMENT,
                        name: AppTabName[AppTabId.PHYSICAL_EXAMINATION_ASSESSMENT],
                        moduleId: MODULE_ID_MAP.physicalExaminationAssessment,
                        url: `${host}/hospital/physical-examination/assessment`,
                        icon: 's-doctor-workbench',
                        visible: !this.isChainAdmin,
                    },
                    {
                        id: AppTabId.PHYSICAL_EXAMINATION_ORDER,
                        name: AppTabName[AppTabId.PHYSICAL_EXAMINATION_ORDER],
                        moduleId: MODULE_ID_MAP.physicalExaminationOrder,
                        url: `${host}/hospital/physical-examination/order/individual`,
                        icon: 's-ordersetting-workbench',
                        visible: !this.isChainAdmin,
                        getUrl: (userModuleIds) => {
                            const _arr = [
                                {
                                    moduleId: MODULE_ID_MAP.physicalExaminationOrderIndividual,
                                    url: `${host}/hospital/physical-examination/order/individual`,
                                },
                                {
                                    moduleId: MODULE_ID_MAP.physicalExaminationOrderTeam,
                                    url: `${host}/hospital/physical-examination/order/team`,
                                },
                            ];
                            const _obj = _arr.find((item) => {
                                return userModuleIds.indexOf(item.moduleId) > -1;
                            });
                            if (!_obj) return '';
                            return _obj.url;
                        },
                    },
                ];

                const userModuleIds = this.userInfo?.moduleIds?.split(',') || [];
                const allModules = this.userInfo?.modules?.children || [];

                list.forEach((navItem) => {
                    navItem.canAccess = true;
                    if (this.$app.isExpired) {
                        navItem.canAccess = false;
                    } else if (userModuleIds.includes('0')) {
                        navItem.canAccess = true;
                    } else if (navItem.id === AppTabId.PHYSICAL_EXAMINATION_CASHIER) {
                        navItem.canAccess = userModuleIds.includes(MODULE_ID_MAP.hospitalChargeStation) || userModuleIds.includes(navItem.moduleId);
                    } else {
                        navItem.canAccess = userModuleIds.includes(navItem.moduleId) || hasModulePermission(+navItem.moduleId, allModules);
                    }
                    if (navItem.canAccess && navItem.getUrl) {
                        navItem.url = navItem.getUrl(userModuleIds) || navItem.url;
                    }
                });
                return [
                    {
                        name: '体检',
                        bgColor: '#F0F0F0',
                        list,
                    },
                ];
            },
        },
        created() {
            this.socketMsgManager = AbcSocketMessageManager.getInstance();
            if (isClientSupportHospital()) {
                window.socketServer = this.socketServer = new MultiTabSocketServer();
                this.socketServer.start();

                window.remote.app.mainWindow.webContents.on('app_tab_message_close_tab', (tabId) => {
                    console.log('closeTab', tabId);
                    this.socketServer.offTabEvent(tabId);
                });
            }
        },
        beforeDestroy() {
            if (isClientSupportHospital()) {
                window.remote.app.mainWindow.webContents.off('app_tab_message_close_tab');

                this.socketServer.destroy();
                this.socketServer = null;
            }
        },
        methods: {
            groupNavByType(list) {
                return Object.values(list.reduce((acc, cur) => {
                    const { groupBy } = cur;
                    const groupTypeInfo = this.navigationTypeList.find((item) => item.groupBy === groupBy);
                    acc[groupBy] = acc[groupBy] || {
                        groupBy, ...groupTypeInfo, list: [],
                    };
                    acc[groupBy].list.push({ ...cur });
                    return acc;
                }, {}));

            },
            handleNavigationItemClick(item) {
                if (!item.canAccess || !item.visible) {
                    return;
                }
                if (isClientSupportHospital()) {
                    // electron 环境，发送消息创建 tab
                    sendCreateTab(item);
                } else {
                    // 浏览器环境，直接打开 url
                    windowOpen(item.url);
                }
            },
        },
    };
</script>

<style lang="scss">
@import "~styles/theme.scss";
@import "~styles/mixin.scss";

.hospital-dashboard-container {
    max-width: 1440px !important;
    box-shadow: none !important;

    &#abc-container {
        height: auto;
        min-height: 100%;
        padding-top: var(--abc-space-l);
        padding-left: var(--abc-space-l);
        background-color: transparent;
        box-shadow: none;

        #abc-container-center {
            height: auto;
            scrollbar-gutter: unset;
        }
    }

    .hospital-dashboard-center {
        padding-bottom: var(--abc-space-m);
        margin-right: var(--abc-space-m);

        .hospital-dashboard {
            flex: 1;
            width: 100%;

            &__notice-wrapper {
                padding: var(--abc-paddingTB-xll) var(--abc-paddingLR-m);
            }

            &__navigation {
                display: grid;
                grid-template-columns: repeat(6, 1fr);
                grid-gap: 12px;
                grid-row-gap: 12px;

                &-item {
                    padding: 0;
                    margin: 0;

                    .nav-item {
                        padding: 13px 10px;
                        margin-bottom: 12px;
                        font-size: 15px;
                        line-height: 16px;
                        color: #343c49;
                        cursor: pointer;
                        border: 1px solid #e1e2e8;
                        border-radius: 5px;

                        .content {
                            display: flex;
                            align-items: center;
                            cursor: pointer;

                            &.is-disable {
                                cursor: not-allowed;
                                opacity: 0.3;
                            }
                        }

                        span {
                            margin-left: 10px;
                        }

                        &:hover {
                            background-color: $P4;
                        }

                        &:active {
                            background-color: $P6;
                        }

                        &.is-disable {
                            &:hover {
                                background-color: transparent;
                            }

                            &:active {
                                background-color: transparent;
                            }
                        }
                    }
                }

                &-item-type {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    min-width: 142px;
                    margin-bottom: 12px;
                    font-size: 15px;
                    color: #343c49;

                    span {
                        display: inline-block;
                        width: 60px;
                        height: 24px;
                        padding: 4px 6px;
                        font-size: 12px;
                        font-weight: bold;
                        color: #2d2d2d;
                        vertical-align: middle;
                        border-radius: var(--abc-border-radius-small);
                    }
                }

                &.direction-rows {
                    grid-template-columns: repeat(1, 1fr);
                }

                .nav-item-wrapper {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(148px, 1fr));
                    grid-gap: 12px;
                    grid-row-gap: 12px;
                }
            }
        }
    }
}

@media screen and (min-width: 1440px) and (max-width: 1920px) {
    .hospital-dashboard__navigation-item {
        min-width: 142px;
    }
}

@media screen and (min-width: 1280px) and (max-width: 1439px) {
    .hospital-dashboard__navigation-item {
        min-width: 130px;
    }
}
</style>
