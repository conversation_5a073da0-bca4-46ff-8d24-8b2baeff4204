import BasePageStore from '@/core/page/store.js';
import propertyAPI from 'api/property/index.js';
import {
    GoodsTypeEnum, GoodsSubTypeEnum,
} from '@abc/constants';
import GoodsV3API from 'api/goods/index-v3';
import SettingAPI from 'api/settings';
/**
* @desc 费用页面局部store，伴随page生命周期
*/
export default class HospitalConsultationPageStore extends BasePageStore {
    constructor() {
        const namespace = '@HospitalConsultation';
        const state = {
            quickList: [],
            selectedQuickItem: {},
            lastSelectedQuickItem: {},
            currentPatient: {},
            consultationConfig: {
                invitedDoctorCanAdvice: 0, // 受邀医生可开医嘱
                invitedDoctorCanMedical: 0, // 受邀医生可添加病历
                invitedDoctorCanDiagnosis: 0, // 受邀医生可下诊断
            },

            commonAdviceList: [{
                key: 'examination', // 检验
                table: [],
            },
            {
                key: 'inspect', // 检查
                table: [],
            },
            {
                key: 'medicine', // 药品
                table: [],
            },
            {
                key: 'nurse', // 护理
                table: [],
            },
            {
                key: 'other', // 其他
                table: [],
            }],
            initCommonAdviceList: false,
            curPatientHospitalInfo: null,
            departments: [], // 科室
        };
        super(namespace, state);
    }

    init() {
        this.fetchDepartments();
    }

    get quickList() {
        return this.state.quickList || [];
    }

    get selectedQuickItem() {
        return this.state.selectedQuickItem;
    }

    get lastSelectedQuickItem() {
        return this.state.lastSelectedQuickItem;
    }

    get selectedPatient() {
        return this.selectedQuickItem.patient || {};
    }

    get consultationConfig() {
        return this.state.consultationConfig || {};
    }

    get commonAdviceList() {
        return this.state.commonAdviceList;
    }

    get curPatientHospitalInfo() {
        return this.state.curPatientHospitalInfo;
    }

    get curDepartments() {
        return this.state.departments;
    }

    setCurDepartments(data) {
        this.state.departments = data;
    }

    setCurPatientHospitalInfo(data) {
        this.state.curPatientHospitalInfo = data;
    }

    setSelectedQuickItem(newSelectedItem) {
        // 记录上一次选中的
        this.state.lastSelectedQuickItem = this.state.selectedQuickItem;
        this.state.selectedQuickItem = newSelectedItem;
    }
    returnLastSelectedItem() {
        this.state.selectedQuickItem = this.state.lastSelectedQuickItem;
    }
    updatePatientInfo(data) {
        Object.assign(this.state.selectedQuickItem.patient, data);
    }

    updateQuickItem(id, data) {
        const target = this.state.quickList.find((it) => it.id === id);
        if (target) {
            Object.assign(target, data);
        }
        return target;
    }

    clearQuickList() {
        this.state.quickList = [];
    }

    setQuickList(data, isConcat = false) {
        if (isConcat) {
            this.state.quickList = this.state.quickList.concat(data);
        } else {
            this.state.quickList = data;
        }
    }

    async initConsultationConfig() {
        const { data } = await propertyAPI.getV3('clinicBasic.hospital.consultation', 'clinic');
        this.state.consultationConfig = data;
    }

    async fetchCommonAdviceList(clinicId) {
        /**
         * 切换患者病区不同，对应的推荐项目的药房不同，需要重新获取
         */
        // if (this.initCommonAdviceList) {
        //     return false;
        // }
        try {
            await Promise.all([
                this.fetchCommonAdviceByType(clinicId,[{
                    type: GoodsTypeEnum.EXAMINATION, subType: [GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect],
                }], 'examination'),
                this.fetchCommonAdviceByType(clinicId,[{
                    type: GoodsTypeEnum.EXAMINATION, subType: [GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test],
                }], 'inspect'),
                this.fetchCommonAdviceByType(clinicId,[{
                    type: GoodsTypeEnum.MEDICINE, subType: [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine],
                }], 'medicine'),
                this.fetchCommonAdviceByType(clinicId, [{ type: GoodsTypeEnum.NURSE },{ type: GoodsTypeEnum.TREATMENT }],'nurse'),
                this.fetchCommonAdviceByType(clinicId, [{ type: GoodsTypeEnum.LEAVE_HOSPITAL }, { type: GoodsTypeEnum.CONSULTATION }],'other'),
            ]);
            this.initCommonAdviceList = true;
        } catch (e) {
            console.log(e);
        }

    }

    async fetchCommonAdviceByType(clinicId, jsonType ,name) {
        let resDate = [];
        try {
            const params = {
                clinicId,
                wardAreaId: this.currentPatientWardId,
                key: '',
                jsonType,
                cMSpec: '',
                sex: '',
                age: {},
                offset: 0,
                limit: 15,
                withDomainMedicine: 1,
            };
            const res = await GoodsV3API.searchGoods(params);
            resDate = res?.data?.list || [];
        } catch (e) {
            console.log(e);
        } finally {
            const commonAdviceItem = this.commonAdviceList.find((item) => {
                return item.key === name;
            });
            commonAdviceItem.table = resDate;
        }
    }

    async fetchDepartments() {
        try {
            const res = await SettingAPI.clinic.fetchClinicDepartments();
            const { data } = res.data;
            this.state.departments = data?.rows || [];
        } catch (e) {
            console.error('科室信息获取错误\n', e);
        }
    }

}
