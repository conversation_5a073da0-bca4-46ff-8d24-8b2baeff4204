<template>
    <abc-container-right class="consultation-sidebar sidebar-container">
        <abc-tabs
            v-model="currentTab"
            :option="tabOptions"
            size="huge"
            disable-indicator
        ></abc-tabs>

        <div class="sidebar-content-wrapper">
            <component
                :is="currentComponent"
                v-if="patientOrderId"
                :key="currentTab"
                ref="content"
                :department-id="departmentId"
                :patient-order-id="patientOrderId"
                :patient-info="patientInfo"
                :default-tab="currentTab === 3 ? 1 : 2"
                :show-m-p-filter="false"
                disabled-operate
            ></component>
        </div>
    </abc-container-right>
</template>

<script>
    import EmrEditorSuggestDiagnosis from '@/views-hospital/medical-prescription/components/emr-editor/suggest-components/emr-editor-suggest-diagnosis.vue';
    import EmrEditorSuggestPrescriptions from '@/views-hospital/medical-prescription/components/emr-editor/suggest-components/emr-editor-suggest-prescriptions.vue';
    import EmrEditorSuggestRecords from '@/views-hospital/medical-prescription/components/emr-editor/suggest-components/emr-editor-suggest-records.vue';

    export default {
        name: 'EmrEditorSuggest',
        props: {
            departmentId: {
                type: String,
                default: '',
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            patientInfo: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                tabOptions: [],
                currentTab: 0,
                catalogueId: '',
            };
        },
        computed: {
            currentComponent() {
                return this.tabOptions.find((tab) => tab.value === this.currentTab)?.component ?? null;
            },
        },
        mounted() {
            this.initTabOptions();
        },
        methods: {
            handleShowEmrEditor(templateId) {
                this.catalogueId = templateId;
            },
            handleClose() {
                this.catalogueId = '';
            },
            handleConfirm(content) {
                this.catalogueId = '';
                this.$emit('confirm', content);
            },
            handleSavePersonalTemplate(template) {
                this.$emit('save-personal-template', template);
            },
            initTabOptions() {
                this.tabOptions = [
                    {
                        label: '诊断',
                        value: 1,
                        component: EmrEditorSuggestDiagnosis,
                    },
                    {
                        label: '医嘱',
                        value: 2,
                        component: EmrEditorSuggestPrescriptions,
                    },
                    {
                        label: '检验',
                        value: 3,
                        component: EmrEditorSuggestRecords,
                    },
                    {
                        label: '检查',
                        value: 4,
                        component: EmrEditorSuggestRecords,
                    },
                ];

                this.currentTab = this.tabOptions[0].value;
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/theme.scss";

    .consultation-sidebar {
        .emr-editor-suggest-prescriptions .prescriptions__item,
        .emr-editor__inspect-item-wrapper .inspect-item__header,
        .emr-editor__exam-item-wrapper .inspect-item__header,
        .emr-editor-suggest-diagnosis .diagnosis__item {
            height: 41px;
            padding: 10px 12px;
        }
    }
</style>
