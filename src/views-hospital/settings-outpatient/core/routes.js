import Index from '../index.vue';
import {
    MODULE_ID_MAP, RouterScope,
} from 'utils/constants';


// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

export default {
    path: 'outpatient-settings',
    name: '@outpatient-settings',
    component: Index,
    meta: {
        name: '门诊',
        needAuth: true,
        moduleId: MODULE_ID_MAP.outpatientSetting,
        pageAsyncClass: PageAsync,
        scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
    },
    redirect: {
        name: 'prescriptiontemplates',
    },
    children: [

    ],
};
