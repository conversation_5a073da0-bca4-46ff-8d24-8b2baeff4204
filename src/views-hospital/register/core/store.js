import BasePageStore from '@/core/page/store.js';
import PatientOrderAPI from 'api/hospital/patient-order';

/**
* @desc 费用页面局部store，伴随page生命周期
*/
export default class HospitalRegisterPageStore extends BasePageStore {
    constructor() {
        const namespace = '@HospitalRegister';
        const state = {
            quickList: [],
            selectedQuickItem: {},
            recordList: [],
        };
        super(namespace, state);
    }

    get selectedQuickItem() {
        return this.state.selectedQuickItem || {};
    }

    get quickList() {
        return this.state.quickList || [];
    }

    get recordList() {
        return this.state.recordList || [];
    }

    init() {
    }

    async fetchRecordList(patientOrderId) {
        try {
            const { data } = await PatientOrderAPI.getHospitalLog(patientOrderId);
            this.state.recordList = data?.rows ?? [];
        } catch (e) {
            console.log('fetchRecordList Error');
        }
    }

    setSelectedQuickItem(newSelectedItem) {
        this.state.selectedQuickItem = newSelectedItem;
    }

    setScrollParams(params) {
        Object.assign(this.state.scrollParams, params);
    }

    setQuickList(list) {
        this.state.quickList = list || [];
    }

}
