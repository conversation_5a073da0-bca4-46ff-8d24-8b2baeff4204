<template>
    <abc-dialog
        v-model="dialogVisible"
        title="样本核收"
        :auto-focus="false"
        size="xlarge"
    >
        <abc-layout v-abc-loading="loading" preset="dialog-table">
            <abc-layout-header>
                <abc-form label-position="left" :label-width="84" item-no-margin>
                    <abc-form-item>
                        <abc-flex justify="space-between">
                            <abc-autocomplete
                                v-model.trim="code"
                                :width="366"
                                :delay-time="0"
                                :async-fetch="true"
                                :fetch-suggestions="fetchSuggestions"
                                :max-length="20"
                                :auto-focus-first="true"
                                inner-width="450px"
                                placeholder="输入样本条码"
                                clearable
                                @focus="isFocus = true"
                                @blur="isFocus = false"
                                @clear="handleClear"
                                @enterEvent="handleSelect"
                            >
                                <abc-icon slot="prepend" icon="n-add-line-medium"></abc-icon>
                                <template slot="suggestion-header">
                                    <div class="verify-suggestion-title">
                                        <span class="verify-suggestion-order">申请单号</span>
                                        <span class="verify-suggestion-name">姓名</span>
                                        <span class="verify-suggestion-sex">性别</span>
                                        <span class="verify-suggestion-age">年龄</span>
                                        <span class="verify-suggestion-date">申请日期</span>
                                    </div>
                                </template>

                                <template slot="suggestions" slot-scope="props">
                                    <div
                                        class="suggestions-item"
                                        :class="{ selected: props.index === props.currentIndex }"
                                        @click="handleSelect(props.suggestion)"
                                    >
                                        <div class="verify-suggestion-order">
                                            {{ props.suggestion.orderNo }}
                                        </div>
                                        <div class="verify-suggestion-name">
                                            {{ props.suggestion.patient ? props.suggestion.patient.name : '' }}
                                        </div>
                                        <div class="verify-suggestion-sex">
                                            {{ props.suggestion.patient ? props.suggestion.patient.sex : '' }}
                                        </div>
                                        <div class="verify-suggestion-age">
                                            {{ props.suggestion.patient && props.suggestion.patient.age ? props.suggestion.patient.age.year : '' }}
                                        </div>
                                        <div class="verify-suggestion-date">
                                            {{ formatDate(props.suggestion.created, 'YYYY-MM-DD HH:mm') }}
                                        </div>
                                    </div>
                                </template>
                            </abc-autocomplete>

                            <abc-select
                                v-model="operatorId"
                                placeholder="核收人"
                                :width="183"
                            >
                                <abc-option
                                    v-for="(o, key) in operatorList"
                                    :key="key"
                                    :value="o.employeeId"
                                    :label="o.employeeName"
                                ></abc-option>
                            </abc-select>
                        </abc-flex>
                    </abc-form-item>
                </abc-form>
            </abc-layout-header>
            <abc-layout-content>
                <abc-table
                    :render-config="renderConfig"
                    :data-list="tableData"
                    :body-style="{
                        minHeight: '320px', height: '320px'
                    }"
                    :show-checked="false"
                    hidden-table-header
                    support-delete-tr
                    :empty-show-icon="false"
                    empty-content=""
                    :custom-tr-class="() => 'list-item-wrapper'"
                    @delete-tr="handleDelete"
                >
                    <template #table-content-empty>
                        <abc-content-empty value="扫条形码，批量核收" top="0" size="small">
                            <abc-icon slot="icon" :size="56" icon="s-emptyIcon-barcode"></abc-icon>
                        </abc-content-empty>
                    </template>
                    <template #name="{ trData: item }">
                        <abc-table-cell>
                            <abc-text>
                                {{ item.name }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #orderNo="{ trData: item }">
                        <abc-table-cell>
                            <abc-text>
                                {{ item.orderNo }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #sampleType="{ trData: item }">
                        <abc-table-cell>
                            <abc-text>
                                {{ item.sampleType }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #samplePipe="{ trData: item }">
                        <abc-table-cell v-if="item.samplePipe">
                            <img :src="item.samplePipe.color" style="width: 14px; height: 14px;" alt="" />
                            <abc-text>
                                {{ item.samplePipe.code }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #examinationName="{ trData: item }">
                        <abc-table-cell>
                            <abc-text>
                                {{ item.examinationName }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #sampleStatus="{ trData: item }">
                        <abc-table-cell>
                            <abc-text>
                                {{ getStatusName(item.sampleStatus) }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #sampleStatusOperate="{ trData: item }">
                        <abc-table-cell
                            v-if="checkIsNotFinish(item.sampleStatus)"
                            :class="{
                                'not-checked-operator': !item.checked
                            }"
                        >
                            <abc-checkbox
                                v-model="item.checked"
                                @change="handleStatusChange($event, item)"
                            >
                                <abc-text theme="primary">
                                    拒收
                                </abc-text>
                            </abc-checkbox>

                            <abc-popover
                                v-if="item.rejectReason.list.length || item.rejectReason.remark"
                                placement="top-start"
                                trigger="hover"
                                theme="yellow"
                                style="display: inline-block;"
                            >
                                <abc-icon
                                    slot="reference"
                                    icon="jinggao"
                                    size="14"
                                    color="#f93"
                                ></abc-icon>

                                <div>
                                    <div v-if="item.rejectReason.list.length">
                                        拒收理由：{{ item.rejectReason.list.join('，') }}
                                    </div>

                                    <div v-if="item.rejectReason.remark">
                                        备注：{{ item.rejectReason.remark }}
                                    </div>
                                </div>
                            </abc-popover>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-layout-content>
        </abc-layout>

        <abc-flex slot="footer" justify="space-between" align="middle">
            <abc-space>
                <abc-text theme="gray">
                    样本数量
                    <abc-text theme="black">
                        {{ tableData.length }}
                    </abc-text>
                    个
                </abc-text>
            </abc-space>

            <abc-space>
                <abc-button :disabled="!finishBtnVisible" @click="handleFinish">
                    提交
                </abc-button>

                <abc-button :disabled="isDisableCancelBtn" @click="handleCancelFinish">
                    取消核收
                </abc-button>

                <abc-button type="blank" @click="handleCancel">
                    取消
                </abc-button>
            </abc-space>
        </abc-flex>

        <sample-refuse-dialog
            v-if="sampleRefuseVisible"
            v-model="sampleRefuseVisible"
            @cancel="handleRefuseCancel"
            @confirm="handleRefuseConfirm"
        >
        </sample-refuse-dialog>
    </abc-dialog>
</template>

<script>
    import SampleRefuseDialog from './sample-refuse-dialog.vue';
    import { throttle } from '@/utils/lodash';
    import ExaminationApi from '@/api/hospital/examination/index';
    import {
        EnumVerify, EnumOperateWay, SEARCH_MAX_COUNT, VERIFY_DIALOG_TABLE_LIST,
    } from '@/views-hospital/sample-verify/utils/constant';
    import {
        pick1, isEqual,
    } from '@/utils/lodash';
    import { mapGetters } from 'vuex';
    import { formatDate } from '@abc/utils-date';
    import Clone from 'utils/clone';
    import { EXAMINATION_STATUS } from '@/views/examination/util/constants';


    export default {
        components: {
            SampleRefuseDialog,
        },

        model: {
            prop: 'value',
            event: 'change',
        },

        props: {
            value: {
                type: Boolean,
                default: false,
            },
            samplePatientFormId: {
                type: String,
                default: '',
            },
            verifyWay: {
                type: String,
                default: () => EnumOperateWay.scan,
            },
            operatorList: {
                type: Array,
                default: () => [],
            },
        },

        data() {
            return {
                tableData: [], // 核收表格数据
                loading: false, // 表格loading状态
                sampleRefuseVisible: false, // 拒收弹窗

                EnumVerify, // 核收的枚举状态值

                code: '', // input 样本条码值
                way: EnumOperateWay.scan,

                currentOperateSample: null, // 核收时操作的某一个样本
                operatorId: '', // 核收人

                cacheTableData: [],

                renderConfig: {
                    list: VERIFY_DIALOG_TABLE_LIST,
                },

                isFocus: false,
            };
        },

        computed: {
            ...mapGetters(['userInfo']),

            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('change', val);
                },
            },

            finishBtnVisible() {
                return this.tableData.some((o) => o.sampleStatus === EnumVerify.wait) || !isEqual(this.cacheTableData, this.tableData);
            },

            isDisableCancelBtn() {
                return !this.tableData.some(
                    (o) => o.sampleStatus === EnumVerify.yes ||
                        !this.checkIsNotFinishExamination(o.status),
                );
            },
        },

        watch: {
            samplePatientFormId: {
                immediate: true,
                handler(val) {
                    if (!val) return;

                    this.way = this.verifyWay;
                    this.fetchDetail();
                },
            },

            userInfo: {
                immediate: true,
                handler(val) {
                    this.operatorId = val?.id ?? '';
                },
            },
        },

        created() {
            this._throttleFetchDetail = throttle(this.fetchDetail, 250, true, true);
        },

        methods: {
            formatDate,

            handleClear() {
                this.code = '';
            },

            handleSelect(data) {
                this.handleDetailData(data);
                this.code = '';
            },

            async fetchSuggestions(key, callback) {
                const { rows = [] } = await ExaminationApi.getExaminationRecord({
                    limit: SEARCH_MAX_COUNT,
                    offset: 0,
                    key,
                    sampleStatus: [EnumVerify.wait, EnumVerify.yes, EnumVerify.no],
                });

                return callback(rows);
            },

            // 获取患者检验单详情
            async fetchDetail() {
                if (this.isFocus) {
                    return;
                }

                try {
                    this.loading = true;

                    if (this.way === EnumOperateWay.scan) {
                        const { rows = [] } = await ExaminationApi.getExaminationRecord({
                            key: this.samplePatientFormId,
                            limit: SEARCH_MAX_COUNT,
                            offset: 0,
                            sampleStatus: [EnumVerify.wait, EnumVerify.yes, EnumVerify.no],
                        });

                        if (!rows.length) {
                            this.$Toast({
                                message: '当前条形码非待核收码',
                                type: 'error',
                            });

                            return;
                        }
                        this.handleDetailData(rows[0]);
                    } else {
                        const res = await ExaminationApi.loadCollectRecordById(this.samplePatientFormId);

                        this.handleDetailData(res);
                    }
                } catch (e) {
                    this.$Toast({
                        message: e.message || e,
                        type: 'error',
                    });
                } finally {
                    this.loading = false;
                }
            },

            handleDetailData(data) {
                if (!data) return;

                const item = this.tableData.find((o) => o.id === data.id);
                if (item) {
                    this.$Toast({
                        type: 'info',
                        message: '当前样本已添加',
                    });

                    return;
                }

                const res = {
                    name: data.patient?.name ?? '',
                    orderNo: data.orderNo ?? '',
                    sampleType: data.sampleType ?? '',
                    samplePipe: data.samplePipe ?? null,
                    examinationName: data.examinationName ?? '',
                    rejectReason: data.sampleStatus === EnumVerify.no ? data.rejectReason : {
                        list: [],
                        remark: '',
                    },
                    sampleStatus: data.sampleStatus ?? EnumVerify.wait,
                    id: data.id ?? '',
                    checked: data.sampleStatus === EnumVerify.no,
                };

                this.tableData.push(res);
                this.cacheTableData.push(Clone(res));
            },

            handleStatusChange(val, item) {
                if (val) {
                    this.currentOperateSample = item;

                    this.sampleRefuseVisible = true;

                    return;
                }

                item.rejectReason = {
                    list: [],
                    remark: '',
                };
            },

            handleRefuseCancel() {
                this.currentOperateSample.checked = false;

                this.currentOperateSample.rejectReason = {
                    list: [],
                    remark: '',
                };
            },

            handleRefuseConfirm(payload) {
                this.currentOperateSample.rejectReason = { ...payload };

                this.sampleRefuseVisible = false;
            },

            handleFinish() {
                this.$emit('finish', {
                    operatorId: this.operatorId,
                    list: this.tableData
                        .map((o) => {
                            return {
                                ...pick1(o, ['id', 'rejectReason']),
                                sampleStatus: o.checked ? EnumVerify.no : EnumVerify.yes,
                            };
                        }),
                });
            },

            // 取消核收
            handleCancelFinish() {
                this.$emit('cancel', {
                    operatorId: this.operatorId,
                    list: this.tableData
                        .filter(
                            (o) => o.sampleStatus === EnumVerify.yes &&
                                this.checkIsNotFinishExamination(o.status),
                        )
                        .map((o) => {
                            return {
                                ...pick1(o, ['id']),
                                sampleStatus: EnumVerify.wait,
                            };
                        }),
                });
            },

            handleCancel() {
                this.dialogVisible = false;
            },

            handleDelete(index) {
                this.tableData.splice(index, 1);
                this.tableData = [...this.tableData];
                this.cacheTableData.splice(index, 1);
            },

            getStatusName(status) {
                const map = {
                    [EnumVerify.wait]: '待核收',
                    [EnumVerify.no]: '已拒收',
                    [EnumVerify.yes]: '已核收',
                };

                return map[status] || '';
            },

            checkIsNotFinish(status) {
                return +status !== EnumVerify.yes;
            },

            checkIsNotFinishExamination(status) {
                return ![EXAMINATION_STATUS.CHECKED, EXAMINATION_STATUS.WAIT_CHECK].includes(+status);
            },
        },
    };
</script>

<style scoped lang="scss">
.not-checked-operator {
    visibility: hidden;
}

.list-item-wrapper {
    &:hover {
        .not-checked-operator {
            visibility: visible;
        }
    }
}

.verify-suggestion-title {
    display: flex;
    align-items: center;
    height: 28px;
    padding: 0 12px 0 10px;
    font-size: 12px;
    line-height: 28px;
    color: #626d77;
    background-color: $P5;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.verify-suggestion-order {
    width: 136px;
}

.verify-suggestion-name {
    width: 80px;
}

.verify-suggestion-sex {
    width: 40px;
}

.verify-suggestion-age {
    width: 40px;
}

.verify-suggestion-date {
    width: 120px;
}
</style>
