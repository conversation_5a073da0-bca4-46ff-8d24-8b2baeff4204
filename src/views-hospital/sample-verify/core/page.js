import { BasePage } from '@/core/index.js';
import PageStore from './store.js';
import AbcSocket from 'views/common/single-socket';
import * as business from 'MfFeEngine/business';

export default class HospitalSampleVerifyPage extends BasePage {
    name = '@HospitalSampleVerify';

    constructor() {
        super({
            store: new PageStore(),
        });
    }

    async init() {
        await super.init();
        const { socket } = AbcSocket.getSocket();
        this.AcceptanceService = new business.AcceptanceService(socket);
        await this.AcceptanceService.start();
    }

    destroyed() {
        super.destroyed();
        if (this.AcceptanceService) {
            this.AcceptanceService?.stop();
            this.AcceptanceService = null;
        }
    }
}
