<template>
    <!-- 主要用药情况 -->
    <div class="ph-add-table-wrapper">
        <ph-table-form :form-items="formItems"></ph-table-form>
        <abc-table custom :support-delete-tr="!isDisabled" :disabled="isDisabled">
            <abc-table-header>
                <abc-table-td>药物名称</abc-table-td>
                <abc-table-td>
                    用法用量
                </abc-table-td>
                <abc-table-td :width="100">
                    用药时间
                </abc-table-td>
                <abc-table-td :width="100">
                    服药依从性
                </abc-table-td>
            </abc-table-header>
            <abc-table-body>
                <abc-table-tr v-for="item in dataList" :key="item.no" @delete-tr="handleDeleteItem(item.keyId)">
                    <abc-table-td>{{ item.medicineName }} {{ OptionsEnum.drugPurchaseRouteDictionary[item.buyingWay] }}</abc-table-td>
                    <abc-table-td>
                        {{ OptionsEnum.drugUsageDictionary[item.usage] }}
                        {{ item.usageRemark }}
                        {{ OptionsEnum.usageFrequencyDictionary[item.freq] }}
                        {{ item.dosage }}
                        {{ OptionsEnum.drugDosageDictionary[item.dosageUnit] }}
                        {{ item.dosageRemark }}
                    </abc-table-td>
                    <abc-table-td :width="100">
                        {{ item.medicationTime }}
                    </abc-table-td>
                    <abc-table-td :width="100">
                        {{ OptionsEnum.hcDrugComplianceDictionary[item.medicationCompliance] }}
                    </abc-table-td>
                </abc-table-tr>
            </abc-table-body>
            <abc-table-footer>
                <abc-table-td custom-td>
                    <ph-table-add-entry
                        title="新建主要用药情况"
                        type="pharmacyCondition"
                        :disabled="isDisabled"
                        :validate="validate"
                        @setDataList="onSetDataList"
                    >
                        <abc-form
                            ref="addForm"
                            slot-scope="{ formData }"
                            item-no-margin
                        >
                            <abc-space direction="vertical" size="middle">
                                <abc-form-item label="药物名称">
                                    <abc-space>
                                        <abc-input
                                            v-model="formData.medicineName"
                                            :width="240"
                                            placeholder="药物名称"
                                        ></abc-input>
                                        <abc-select
                                            v-model="formData.buyingWay"
                                            :width="164"
                                            placeholder="购买途径"
                                        >
                                            <abc-option
                                                v-for="(item, index) in swapToOptions(OptionsEnum.drugPurchaseRouteDictionary)"
                                                :key="index"
                                                :value="item.value"
                                                :label="item.label"
                                            >
                                            </abc-option>
                                        </abc-select>
                                    </abc-space>
                                </abc-form-item>

                                <abc-form-item label="用法">
                                    <abc-space>
                                        <abc-select
                                            v-model="formData.usage"
                                            :width="240"
                                            placeholder="用法"
                                        >
                                            <abc-option
                                                v-for="(item, index) in swapToOptions(OptionsEnum.drugUsageDictionary)"
                                                :key="index"
                                                :value="item.value"
                                                :label="item.label"
                                            >
                                            </abc-option>
                                        </abc-select>
                                        <abc-input
                                            v-model="formData.usageRemark"
                                            :width="164"
                                            placeholder="用法备注"
                                        ></abc-input>
                                    </abc-space>
                                </abc-form-item>

                                <abc-form-item label="用量">
                                    <abc-space>
                                        <abc-select
                                            v-model="formData.freq"
                                            :width="97"
                                            placeholder="频次"
                                        >
                                            <abc-option
                                                v-for="(item, index) in swapToOptions(OptionsEnum.usageFrequencyDictionary)"
                                                :key="index"
                                                :value="item.value"
                                                :label="item.label"
                                            >
                                            </abc-option>
                                        </abc-select>
                                        <abc-input
                                            v-model="formData.dosage"
                                            :width="97"
                                            placeholder="用量"
                                        ></abc-input>
                                        <abc-select
                                            v-model="formData.dosageUnit"
                                            :width="97"
                                            placeholder="用量单位"
                                        >
                                            <abc-option
                                                v-for="(item, index) in swapToOptions(OptionsEnum.drugDosageDictionary)"
                                                :key="index"
                                                :value="item.value"
                                                :label="item.label"
                                            >
                                            </abc-option>
                                        </abc-select>
                                        <abc-input
                                            v-model="formData.dosageRemark"
                                            :width="97"
                                            placeholder="用量备注"
                                        ></abc-input>
                                    </abc-space>
                                </abc-form-item>

                                <abc-form-item label="用药时间">
                                    <abc-input
                                        v-model="formData.medicationTime"
                                        :width="412"
                                        placeholder="用药时间"
                                    ></abc-input>
                                </abc-form-item>

                                <abc-form-item label="服药依从性">
                                    <abc-select
                                        v-model="formData.medicationCompliance"
                                        :width="412"
                                        placeholder="服药依从性"
                                    >
                                        <abc-option
                                            v-for="(item, index) in swapToOptions(OptionsEnum.hcDrugComplianceDictionary)"
                                            :key="index"
                                            :value="item.value"
                                            :label="item.label"
                                        >
                                        </abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-space>
                        </abc-form>
                    </ph-table-add-entry>
                </abc-table-td>
            </abc-table-footer>
        </abc-table>
    </div>
</template>

<script>
    import {
        FORM_ITEM_TYPE,
        PUBLIC_HEALTHY_FORM_KEYS,
        PUBLIC_HEALTHY_OPTION,
    } from '@/views-hospital/inspect-diagnosis/utils/public-healthy-constant';

    const WAY_ENUM = {
        0: '1次/日',
        1: '2次/日',
        2: '3次/日',
    };
    const WHEN_ENUM = {
        0: '规律',
        1: '间断',
        2: '不服药',
    };
    import themeStyle from 'styles/theme.module.scss';
    import { PHTableAddEntry } from './components';
    import { createGUID } from '@/utils';
    import PhTableForm
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/public-healthy/components/ph-table-form.vue';
    import UpdateFormMixin
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/public-healthy/update-form';
    import {
        OptionsEnum, swapToOptions,
    } from 'views/physical-examination/integrated/public-health-sync/region/qingdao/muhua/options-enum';

    /**
     * {
     *     "valueObject": {
     *         "items": [
     *             {
     *                 "medicineName": "",
     *                 "buyingWay": "",
     *                 "usage": "",
     *                 "usageRemark": "",
     *                 "freq": "",
     *                 "dosage": "",
     *                 "dosageUnit": "",
     *                 "dosageRemark": "",
     *                 "medicationTime": "",
     *                 "medicationCompliance": ""
     *             }
     *         ],
     *         "value": null
     *     }
     * }
     *
     * 测试数据
     * drugDosageCode: 7
     * drugDosageInput: "1"
     * drugDosageOther: "用量备注"
     * drugName: "优泌林70\\30注射液"
     * drugPurchaseRouteCode: 3
     * drugUsageCode: 1
     * drugUsageFrequencyCode: 7
     * drugUsageOther: "120"
     * drugUseTime: "1"
     * hcDrugComplianceCode: 3
     */

    export default {
        name: 'PHPharmacyConditionPlus',
        components: {
            PhTableForm,
            'ph-table-add-entry': PHTableAddEntry,
        },
        mixins: [UpdateFormMixin],
        props: {
            form: {
                type: Object,
                default: () => ({}),
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                themeStyle,
                WAY_ENUM,
                WHEN_ENUM,
            };
        },

        computed: {
            OptionsEnum() {
                return OptionsEnum;
            },
            curForm: {
                get() {
                    return this.form;
                },

                set(v) {
                    this.$emit('update:form', v);
                },
            },
            formItems() {
                return [
                    [
                        {
                            formItemType: FORM_ITEM_TYPE.radioGroup,
                            options: PUBLIC_HEALTHY_OPTION.occupationalHazardFactors,
                            value: this.curForm[PUBLIC_HEALTHY_FORM_KEYS.pharmacyCondition].value,
                            valueNonEmpty: () => {
                                return !((this.curForm[PUBLIC_HEALTHY_FORM_KEYS.pharmacyCondition].value === 2 &&
                                    this.curForm[PUBLIC_HEALTHY_FORM_KEYS.pharmacyCondition].items.length === 0) || this.curForm[PUBLIC_HEALTHY_FORM_KEYS.pharmacyCondition].value === '');
                            },
                            on: {
                                input: (v) => {
                                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.pharmacyCondition, 'value'], v);
                                },
                                change: (v) => {
                                    if (Number(v) === 1) {
                                        this.dataList = [];
                                    }
                                },
                            },
                            disabled: this.disabled,
                        },
                    ],
                ];
            },
            isDisabled() {
                return this.disabled || Number(this.curForm[PUBLIC_HEALTHY_FORM_KEYS.pharmacyCondition].value) !== 2;
            },
            dataList: {
                get() {
                    this.curForm[PUBLIC_HEALTHY_FORM_KEYS.pharmacyCondition].items.forEach((item) => {
                        if (!item.keyId) {
                            item.keyId = createGUID();
                        }
                    });
                    return this.curForm[PUBLIC_HEALTHY_FORM_KEYS.pharmacyCondition].items || [];
                },
                set(v) {
                    this.updateForm([PUBLIC_HEALTHY_FORM_KEYS.pharmacyCondition, 'items'], v);
                },
            },
        },
        methods: {
            onSetDataList(item) {
                this.dataList.push(item);
            },
            handleDeleteItem(keyId) {
                this.dataList = this.dataList.filter((item) => {return item.keyId !== keyId;});
            },
            swapToOptions,

            validate() {
                return new Promise((resolve) => {
                    this.$refs.addForm.validate((res) => {
                        resolve(res);
                    });
                });
            },
        },
    };
</script>
