<template>
    <div class="physical-examination-card-wrapper">
        <div class="physical-examination-card-left">
            <abc-image :src="peSheetDetail.patientImageUrl || defaultUserAvatarUrl"></abc-image>
        </div>

        <div class="physical-examination-card-center">
            <div class="physical-examination-card-item">
                <span>个检/团检</span>
                <span>
                    {{ peSheetDetail.name }}
                </span>
            </div>
            <div class="physical-examination-card-item">
                <span>身份证</span>
                <span>
                    {{ patient.idCard }}
                </span>
            </div>
            <div class="physical-examination-card-item">
                <span>婚姻状况</span>
                <span>
                    {{ maritalStr }}
                </span>
            </div>
            <div class="physical-examination-card-item">
                <span>体检单号</span>
                <span>
                    {{ peSheetDetail.no }}
                </span>
            </div>
        </div>

        <div class="physical-examination-card-right">
            <div class="physical-examination-card-btn" @click="handleViewDetail">
                <span>查看详情</span>
                <abc-icon icon="Arrow_Rgiht"></abc-icon>
            </div>
        </div>

        <p-e-detail-dialog
            v-if="dialogVisible"
            ref="formDialog"
            v-model="dialogVisible"
            :order-id="peSheetDetail.id"
            @add-item="showOrderDialog = true"
        ></p-e-detail-dialog>

        <order-dialog
            :id="peSheetDetail.id"
            v-model="showOrderDialog"
            :order-type="peSheetDetail.type"
            :employee-list="employeeList"
            :business-type="peSheetDetail.businessType"
            @refresh="handleRefresh"
        ></order-dialog>
    </div>
</template>

<script>
    import { MaritalStatusLabel } from 'views/crm/constants.js';

    export default {
        name: 'PhysicalExaminationCard',

        components: {
            PEDetailDialog: () => import('views/physical-examination/integrated/check-up/components/form-dialog.vue'),
            OrderDialog: () => import('views/physical-examination/order/individual/components/order-dialog.vue'),
        },

        props: {
            peSheetDetail: {
                type: Object,
                default: () => ({}),
            },

            patient: {
                type: Object,
                default: () => ({}),
            },

            employeeList: {
                type: Array,
                default: () => [],
            },
        },

        data () {
            return {
                dialogVisible: false,
                showOrderDialog: false,
                defaultUserAvatarUrl: '//cd-cis-static-common.oss-cn-chengdu.aliyuncs.com/img/ph-default-user-avatar.png',
            };
        },

        computed: {
            maritalStr() {
                const { marital } = this.patient;
                return MaritalStatusLabel[marital];
            },
        },

        methods: {
            handleViewDetail() {
                if (!this.peSheetDetail.id) {
                    return;
                }
                this.dialogVisible = true;
            },

            handleRefresh() {
                if (this.dialogVisible) {
                    this.$refs.formDialog.fetchDetail();
                }
            },
        },
    };
</script>

<style lang="scss">
.physical-examination-card-wrapper {
    display: flex;
    gap: 20px;
    width: 100%;
    padding: 12px;
    background-color: #ffffff;
    border: 1px solid #e6eaee;
    border-radius: var(--abc-border-radius-small);

    .physical-examination-card-left {
        width: 80px;
    }

    .physical-examination-card-center {
        flex: 1;

        .physical-examination-card-item {
            display: inline-flex;
            width: 100%;
            line-height: 28px;

            >span:first-child {
                width: 80px;
                color: $T2;
            }

            >span:last-child {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .physical-examination-card-right {
        display: inline-flex;
        align-items: center;
        width: 80px;
        color: $T2;

        .physical-examination-card-btn {
            display: inline-flex;
            gap: 4px;
            align-items: center;
            cursor: pointer;
        }
    }
}
</style>
