<template>
    <header
        class="inspect-assist-header"
    >
        <abc-tabs-v2
            v-model="curTab"
            :option="tabItems"
            size="huge"
            disable-indicator
            :custom-gap="0.1"
            @change="v => $emit('change', v)"
        ></abc-tabs-v2>
    </header>
</template>

<script>
    import { INSPECT_EVENT_KEY } from '@/views-hospital/inspect-diagnosis/utils/constant.js';

    import CaichaoImg from '@/assets/images/hospital/caichao.png';
    import WCJImg from '@/assets/images/hospital/wcj-tab.png';
    import { INSPECT_TYPE } from '@/views-hospital/inspect-setting/utils/constant';


    export default {
        name: 'InspectAssistHeader',
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: {
                type: String,
                default: '影像',
            },
            // 是否为放射科
            isFangShe: {
                type: Boolean,
                default: false,
            },
            // 是否为彩超
            isVideoCamMode: {
                type: <PERSON>olean,
                default: false,
            },

            deviceType: {
                required: true,
            },
        },
        data() {
            return {
                historyReportTotal: 0,
            };
        },
        computed: {
            imageTotal() {
                return this.$abcPage.$store.state.imageListLength || 0;
            },
            tabItems() {
                return [
                    {
                        label: '历史报告' ,
                        value: '历史报告' ,
                        statisticsNumber: this.historyReportTotal || undefined,
                    },
                    this.isFangShe ? {
                        label: '影像' ,
                        value: '影像' ,
                        statisticsNumber: this.imageTotal || undefined,
                    } : (
                        this.isVideoCamMode ?
                            {
                                label: '影像采集',
                                value: '影像',
                                image: this.videoCamTabImage,
                            } : null
                    ),
                ].filter((item) => !!item);
            },

            videoCamTabImage() {
                switch (this.deviceType) {
                    case INSPECT_TYPE.CDU:
                        return CaichaoImg;
                    case INSPECT_TYPE.GASTROSCOPE:
                        return WCJImg;
                    default:
                        return CaichaoImg;
                }
            },

            curTab: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
        },
        mounted() {
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.UPDATE_HISTORY_COUNT, (v) => {
                this.historyReportTotal = v;
            }, this);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
    };
</script>

<style lang="scss" scoped>
.inspect-assist-header {
    display: flex;
    width: 100%;
    height: 56px;
    padding: 0 4px;
    border-bottom: 1px solid var(--abc-color-P6);
}
</style>
