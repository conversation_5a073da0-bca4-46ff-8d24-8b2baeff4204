<template>
    <div v-abc-loading:page="loading" class="inspect-main-wrapper">
        <!-- header -->
        <abc-container-center-top-head>
            <inspect-header
                :key="reportId"
                :handle-save-report="handleSaveReport"
                :is-send-to-center-organ="isSendToCenterOrgan"
                :is-public-healthy-project="isPublicHealthyProject"
                :is-report-update="isReportUpdate"
            ></inspect-header>
        </abc-container-center-top-head>

        <!-- 检查报告内容 -->
        <abc-container-center-main-content class="inspect-diagnosis-main-content">
            <abc-flex :key="reportId" vertical :gap="16">
                <!--人员信息-->
                <inspect-report-person
                    ref="header"
                    :patient="postData.patient"
                    :selected-patient="selectedPatient"
                    :checker-id.sync="postData.checkerId"
                    :tester-id.sync="postData.testerId"
                    :editable="editable"
                    :is-send-to-center-organ="isSendToCenterOrgan"
                    :doctor-list="operateDoctorList"
                    :report-is-finished="reportIsFinished"
                    @change-patient="changePatientInfo"
                ></inspect-report-person>

                <!--多报告tab-->
                <inspect-multiple-report-tab
                    v-if="isRenderMultipleTab && (editable || reportTabs.length > 1)"
                    :value="curReportIndex"
                    :tabs="reportTabs"
                    :editable="editable"
                    @add="handleAddReport"
                    @remove="handleRemoveReport"
                    @change="handleChangeReport"
                ></inspect-multiple-report-tab>

                <!--体检卡片-->
                <template v-if="isPhysicalExamination">
                    <physical-examination-card
                        :pe-sheet-detail="postData.peSheetSimpleView"
                        :patient="postData.patient"
                        :employee-list="employeeList"
                    ></physical-examination-card>
                </template>

                <!--公卫总检项目-->
                <template v-if="isPublicHealthyProject">
                    <abc-form ref="publicHealthyForm">
                        <public-healthy-inspect-report
                            :key="$route.params.id"
                            :disabled="!editable"
                            :form.sync="postData.publicHealthyForm"
                            :public-healthy-init-form="publicHealthyInitForm"
                            :ph-item-with-group-id-list="phItemWithGroupIdList"
                            :principal-doctor-id.sync="postData.examinationSheetReport.principalDoctorId"
                            :principal-doctor-sign.sync="postData.examinationSheetReport.principalDoctorSign"
                            :business-time.sync="postData.peSheetSimpleView.businessTime"
                            :patient-order-id="postData.patientOrderId"
                            :patient="postData.patient"
                        ></public-healthy-inspect-report>
                    </abc-form>
                </template>

                <template v-else>
                    <!--检查报告头部信息-->
                    <inspect-report-description
                        type="header"
                        :device-model-desc.sync="curReport.deviceModelDesc"
                        :doctor-list="operateDoctorList"
                        :is-send-to-center-organ="isSendToCenterOrgan"
                        :editable="editable"
                        :is-need-apply-sheet="isNeedApplySheet"
                        :is-physical-examination="isPhysicalExamination"
                        :post-data.sync="postData"
                        :is-normal-report="isNormalReport"
                        :examination-sheet-report.sync="postData.examinationSheetReport"
                        :post-data-cache="postDataCache"
                        :edit-tag="editTag"
                    ></inspect-report-description>

                    <template v-if="showResult">
                        <!--检查影像-->
                        <inspect-report-file
                            v-if="isRenderImagePart"
                            :id="curReport.id"
                            :key="`image${ curReportIndex}`"
                            :image-list="curReport.imageFiles"
                            :editable="editable"
                            :patient="postData.patient"
                            :can-add="isGastroscopyReport ? editable && curReport.imageFiles.length < 8 : editable"
                            :device-type="postData.deviceType"
                            :need-position-suggestion="isGastroscopyReport"
                            :is-local="curReport.isLocal"
                            @add="handleAddImage"
                            @del="handleDeleteImage"
                        ></inspect-report-file>

                        <!--超声所见 / 检查所见 / 影像表现 / 老报告影像描述-->
                        <inspect-report-result
                            v-if="isRenderVideoDescription"
                            :key="`videoDescription${ curReportIndex}`"
                            v-model="curReport.videoDescription"
                            :title="videoDescriptionTitle"
                            component-type="textarea"
                            :editable="editable"
                            :need-template="!isNormalReport"
                            :default-height="40"
                            :device-type="postData.deviceType"
                            :data-cy="`inspect-report-result-${videoDescriptionTitle}`"
                            @update-inspect-result="handleUpdateInspectResult"
                        ></inspect-report-result>

                        <!--活检部位-->
                        <inspect-report-result
                            v-if="isGastroscopyReport"
                            :key="`inspectionSite${ curReportIndex}`"
                            v-model="curReport.inspectionSite"
                            title="活检部位"
                            component-type="input"
                            need-character-count
                            data-cy="inspect-report-result-活检部位"
                            :editable="editable"
                        ></inspect-report-result>

                        <!--医生建议-->
                        <inspect-report-result
                            v-if="isGastroscopyReport"
                            :key="`suggestion${ curReportIndex}`"
                            v-model="curReport.suggestion"
                            title="医生建议"
                            component-type="input"
                            need-character-count
                            data-cy="inspect-report-result-医生建议"
                            :editable="editable"
                        ></inspect-report-result>

                        <!--临床检查表格-->
                        <inspect-clinical-report-table
                            v-if="isRenderClinicalTable"
                            :editable="editable"
                            :items-value.sync="postData.itemsValue"
                        >
                            <template v-if="postData.attachments.length || editable" #table-footer>
                                <examination-file-uploader
                                    v-model="postData.attachments"
                                    :business-type="BusinessTypeEnum.INSPECT_REPORTS"
                                    :business-id="reportId"
                                    business-desc="报告附件"
                                    :patient-info="postData.patient"
                                    upload-description="报告附件支持图片、PDF格式"
                                    oss-filepath="inspect"
                                    :disable="!editable"
                                    :accept="accessAppend()"
                                    class="report-content__attachments-add-uploader"
                                >
                                </examination-file-uploader>
                            </template>
                        </inspect-clinical-report-table>

                        <!--一般检查表格-->
                        <inspect-normal-report-table
                            v-if="isRenderNormalTable"
                            :editable="editable"
                            :items-value.sync="postData.itemsValue"
                            :device-type="postData.deviceType"
                        >
                            <template v-if="postData.attachments.length || editable" #table-footer>
                                <examination-file-uploader
                                    v-model="postData.attachments"
                                    :business-type="BusinessTypeEnum.INSPECT_REPORTS"
                                    :business-id="reportId"
                                    business-desc="报告附件"
                                    :patient-info="postData.patient"
                                    upload-description="报告附件支持图片、PDF格式"
                                    oss-filepath="inspect"
                                    :disable="!editable"
                                    :accept="accessAppend()"
                                    class="report-content__attachments-add-uploader"
                                >
                                </examination-file-uploader>
                            </template>
                        </inspect-normal-report-table>

                        <!--诊断意见-->
                        <diagnosis-advice
                            :key="`diagnosisEntryItems${ curReportIndex}`"
                            :diagnosis-entry-items.sync="curReport.diagnosisEntryItems"
                            :diagnosis-flag.sync="curReport.diagnosisFlag"
                            :device-type="postData.deviceType"
                            :editable="editable"
                        ></diagnosis-advice>

                        <!--附件-->
                        <inspect-report-attachment
                            v-if="!isRenderClinicalTable && !isRenderNormalTable"
                            :editable="editable"
                            can-add
                            :report-id="reportId"
                            :attachments.sync="postData.attachments"
                        ></inspect-report-attachment>

                        <!--备注|兼容老报告-->
                        <examination-report-remark
                            v-if="isNormalReport"
                            v-model="postData.remark"
                            remark-data-cy="inspect-report-remark-input"
                            description-data-cy="inspect-report-remark-descriptions"
                            :editable="editable"
                        ></examination-report-remark>
                    </template>

                    <template v-else>
                        <abc-card>
                            <abc-flex align="center" justify="center" style="height: 430px;">
                                <abc-content-empty value="等待出报告中" top="0">
                                    <abc-icon slot="icon" icon="s-emptyIcon-inspect" :size="84"></abc-icon>
                                </abc-content-empty>
                            </abc-flex>
                        </abc-card>
                    </template>

                    <!--检查报告底部信息-->
                    <inspect-report-description
                        v-if="!isNormalReport"
                        ref="footer"
                        type="footer"
                        :doctor-list="operateDoctorList"
                        :is-send-to-center-organ="isSendToCenterOrgan"
                        :editable="editable"
                        :is-need-apply-sheet="isNeedApplySheet"
                        :is-physical-examination="isPhysicalExamination"
                        :post-data.sync="postData"
                        :examination-sheet-report.sync="postData.examinationSheetReport"
                        :post-data-cache="postDataCache"
                        :edit-tag="editTag"
                        :need-check="!!needCheck"
                    ></inspect-report-description>
                </template>
            </abc-flex>
        </abc-container-center-main-content>

        <!-- 侧边栏 -->
        <abc-container-right>
            <inspect-assist></inspect-assist>
        </abc-container-right>
    </div>
</template>

<script type="text/ecmascript-6">
    import InspectHeader from '@/views-hospital/inspect-diagnosis/components/inspect-report/inspect-header/index.vue';
    import InspectAssist from '../components/inspect-assist/index.vue';
    import InspectReportPerson
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/inspect-report-person/index.vue';
    import { mapGetters } from 'vuex';
    import {
        EXAMINATION_STATUS,
        INSPECT_EVENT_KEY,
        INSPECT_REPORT_TYPE,
    } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import { EXAM_SHEET_TYPE } from 'views/examination/util/constants';
    import PhysicalExaminationCard
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/components/physical-examination-card.vue';
    import { BusinessType } from '@/views-hospital/sample-collection/utils/constant';
    import PublicHealthyInspectReport
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/public-healthy/index.vue';
    import {
        dcm4cheeType, INSPECT_TYPE,
    } from '@/views-hospital/inspect-setting/utils/constant';
    import ExaminationAPI from 'api/examination';
    import { inspectDetailAdapter } from '@/views-hospital/inspect-diagnosis/utils/adapter';
    import { publicHealthyForm } from '@/views-hospital/inspect-diagnosis/utils/public-healthy-constant';
    import {
        covertItems,
        covertItemsToPublicHealthyForm,
        covertPublicHealthyFormToItems,
        saveInspectReportAsPDFtAsPDF,
    } from '@/views-hospital/inspect-diagnosis/utils';
    import clone from 'utils/clone';
    import { AbcMedicalImagingViewerService } from '@/service/abcMedicalImagingViewer';
    import Logger from 'utils/logger';
    import { formatDate } from '@abc/utils-date';
    import {
        DiagnosisAdvice,
        InspectClinicalReportTable,
        InspectNormalReportTable,
        InspectReportAttachment,
        InspectReportDescription,
        InspectReportFile,
        InspectReportResult,
    } from '@/views-hospital/inspect-diagnosis/components/inspect-report';
    import ExaminationFileUploader from '@/views-hospital/inspect-diagnosis/components/inspect-report/examination-file-uploader.vue';
    import { BusinessTypeEnum } from '@/views/layout/mobile-upload-dialog/config';
    import { accessAppend } from 'assets/configure/access-file';
    import AbcSocket from 'views/common/single-socket';
    import { isEqual } from 'utils/lodash';
    import ExaminationReportRemark from 'views/examination/components/examination-report-remark/index.vue';
    import InspectMultipleReportTab
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/multiple-report-tab/index.vue';
    import { navigateToInspectSetting } from '@/core/navigate-helper';
    import { createGUID } from '@/utils';
    import { mergeItemsValue } from 'views/examination/util';
    import { debounce } from '@abc/utils';

    export default {
        name: 'HospitalInspectDiagnosisMain',

        components: {
            InspectMultipleReportTab,
            ExaminationReportRemark,
            InspectReportAttachment,
            ExaminationFileUploader,
            InspectNormalReportTable,
            InspectClinicalReportTable,
            DiagnosisAdvice,
            InspectReportResult,
            InspectReportFile,
            InspectReportDescription,
            PublicHealthyInspectReport,
            PhysicalExaminationCard,
            InspectReportPerson,
            InspectAssist,
            InspectHeader,
        },

        provide() {
            return {
                $report: this,
            };
        },

        inject: {
            $abcPage: {
                default: {},
            },
        },

        beforeRouteUpdate(to, from, next) {
            this.handleContentUpdateCheck(next);
        },

        beforeRouteLeave(to, from, next) {
            this.handleContentUpdateCheck(next);
        },

        data() {
            return {
                BusinessTypeEnum,
                loading: false,
                dcm4cheeType,
                publicHealthyInitForm: {},
                phItemWithGroupIdList: [],
                curReportIndex: 0,
            };
        },

        computed: {
            ...mapGetters([
                'userInfo',
                'inspect',
                'isEnablePacsUpgrade',
                'clinicBasicConfig',
                'currentClinic',
            ]),

            ...mapGetters('inspect', [
                'needCheck',
            ]),

            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isNeedApplySheet() {
                return this.viewDistributeConfig.Inspect.isNeedApplySheet;
            },

            ...mapGetters('inspect', ['isCanMerge']),

            reportId() {
                return this.$route.params.id;
            },

            postData: {
                get() {
                    return this.$abcPage.$store.state.postData;
                },
                set(v) {
                    this.$abcPage.$store.state.postData = v;
                },
            },

            postDataCache: {
                get() {
                    return this.$abcPage.$store.state.postDataCache;
                },
                set(v) {
                    this.$abcPage.$store.state.postDataCache = v;
                },
            },

            doctorList() {
                return this.$abcPage.$store.state.operateDoctorList;
            },

            selectedPatient() {
                return this.inspect.selectedPatient || {};
            },

            isSendToCenterOrgan() {
                return this.postData.coFlag === EXAM_SHEET_TYPE.sendToCenterOrgan;
            },

            // 检验单是否来自合作门店
            isReceiveFromCoOrgan() {
                return this.postData.coFlag === EXAM_SHEET_TYPE.receiveFromCoOrgan;
            },

            editable() {
                if (this.isSendToCenterOrgan) {
                    return false;
                }

                if (
                    [EXAMINATION_STATUS.WAIT, EXAMINATION_STATUS.WAIT_WRITE].includes(this.postData.status)
                ) {
                    return true;
                }

                if (this.postData.status === EXAMINATION_STATUS.WAIT_CHECK && this.editTag) {
                    return true;
                }

                if (!this.needCheck && this.editTag) {
                    return true;
                }

                return false;
            },

            operateDoctorList() {
                return this.$abcPage.$store.state.operateDoctorList;
            },

            reportIsFinished() {
                return this.postData.status === EXAMINATION_STATUS.CHECKED;
            },

            isPhysicalExamination() {
                return this.postData.businessType === BusinessType.physicalExamination && !this.isReceiveFromCoOrgan;
            },

            employeeList() {
                return this.$abcPage.$store.state.employeeList;
            },

            // 是否为公卫项目
            isPublicHealthyProject() {
                return this.postData.deviceType === INSPECT_TYPE.PUBLIC_HEALTH;
            },

            isGastroscopyReport() {
                return this.postData.deviceType === INSPECT_TYPE.GASTROSCOPE;
            },

            videoDescriptionTitle() {
                if (this.isNormalReport) {
                    return '检查描述';
                }
                switch (this.postData.deviceType) {
                    case INSPECT_TYPE.CDU:
                        return '超声所见';
                    case INSPECT_TYPE.GASTROSCOPE:
                        return '检查所见';
                    default:
                        return '影像表现';
                }
            },

            isRenderMultipleTab() {
                return [
                    INSPECT_TYPE.CDU,
                    INSPECT_TYPE.GASTROSCOPE,
                    INSPECT_TYPE.CT,
                    INSPECT_TYPE.DR,
                    INSPECT_TYPE.MG,
                    INSPECT_TYPE.MR,
                ].includes(this.postData.deviceType) && !this.isPublicHealthyProject;
            },

            isRenderVideoDescription() {
                return [
                    INSPECT_TYPE.CDU,
                    INSPECT_TYPE.GASTROSCOPE,
                    INSPECT_TYPE.CT,
                    INSPECT_TYPE.DR,
                    INSPECT_TYPE.MR,
                    INSPECT_TYPE.MG,
                ].includes(this.postData.deviceType) || this.isNormalReport;
            },

            // 老版本报告
            isNormalReport() {
                return this.postData.subType === INSPECT_REPORT_TYPE.normal || (
                    [INSPECT_TYPE.UN_KNOW, INSPECT_TYPE.OTHER].includes(this.postData.deviceType)
                );
            },

            isRenderImagePart() {
                return [
                    INSPECT_TYPE.CT,
                    INSPECT_TYPE.DR,
                    INSPECT_TYPE.MR,
                    INSPECT_TYPE.MG,
                    INSPECT_TYPE.MDB,
                    INSPECT_TYPE.ECG,
                    INSPECT_TYPE.GASTROSCOPE,
                    INSPECT_TYPE.CDU,
                    INSPECT_TYPE.ASO,
                    INSPECT_TYPE.BODY_COMPOSITION,
                    INSPECT_TYPE.C13_14,
                ].includes(this.postData.deviceType) || this.isNormalReport;
            },

            isRenderClinicalTable() {
                return [
                    INSPECT_TYPE.GYNECOLOGY,
                    INSPECT_TYPE.MOUTH,
                    INSPECT_TYPE.ENT,
                    INSPECT_TYPE.INTERNAL,
                    INSPECT_TYPE.SURGERY,
                    INSPECT_TYPE.EYE,
                ].includes(this.postData.deviceType);
            },

            isRenderNormalTable() {
                return [
                    INSPECT_TYPE.NORMAL,
                ].includes(this.postData.deviceType);
            },

            isReportUpdate() {
                return !isEqual(this.postData, this.postDataCache);
            },

            editTag: {
                get() {
                    return this.$abcPage.$store.state.editTag;
                },
                set(v) {
                    this.$abcPage.$store.setEditTag(v);
                },
            },

            showResult() {
                if (this.isSendToCenterOrgan) {
                    return this.reportIsFinished;
                }

                return true;
            },

            curReportList: {
                get() {
                    return [
                        this.postData.examinationSheetReport,
                        ...(this.postData.additionalExaminationSheetReports || []),
                    ].filter((report) => !report.isDeleted);
                },
            },

            curReport() {
                return this.curReportList[this.curReportIndex] || { imageFiles: [] };
            },

            reportTabs() {
                const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
                const len = this.curReportList.length;
                return this.curReportList.map((report, index) => {
                    const chineseNumber = len === 1 && index === 0 ? '' : chineseNumbers[index];
                    return {
                        label: `报告${chineseNumber}`,
                        canDelete: !report.examinationSheetId,
                    };
                });
            },
        },

        watch: {
            $route: {
                async handler() {
                    await this.handleChangeInspectItem();
                    // 组件会被复用，route 切换需要重置 tab 选中态
                    this.curReportIndex = 0;
                },
            },
        },

        mounted() {
            this.fetchInspectRelateConfig();
            this.handleChangeInspectItem();

            this.$abcEventBus.$on(INSPECT_EVENT_KEY.downloadDeviceCallbackData, () => {
                this.handleComplete();
            }, this);
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.refreshItemsValue, () => {
                this.handleFetchDetail('isSheet');
            }, this);
            // 彩超采集图片
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.addImage, (url) => {
                const IMAGE_LIMIT = 9;
                if (this.curReport.imageFiles.length > IMAGE_LIMIT) {
                    this.$Toast({
                        type: 'warn',
                        message: `最多可选择${IMAGE_LIMIT}张图片`,
                    });
                    return;
                }
                this.curReport.imageFiles.push({
                    url,
                    name: '',
                    fileName: '',
                });
            }, this);
            // 彩超移除图片
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.removeImage, (url) => {
                this.curReport.imageFiles = this.curReport.imageFiles.filter((item) => item.url !== url);
            }, this);
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.COPY_HISTORY_KEY, ({
                key, value,
            }) => {
                this.handleUpdateInspectResult({
                    [key]: value,
                });
            }, this);

            this._socket = AbcSocket.getSocket().socket;
            this._debounceUpdate = debounce(this.handleSheetUpdate, 300, true);
            this._socket.on('examination.sheet.update', this._debounceUpdate);
        },

        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
            this._socket.off('examination.sheet.update', this._debounceUpdate);
        },

        methods: {
            accessAppend,

            // 获取打印设置
            fetchInspectRelateConfig() {
                this.$store.dispatch('fetchPrintAllConfigIfNeed');
                this.$store.dispatch('inspect/fetchInspectSettings');
            },

            // 修改患者信息
            changePatientInfo(patientInfo) {
                this.postData.patient = patientInfo;
                this.$store.dispatch('setSelectedPatient', {
                    type: 'inspect',
                    patientInfo,
                });
                this.$store.dispatch('updatePatientInfo', {
                    type: 'inspect',
                    info: patientInfo,
                });
            },

            // 切换检查项
            handleChangeInspectItem() {
                this.$abcPage.$store.setEditTag(false);
                this.handleFetchDetail();
            },

            // 缓存报告数据
            cachePostData() {
                this.postDataCache = {
                    ...clone(this.postData),
                };
            },

            // 拉取报告数据
            async handleFetchDetail(mode, merge = false) {
                const curReportId = this.reportId;
                this.loading = true;
                try {
                    let { data } = await ExaminationAPI.fetchDetailInterface(
                        this.reportId,
                    );
                    data = data || {};

                    if (curReportId === this.reportId) {
                        const convertedData = inspectDetailAdapter(data);
                        if (merge) {
                            const _postData = clone(this.postData);
                            const oldItemsValue = _postData.deviceType === INSPECT_TYPE.PUBLIC_HEALTH ?
                                covertPublicHealthyFormToItems(
                                    _postData.publicHealthyForm,covertItems(_postData.itemsValue),
                                ) : covertItems(_postData.itemsValue);

                            mergeItemsValue(convertedData.itemsValue, oldItemsValue);
                        }

                        if (mode === 'isSheet') {
                            //只更新itemsValue
                            this.postData.itemsValue = convertedData.itemsValue;
                        } else {
                            this.postData = {
                                ...convertedData,
                                publicHealthyForm,
                            };
                        }

                        if (convertedData.deviceType === INSPECT_TYPE.PUBLIC_HEALTH) {
                            const covertData = covertItemsToPublicHealthyForm(convertedData.itemsValue, publicHealthyForm) || {};
                            this.phItemWithGroupIdList = convertedData.itemsValue.filter((item) => item.itemGroupId);
                            this.postData.publicHealthyForm = covertData;
                            this.publicHealthyInitForm = clone(covertData);
                        }

                        // 处理新开单子，examinationSheetReport 为 null, 手机上传图片报错问题
                        if (!this.postData.examinationSheetReport.id) {
                            this.postData.examinationSheetReport.isLocal = true;
                            this.postData.examinationSheetReport.id = createGUID();
                        }

                        this.setReportDefaultValue();

                        await this.handleDICOMImage(curReportId);
                    }
                } catch (error) {
                    console.log(error);
                } finally {
                    this.loading = false;
                }
            },

            // 处理dicom影像
            async handleDICOMImage(curReportId) {
                if (
                    this.dcm4cheeType.includes(this.postData.deviceType) &&
                    this.postData.status === EXAMINATION_STATUS.WAIT
                ) {
                    const applySheetNo = this.postData?.examinationApplySheetNo;
                    if (applySheetNo) {
                        try {
                            await AbcMedicalImagingViewerService.getInstance().start();
                            let completed = false;
                            if (!this.isEnablePacsUpgrade) {
                                const { completed: _completed } = await AbcMedicalImagingViewerService
                                    .getInstance()
                                    .getBusinessService()
                                    .getInstance()
                                    .checkStudyCompleteByAccessionNumber(applySheetNo); // 申请单id对应的AccessionNumber
                                completed = _completed;
                            }

                            if (completed && curReportId === this.reportId && !this.isEnablePacsUpgrade) {
                                await ExaminationAPI.changeExamSheetStatus(this.reportId, EXAMINATION_STATUS.WAIT_WRITE);
                            }
                        } catch (e) {
                            Logger.error({
                                scene: 'CHECK_STUDY_COMPLETE_ERROR',
                                err: e,
                            });
                        }
                    }
                }
            },

            // 报告默认值设置
            setReportDefaultValue() {
                if (
                    this.postData.status === EXAMINATION_STATUS.WAIT ||
                    this.postData.status === EXAMINATION_STATUS.WAIT_WRITE
                ) {
                    // 设置默认 检查人 审核人 记录医师
                    if (this.doctorList?.find((item) => item.value === this.userInfo.id)) {
                        this.postData.testerId = this.postData.testerId || this.userInfo.id;
                        this.postData.checkerId = this.postData.checkerId || this.userInfo.id;
                        this.postData.examinationSheetReport.recordDoctorId = this.postData.examinationSheetReport.recordDoctorId || this.userInfo.id;
                    }

                    // 检查时间未修改默认当前时间
                    this.postData.testTime = this.postData.testTime || formatDate(new Date(), 'YYYY-MM-DD HH:mm');

                    // 第一次编辑报告，设置默认值
                    if (!this.postData.updateItemsValueFlag) {
                        this.postData.itemsValue = this.postData.itemsValue.map((item) => {
                            item.value = item.defaultValue || '';
                            return item;
                        });
                    }

                    this.cachePostData();
                }
            },

            // 使用报告模板
            handleUpdateInspectResult(templateData) {
                const {
                    videoDescription = '',
                    suggestion = '',
                    diagnosisEntryItems = [],
                    inspectionSite = '',
                } = templateData;

                if (videoDescription) {
                    this.curReport.videoDescription = (this.curReport.videoDescription || '') + videoDescription;
                }

                if (suggestion) {
                    this.curReport.suggestion = (this.curReport.suggestion || '') + suggestion;
                }

                if (inspectionSite) {
                    this.curReport.inspectionSite = (this.curReport.inspectionSite || '') + inspectionSite;
                }

                if (diagnosisEntryItems?.length) {
                    this.curReport.diagnosisEntryItems.push(...diagnosisEntryItems);
                }
            },

            // 下载实验仪器结果
            handleComplete() {
                this.$Toast({
                    message: '下载成功',
                    type: 'success',
                });

                this.handleFetchDetail();
            },

            handleSheetUpdate(val) {
                this.handleOutsourcingUpdate(val);
                this.handleFetchDetail('isSheet', true);
            },
            // 更新中心门店回传结果
            handleOutsourcingUpdate({ examSheetId }) {
                const { id } = this.$route.params;
                if (examSheetId !== id) {
                    return;
                }

                if (!this.isSendToCenterOrgan) return;

                this.handleFetchDetail();
            },

            // 检查报告基础字段表单校验
            handleFormValidate() {
                return new Promise((resolve) => {
                    const headerForm = this.$refs.header?.$refs?.form;
                    const footerForm = this.$refs.footer?.$refs?.form;

                    headerForm.validate((v) => {
                        if (v && footerForm) {
                            footerForm.validate((v2) => {
                                resolve(v2);
                            });
                        } else {
                            resolve(v);
                        }
                    });
                });
            },

            // 公卫表单字段校验
            handlePublicHealthyFormValidate() {
                return new Promise((resolve) => {
                    this.$refs?.publicHealthyForm?.validate((v) => {
                        resolve(v);
                    });
                });
            },

            // 判断是否需要生成pdf
            checkIsNeedCreatePDF(status) {
                // 公卫不生成PDF
                if (this.isPublicHealthyProject) {
                    return false;
                }

                // ------ 非公卫 -----
                // 无审核模式，未修改内容时不能保存，可以保存意味着修改了内容需要生成pdf
                if (!this.needCheck) {
                    return true;
                }
                // 有审核
                const pdfIsInValid = (this.postData.reportInvalidFlag === 1 || !this.postData.reportUrl);
                const isFinishCheck = status === EXAMINATION_STATUS.CHECKED;

                return pdfIsInValid && isFinishCheck;
            },

            // 生成提交数据
            generatePostData(updateStatus, sampleStatus) {
                const format = 'YYYY-MM-DD HH:mm';
                const _postData = clone(this.postData);
                const postData = {
                    ..._postData,
                    status: updateStatus || _postData.status,
                    sampleStatus: _postData.sampleStatus || sampleStatus,
                    checkTime: formatDate(_postData.checkTime, format),
                    reportTime: _postData.deviceType === INSPECT_TYPE.PUBLIC_HEALTH ? undefined : formatDate(_postData.reportTime, format),
                    testTime: formatDate(_postData.testTime, format),
                    type: 2, // 检查
                    itemsValue: _postData.deviceType === INSPECT_TYPE.PUBLIC_HEALTH ?
                        covertPublicHealthyFormToItems(
                            _postData.publicHealthyForm,covertItems(_postData.itemsValue),
                        ) : covertItems(_postData.itemsValue),
                };

                delete postData.publicHealthyForm;

                if (postData.status !== EXAMINATION_STATUS.CHECKED) {
                    delete postData.checkTime;
                }

                return postData;
            },

            // 保存报告
            async handleSaveReport(updateStatus, sampleStatus, isNeedValidate = true) {
                let formValidateResult = false;
                if (!isNeedValidate) {
                    formValidateResult = true;
                } else {
                    if (this.isPublicHealthyProject) {
                        formValidateResult = await this.handlePublicHealthyFormValidate();
                    } else {
                        formValidateResult = await this.handleFormValidate();
                    }
                }

                if (formValidateResult) {
                    const { id } = this.$route.params;

                    // eslint-disable-next-line no-useless-catch
                    try {
                        const postData = this.generatePostData(updateStatus, sampleStatus);
                        // 切换路由不取消请求
                        await ExaminationAPI.updateExam(id, postData, true);
                        await this.handleFetchDetail();
                        if (this.checkIsNeedCreatePDF(this.postData.status)) {
                            await saveInspectReportAsPDFtAsPDF(id, {
                                ...this.postData,
                                organPrintView: this.clinicBasicConfig,
                            });
                        }
                    } catch (error) {
                        throw error;
                    }
                } else {
                    const el = document.querySelector('#abc-container-center');
                    el.scrollTo({
                        top: 10000,
                        behavior: 'smooth',
                    });
                    throw new Error('表单校验失败');
                }
            },

            // 路由切换前，提示保存更改
            handleContentUpdateCheck(next) {
                if (this._confirmModal) {
                    return;
                }
                const isDraftChange = (
                    (
                        this.postData.status === EXAMINATION_STATUS.WAIT ||
                        this.postData.status === EXAMINATION_STATUS.WAIT_WRITE
                    ) && (
                        this.isReportUpdate
                    )
                );

                const isReportChange = (
                    this.editTag &&
                    this.isReportUpdate
                );
                if (isDraftChange || isReportChange) {
                    this._confirmModal = this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '你的修改内容还未保存，确定离开？',
                        closeAfterConfirm: false,
                        onConfirm: (modelVm) => {
                            modelVm.close();
                            next();
                        },
                        onCancel: (modelVm) => {
                            modelVm.close();
                        },
                        onClose: () => {
                            this._confirmModal = null;
                        },
                    });
                } else {
                    next();
                }
            },

            handleAddImage(files) {
                this.curReport.imageFiles.push(...files);
            },

            handleDeleteImage(idx) {
                this.curReport.imageFiles.splice(idx, 1);
                this.$abcEventBus.$emit(INSPECT_EVENT_KEY.resetCollectedImageStatus);
            },

            handleAddReport() {
                if (!this.isCanMerge) {
                    let modal = this.$alert({
                        type: 'warn',
                        title: '无法创建多份报告',
                        content: () => {
                            // eslint-disable-next-line no-unused-vars
                            const h = this.$createElement;

                            const handleToInspectSetting = () => {
                                navigateToInspectSetting(this.currentClinic);
                                modal.close();
                                modal = null;
                            };
                            return (
                                <abc-space>
                                    <span>请将检查设置，项目合并规则设置成合并模式</span>

                                    <abc-link onClick={handleToInspectSetting}>去设置</abc-link>
                                </abc-space>
                            );
                        },
                    });
                    return;
                }
                if (!this.postData.additionalExaminationSheetReports) {
                    this.$set(this.postData, 'additionalExaminationSheetReports', []);
                }

                const reportModel = {
                    ...this.postData.examinationSheetReport,
                    'id': `${createGUID()}`,
                    'imageFiles': [],
                    'method': '',
                    'videoDescription': '',
                    'resultInfo': '',
                    'advice': '',
                    'suggestion': '',
                    'diagnosisFlag': '',
                    'inspectionSite': '',
                    'diagnosisEntryItems': [],
                    deviceModelDesc: '',
                    isLocal: true,
                };

                delete reportModel.examinationSheetId;
                delete reportModel.mergeSheetId;

                this.postData.additionalExaminationSheetReports.push(reportModel);
                this.handleChangeReport(this.curReportList.length - 1);
            },

            checkReportIsEmpty(report) {
                const stringKeys = [
                    'method',
                    'videoDescription',
                    'resultInfo',
                    'advice',
                    'suggestion',
                    'diagnosisFlag',
                    'inspectionSite',
                ];

                const arrayKeys = [
                    'imageFiles',
                    'diagnosisEntryItems',
                ];

                return stringKeys.every((key) => !report[key]) &&
                    arrayKeys.every((key) => !report[key] || report[key].length === 0);
            },

            handleRemoveReport(idx) {
                const confirm = () => {
                    const delReport = this.curReportList[idx];
                    if (delReport.id) {
                        this.$set(delReport, 'isDeleted', 1);
                    } else {
                        this.postData.additionalExaminationSheetReports.splice(idx - 1, 1);
                    }
                    if (idx <= this.curReportIndex) {
                        this.handleChangeReport(this.curReportIndex - 1);
                    }
                };
                if (!this.checkReportIsEmpty(this.curReportList[idx])) {
                    this.$confirm({
                        type: 'warn',
                        title: '删除提示',
                        content: '报告已编辑，确认需要删除？',
                        onConfirm: confirm,
                    });
                } else {
                    confirm();
                }
            },
            handleChangeReport(idx) {
                this.curReportIndex = idx;
                this.$abcEventBus.$emit(INSPECT_EVENT_KEY.resetCollectedImageStatus);
            },
        },
    };
</script>

<style lang="scss">
.inspect-main-wrapper {
    position: relative;
    min-height: 100%;
    overflow-x: auto;

    .report-content__attachments-add-wrapper {
        display: flex;
        min-height: 60px;
        margin-top: 16px;
        border: 1px var(--abc-color-P6) solid;
        border-radius: var(--abc-border-radius-small);

        .report-content__attachments-add-title {
            flex-shrink: 0;
            align-content: stretch;
            width: 138px;
            padding-top: 20px;
            font-weight: bold;
            text-align: center;
            border-right: 1px var(--abc-color-P6) solid;
        }

        .report-content__attachments-add-uploader {
            flex: 1;
            overflow: hidden;
        }
    }
}
</style>


