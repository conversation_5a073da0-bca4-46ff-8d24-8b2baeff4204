<template>
    <abc-container has-left-container class="abc-hospital-nurse-settings-container">
        <abc-container-left>
            <div class="hospital-settings-list">
                <span class="hospital-settings-item">
                    <abc-icon icon="menu" size="14px" class="icon"></abc-icon>
                    <span class="text">病区成员管理</span>
                </span>
            </div>
        </abc-container-left>

        <abc-container-center class="content-container">
            <router-view ref="content"></router-view>
        </abc-container-center>
    </abc-container>
</template>

<script>
    import * as core from '@/views-hospital/nurse-settings/core/index.js';
    import { createAbcPage } from '@/core/page/factory.js';

    export default {
        name: 'HospitalSettings',
        mixins: [
            createAbcPage(core),
        ],
        data() {
            return {

            };
        },
    };
</script>

<style lang="scss">
    @import './_index.scss';
</style>


