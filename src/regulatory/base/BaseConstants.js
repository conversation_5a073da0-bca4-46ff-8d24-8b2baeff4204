import {
    DAY_TIMES,
    DATE_FORMATE,
    DATE_TIME_FORMATE,
} from 'assets/configure/constants.js';

import { isProd } from '@/assets/configure/build-env';

export default class BaseConstants {
    constructor(AbcRegulatory) {
        this.AbcRegulatory = AbcRegulatory;
    }
    REGULATORY_AUTO_REPORT_TIME = '_regulatory_auto_report_time_';

    dateConst = Object.freeze({
        DATE_FORMATE, // 时间模板（1970-01-01）
        DATE_TIME_FORMATE,
        DATE_FORMATE_8: 'YYYYMMDD',
        DATETIME_FORMATE_14: 'YYYYMMDDHHmmss',
    });
    DAY_TIMES = DAY_TIMES; // 以天单位时间戳

    isProd = isProd; // 统一正式环境

    supportReportTypeConst = Object.freeze({
        backendReport: 1,
        frontReport: 2,
    });

    reportTypeConst = Object.freeze({
        REPORT_DOCTOR: 0, // 医生信息
        REPORT_DEPARTMENT: 1, // 科室信息
        REPORT_TREATMENT: 2, // 检验检查治疗理疗项目信息
        REPORT_MATERIAL: 3, // 材料固定资产信息
        REPORT_DIAGNOSIS_DICT: 4, // 疾病目录信息
        REPORT_MEDICINE_DICT: 5, // 药品目录信息
        REPORT_OUTPATIENT: 6, // 门诊相关，患者
        REPORT_HOSPITAL_BASIC: 7, // 诊所信息
        REPORT_SUPPLIER: 8, // 供应商信息
        REPORT_EQUIPMENT: 9, // 设备信息
        REPORT_STOCK_IN: 10, // 入库
        REPORT_STOCK_OUT: 11, // 出库
        //----------------customer---------------
        UPLOAD_DRUG_PURCHASE: 99,
        UPLOAD_DRUG_STOCK: 98,
    });

    goodsTypeId = Object.freeze({
        medicine: 1, // 药品
        material: 2, // 物资
        examination: 3, // 检查检验
        treatmentAndPhysiotherapy: 4, // 治疗理疗
        registration: 5, // 挂号
        memberCard: 6, // 会员卡
        goods: 7, // 商品
        package: 8, // 套餐
        consultFee: 9, // 在线问诊
        deliveryFee: 10, // 快递费
        decoctionFee: 11, // 煎药费
        medicineWest: 12, // 西药
        medicineChinese: 13, // 中药
        medicineChinesePiece: 14, // 中药饮片
        medicineChineseGranule: 15, // 中药颗粒
        medicineChinesePatent: 16, //中成药
        materialMedical: 17, // 医疗器械
        materialLogistics: 18, // 后勤材料
        materialFixedAssets: 19, // 固定资产
        examine: 20, // 检验
        test: 21, // 检查
        physiotherapy: 22, // 治疗
        treatment: 23, // 理疗
        other: 24, // 其他
        goodsHomemade: 25, // 自制成品
        healthMedicine: 26, // 保健药品
        healthGood: 27, // 保健食品
        otherGood: 28, // 其他商品
        otherGoods49: 33, // 其他费用
        nurseProduct: 56, // 护理项目
        eyeGlass: 64, //眼镜片
        mirrorFrame: 65, //镜架
        orthokeratoscope: 66, //角膜塑形镜
        softHydrophilicMirror: 67, //软性亲水镜
        rigidOxygenPermeator: 68, //硬性透氧镜
        sunglasses: 69, //太阳镜
    });

    chargeSourceFormType = Object.freeze ({
        registration: 1, // 挂号
        examination: 2, // 检查检验
        treatment: 3, // 治疗理疗
        westernPrescription: 4, // 成药处方
        infusionPrescription: 5, // 输液处方
        chinesePrescription: 6, // 中药处方
        additional: 7, // 附加收费项目
        goods: 8, // 商品
        material: 9, // 医疗器械
        gift: 10, // 赠品
        package: 11, // 套餐
        consultation: 12, //咨询
        delivery: 13, // 快递
        decoction: 14, // 代煎
        airPharmacy: 15, // 空中药房处方
        externalPrescription: 16, // 外治处方
        familyDoctor: 17, // 家庭医生
        promotionCardOpen: 18, // 营销卡项开卡
        promotionCardRecharge: 19, // 营销卡项充值
        memberCardRecharge: 20, // 营销卡项充值
        otherFee: 21, // 其他费用
        glasses: 22, // 眼镜
        nurseProductFee: 23, // 护理项目费用
    });
}
