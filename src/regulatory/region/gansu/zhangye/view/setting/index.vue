<script>
    import BaseSettingIndex from '@/regulatory/base/view/setting-new/index.vue';
    import CommonFormPresenter from '@/regulatory/base/components/common-form/CommonFormPresenter';
    export default {
        name: 'ZhangyeSettingView',
        extends: BaseSettingIndex,
        methods: {
            /**
             * 创建医保服务开通formitem的配置
             * <AUTHOR>
             * @date 2021-10-28
             * @returns {Array}
             */
            createFormItemConfigList() {
                const { formItemTypeConst } = CommonFormPresenter;

                const formItemConfigList = [
                    {
                        value: 'orgCode',
                        name: '机构编号',
                        type: formItemTypeConst.INPUT,
                        required: true,
                    },
                    ...this.createReportEnvironmentConfigItem(),
                ];
                return formItemConfigList;
            },
        },
    };
</script>
