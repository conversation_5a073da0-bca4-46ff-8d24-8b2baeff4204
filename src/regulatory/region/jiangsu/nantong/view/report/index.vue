<script>
    import BaseReportView from '@/regulatory/base/view/report/index.vue';
    export default {
        name: 'NantongReportView',
        extends: BaseReportView,
        data() {
            return {
                isNeedAbcDatePicker: false, // 是否展示日期选择
                isNeedQuickReportBtn: true, // 是否展示一键上报
                isNeedElectron: true,//需要在客户端环境上报
            };
        },
        computed: {
            // 获取表头
            tableHeader () {
                return this.createNantongTableHeader();
            },
            // 列表展示数据
            reportDataList() {
                let reportDataList = [
                    {
                        businessName: '门诊挂号',
                        primaryKey: 'GHLSH',
                        businessType: 'registerDatas',
                        type: '0', 
                    },
                    {
                        businessName: '门诊就诊记录',
                        primaryKey: 'JZLSH',
                        businessType: 'recordDatas',
                        type: '1',
                    },
                    {
                        businessName: '门诊处方记录',
                        primaryKey: 'CFID',
                        businessType: 'recipeDatas',
                        type: '2',
                    },
                    {
                        businessName: '门诊收费记录',
                        primaryKey: 'FYID',
                        businessType: 'feeDatas',
                        type: '3',
                    },
                    {
                        businessName: '预约挂号',
                        primaryKey: 'YYLSH',
                        businessType: 'appointmentDatas',
                        type: '4',
                    },
                    {
                        businessName: '患者基本信息',
                        primaryKey: 'PERSONAL_ID',
                        businessType: 'patientDatas',
                        type: '5',
                    },
                    {
                        businessName: '挂号数据',
                        primaryKey: 'REGISTOR_ID',
                        businessType: 'patientRecordDatas',
                        type: '6',
                    },
                    {
                        businessName: '就诊记录',
                        primaryKey: 'MED_REC_ID',
                        businessType: 'visitRecordDatas',
                        type: '7',
                    },
                    {
                        businessName: '西药处方(主)',
                        primaryKey: 'RX_ID',
                        businessType: 'wmPrescriptionDatas',
                        type: '8',
                    },
                    {
                        businessName: '西药处方(从)',
                        primaryKey: 'DRUG_ID',
                        businessType: 'wmMedicineDatas',
                        type: '9',
                    },
                    {
                        businessName: '中药药处方(主)',
                        primaryKey: 'RX_ID',
                        businessType: 'cmPrescriptionDatas',
                        type: '10',
                    },
                    {
                        businessName: '中药处方(从)',
                        primaryKey: 'DRUG_ID',
                        businessType: 'cmMedicineDatas',
                        type: '11',
                    },
                    {
                        businessName: '患者信息',
                        primaryKey: 'PERSONAL_ID',
                        businessType: 'ptInformationDatas',
                        type: '12',
                    },
                    {
                        businessName: '门诊处方明细',
                        primaryKey: '',
                        businessType: 'recipeDetailDatas',
                        type: '13',
                    },
                ];
                reportDataList = reportDataList.map((item) => {
                    item.uploadTime = this.fecha.format(this.originData?.[0]?.uploadTime, this.dateConst.DATE_TIME_FORMATE);
                    return item;
                });
                return reportDataList;
            },
        },
        methods: {
            /**
             * @desc 创建表头
             * <AUTHOR>
             * @date 2023-04-27
             */
            createNantongTableHeader() {
                const tableHeader = this.createTableHeader();
                return tableHeader.filter((item) => {
                    return item.prop === 'businessName' ||
                        item.prop === 'uploadTime';
                });
            },
            /**
             * @desc 当点击一键上报时
             * <AUTHOR>
             * @date 2023-02-24
             */
            async onClickQuickReport() {
                this.quickBtnLoading = true;
                const connectResponse = await this.$abcRegulatory.api.connectOracleDatabase();
                if (connectResponse.status === false) {
                    this.$Toast.error(connectResponse.message);
                    this.quickBtnLoading = false;
                    return false;
                }
                const awaitReportDataResponse = await this.$abcRegulatory.api.getAwaitReportData();
                if (awaitReportDataResponse.status === false) {
                    return awaitReportDataResponse;
                }
                const { data } = awaitReportDataResponse.data;
                for (let index = 0; index < this.reportDataList.length; index++) {
                    const awaitData = data[this.reportDataList[index].businessType];
                    if (awaitData && Array.isArray(awaitData) && awaitData.length !== 0) {
                        const response = await this.$abcRegulatory.api.postOracleSingularInsertInto(awaitData);
                        if (response.status === false) {
                            this.$Toast.error(response.message);
                            this.quickBtnLoading = false;
                            return false;
                        }
                    }
                }
                const reportParams = {
                    type: '0',
                    uploadTime: this.fecha.format(new Date(), this.dateConst.DATE_TIME_FORMATE),
                };
                await this.$abcRegulatory.api.postReportResult(reportParams);
                this.quickBtnLoading = false;
                this.$Toast.success('数据上报成功');
                this.fetchReportList();
            },
        },
    };
</script>