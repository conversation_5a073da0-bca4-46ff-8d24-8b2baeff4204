<template>
    <abc-dialog
        title="附件上传"
        :value="true"
        append-to-body
        class="setting-module__wisdom-city__supplier_dialog-attachment"
        content-styles="width: 580px; height: 280px; padding: 22px;"
        @input="(val) => $emit('input', val)"
    >
        <section>
            <div class="attachment-left">
                <div class="select-img" :style="{ 'background-image': `url(${postData.certUrl})` }">
                    <input
                        v-if="showUploadNode"
                        type="file"
                        :title="''"
                        accept="image/*"
                        @change="onChangeUploadFile"
                    />
                    <div
                        v-if="postData.certUrl || postData.certFile"
                        class="close-box"
                        @click="
                            postData.certUrl = '';
                            postData.certFile = '';
                        "
                    >
                        <span class="iconfont cis-icon-crosspx"></span>
                    </div>
                    <span v-else class="iconfont cis-icon-add_hover"></span>
                </div>
                <p>大小限制：{{ MAX_IMAGE_SIZE_WRITE }}</p>
                <p>支持格式：{{ ACCEPT_IMAGE_TYPE.join('、') }}</p>
            </div>
            <abc-form
                ref="postData"
                label-position="left"
                :label-width="100"
                item-block
            >
                <abc-form-item label="证件类型" required>
                    <abc-select v-model="postData.certType" :width="180">
                        <abc-option
                            v-for="item in supplierFileOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
                <abc-form-item label="证件编号" required>
                    <abc-input v-model="postData.certNo" :width="180" :max-length="30"></abc-input>
                </abc-form-item>
                <abc-form-item label="证件名称" required>
                    <abc-input v-model="postData.certName" :width="180" :max-length="30"></abc-input>
                </abc-form-item>
                <abc-form-item label="有效期">
                    <abc-date-picker
                        v-model="postData.certExpiry"
                        :picker-options="{
                            disabledDate: disabledFutureDate,
                            yearRange: { end: '2099' }
                        }"
                        value-format="YYYY-MM-DD"
                        :width="180"
                    ></abc-date-picker>
                </abc-form-item>
            </abc-form>
        </section>
        <div slot="footer" class="dialog-footer">
            <abc-button
                style=" width: 92px; min-width: 92px;"
                :disabled="disabledBtn"
                :loading="loadingUpload"
                @click="onClickUploadAttachment"
            >
                确认上传
            </abc-button>
            <abc-button style=" width: 92px; min-width: 92px;" type="blank" @click="$emit('input', false)">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import MixinUpload from 'views/crm/common/mixin-upload';
    import MixinAttachment from '../common/mixin-attachment';
    export default {
        name: 'DialogAttachment',
        mixins: [MixinUpload, MixinAttachment],
        props: {
            // 机构编码
            medicalOrgCode: {
                type: String,
                required: true,
            },
            // 缓存的附件信息
            cacheCertPostData: {
                type: Object,
                default: null,
                required: false,
            },
            objectType: {
                type: String,
                required: true,
            },
            objectId: {
                type: String,
                required: true,
            },
        },
        data() {
            const {
                ACCEPT_IMAGE_TYPE, MAX_IMAGE_SIZE_WRITE, 
            } = this.$abcRegulatory.constants;
            return {
                supplierFileOptions: this.$abcRegulatory.options.supplierFileOptions,
                ACCEPT_IMAGE_TYPE,
                MAX_IMAGE_SIZE_WRITE,

                showUploadNode: true,
                loadingUpload: false,
                postData: null,
            };
        },
        computed: {
            // 是否有更新
            isUpdated() {
                return !this.$abcRegulatory.tools.isEqual(this.postData, this.createPostData());
            },
            disabledBtn() {
                const {
                    certFile, certUrl, 
                } = this.postData || {};
                return this.loadingUpload || !(certFile || certUrl) || !this.isUpdated;
            },
        },
        created() {
            this.postData = this.createPostData();
        },
        methods: {
            /**
             * 创建数据提交结构
             * <AUTHOR>
             * @date 2020-07-24
             * @returns {Object}
             */
            createPostData() {
                const postData = {
                    certFile: '', // 文件
                    certUrl: '', // 图片
                    certType: this.supplierFileOptions[0].value, // 证书类型 √
                    certNo: '', // 证书编码 √
                    certName: '', // 证书名称 √
                    certExpiry: '', // 有效期
                };
                if (this.cacheCertPostData) {
                    Object.assign(postData, this.cacheCertPostData);
                    const {
                        certExpiry, // 有效期
                    } = this.cacheCertPostData;
                    if (certExpiry) {
                        const {
                            tools,
                            constants: {
                                dateConst,
                            },
                        } = this.$abcRegulatory;
                        postData.certExpiry = tools.fecha.format(new Date(certExpiry) , dateConst.DATE_FORMATE);
                    }
                }
                return postData;
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .setting-module__wisdom-city__supplier_dialog-attachment {
        section {
            width: 100%;
            height: 100%;

            @include flex(row, center, flex-start);

            .attachment-left {
                .select-img {
                    position: relative;
                    flex-shrink: 0;
                    width: 160px;
                    height: 160px;
                    margin-right: 42px;
                    margin-bottom: 12px;
                    background-repeat: no-repeat;
                    background-position: center center;
                    background-size: contain;
                    border: 1px dashed $P6;

                    @include flex(row, center, center);

                    &:hover {
                        background-color: rgba(0, 0, 0, 0.04);
                    }

                    > input {
                        position: absolute;
                        top: 0;
                        left: 0;
                        z-index: 2;
                        width: 100%;
                        height: 100%;
                        cursor: pointer;
                        opacity: 0;
                    }

                    .close-box {
                        position: absolute;
                        top: -10px;
                        right: -10px;
                        z-index: 3;
                        width: 22px;
                        height: 22px;
                        cursor: pointer;
                        background-color: $P5;
                        border-radius: 10px;

                        @include flex(row, center, center);

                        &:hover .iconfont {
                            color: $T3;
                        }

                        .iconfont {
                            font-size: 12px;
                            color: $P1;
                        }
                    }

                    > .iconfont {
                        font-size: 18px;
                        color: $P1;
                    }
                }

                > p {
                    font-size: 12px;
                    line-height: 1.6;
                    color: $T3;
                }
            }
        }
    }
</style>
