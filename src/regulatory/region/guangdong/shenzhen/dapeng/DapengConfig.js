/*
 * <AUTHOR>
 * @DateTime 2021-11-26 14:19:25
 */
import GuangdongConfig from '../../GuangdongConfig';
import DapengApi from './DapengApi';

const DapengReport = () => import(/* webpackChunkName: "national-dapeng" */ './view/report/index.vue');

export default class DapengConfig extends GuangdongConfig {
    Report = DapengReport;
    api = new DapengApi(this);
    isSupportRegulatory = false; // 是否支持监管，默认不支持

    /**
     * 初始化处理
     * <AUTHOR>
     * @date 2022-12-29
     */
    async init() {
        const isSupportRegulatoryResponse = await this.api.fetchIsSupportRegulatory();
        if (isSupportRegulatoryResponse.status === true) {
            this.isSupportRegulatory = isSupportRegulatoryResponse.data?.data?.enable;
        }
    }
    /**
     * 检查是否支持监管
     * <AUTHOR>
     * @date 2022-12-29
     * @returns {Boolean}
     */
    checkIsSupportRegulatory() {
        return this.isSupportRegulatory;
    }
    /**
     * @desc 创建路由
     * <AUTHOR>
     * @date 2022-11-08
     * @return {Object}
     */
    createRoute() {
        const route = super.createRoute();
        route.redirect = {
            name: '@regulatory/report',
        };
        return route;
    }
    /**
     * 创建子路由
     * <AUTHOR>
     * @date 2022-12-29
     * @returns {Array}
     */
    createChildrenRoutes() {
        const reportRouter = super.createChildrenRoutes().find((item) => item.path === 'report');
        const children = [
            reportRouter,
        ];
        return children;
    }
}
