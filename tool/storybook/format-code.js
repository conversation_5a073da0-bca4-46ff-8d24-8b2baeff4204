import jsBeautify from 'js-beautify';

const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generator = require('@babel/generator').default;

export function formatCode(code) {
    const sourceCode = `const obj = ${code}`;
    const ast = parser.parse(sourceCode, {
        sourceType: 'module',
        plugins: ['jsx'],
    });

    // 遍历 AST 提取需要的部分
    const extracted = {
        setup: null,
        data: null,
        computed: null,
        watch: null,
        created: null,
        mounted: null,
        beforeDestroy: null,
        methods: null,
        template: null,
    };

    traverse(ast, {
        ObjectExpression (path) {
            const { properties } = path.node;

            Object.keys(extracted).forEach((propName) => {
                const node = properties.find((prop) => prop.key?.name === propName);
                if(node && !extracted[propName]) {
                    extracted[propName] = generator(node).code;
                }
            });
        },
    });

    const renderTemplate = extracted.template ? extracted.template.slice(11, -2) : '';
    delete extracted.template;
    const renderScript = Object.values(extracted).filter((item) => !!item).join(',');

    const html = renderTemplate ? jsBeautify.html(`
        <template>${renderTemplate}</template>
    `, {
        indent_size: 4,
        preserve_newlines: true,
        max_preserve_newlines: 2,
        wrap_attributes: 'force-expand-multiline',
    }) : '';

    const js = renderScript ? jsBeautify.js(`
        export default {
            ${renderScript}
        }
    `, {
        indent_size: 4,
        preserve_newlines: true,
        max_preserve_newlines: 2,
    }) : '';

    return `
${html}

${js}
    `;
}
